.box {
  position: relative;
  background: url(../img/new-tureplate-bg-20221209.jpg) no-repeat;
  background-size: 100% auto;
  /* min-height: 100%; */
  background-color: #f5341d;
  width: 100%;
  overflow-x: hidden;
}
.box .blank {
  height: 2.1rem;
}

.box .truntable-title {
  position: absolute;
  top: 0.58rem;
  left: 50%;
  transform: translateX(-50%);
  color: #fff;
  font-size: 0.25rem;
  font-style: italic;
  font-weight: bold;
}

.box .truntable-title span {
  color: #fcec71;
}

.box .truntable-sub-title {
  font-size: 0.3rem;
  color: #fffedb;
  font-weight: bolder;
  position: absolute;
  top: 1rem;
  width: 100%;
  text-align: center;
  text-shadow: 1px 1px 1px #fad49e;
}

.inner {
  width: 100%;
  margin: auto;
}
.box .startTime {
  width: 2.35rem;
  height: .35rem;
  background: #f5341d;
  border: .01rem solid #ea3b22;
  border-radius: .2rem;
  margin: auto;
  font-size: .13rem;
  line-height: .35rem;
  color: #fff;
  letter-spacing: .02rem;
  /* background: url(../img/stratTime-1229.png) no-repeat;
  background-size: 100% auto; */
}
/* .box .startTime:after {
  content: "";
  display: inline-block;
  width: 2.8rem;
  height: 0.04rem;
  background: linear-gradient(
    270deg,
    rgba(205, 50, 127, 1) 0,
    rgba(186, 36, 97, 1) 51%,
    rgba(228, 54, 104, 1) 100%
  );
  opacity: 0.6;
  filter: blur(0.01rem);
  position: absolute;
  left: 0;
  bottom: 0;
} */
.turntable_bg,
.truntable_content {
  height: 3.74rem;
  position: relative;
}

.truntable_content {
  height: 3.74rem;
  width: 3.74rem;
  margin: auto;
  margin-top: -0.12rem;
}

.new_turntable_bg {
  width: 3.74rem;
  height: 3.74rem;
  position: absolute;
  top: 0;
  left: 50%;
  margin-left: -1.87rem;
  overflow: hidden;
}

.turntable_box {
  margin: 0.218rem auto 0;
  position: relative;
  width: 3.31rem;
  height: 3.31rem;
}
.pointer,
.pointer em,
.pointer img,
.shadow,
.turntable_bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}
.turntable_bg {
  z-index: 4;
  width: 3.74rem;
  height: 3.74rem;
}
.pointer {
  width: 0.8rem;
  height: 0.92rem;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 8;
  margin-top: -.03rem;
}
.pointer img {
  width: 100%;
  left: 0;
  top: 0;
}
.pointer em {
  display: inline-block;
  text-align: center;
  width: 50%;
  z-index: 7;
  font-style: normal;
  top: 0.3rem;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.17rem;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  color: rgba(255, 45, 13, 1);
  line-height: 0.2rem;
  text-shadow: 0 1px 1px rgba(81, 4, 1, 0.1), 0 1px 3px rgba(255, 235, 139, 0.5);
  background: linear-gradient(
    180deg,
    rgba(255, 93, 7, 1) 0,
    rgba(255, 22, 22, 1) 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.shadow {
  width: 2.92rem;
  z-index: 3;
  top: 4.68rem;
  left: 0.29rem;
}
.turntable_box .item {
  width: 0.95rem;
  height: 1.28rem;
  line-height: 0.15rem;
  font-size: 0.11rem;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  -ms-flex-align: center;
  align-items: center;
  position: absolute;
  top: 0.38rem;
  left: 1.18rem;
  transform-origin: 50% 100%;
  font-family: PingFangSC-Medium;
  z-index: 6;
}
.turntable_box .item div {
  margin-top: 0.08rem;
  width: 0.85rem;
  text-align: center;
}
.turntable_box .item img {
  width: 0.5rem;
  height: 0.5rem;
  margin-top: 0.06rem;
}
.share-box {
  height: 0.5rem;
  margin-top: 0.19rem;
  background: url(../img/big_share_btn.png) no-repeat;
  background-size: 100% 100%;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: distribute;
  justify-content: space-around;
  -ms-flex-align: center;
  align-items: center;
}
.share-box section {
  width: 2.28rem;
  /* padding-left: 0.15rem; */
}
.share-box section header {
  font-size: 0.15rem;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  color: rgba(255, 255, 255, 1);
  line-height: 0.21rem;
}
.share-box section small {
  font-size: 0.12rem;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  color: rgba(255, 255, 255, 1);
  line-height: 0.16rem;
}
.share-box button {
  width: 0.59rem;
  height: 0.3rem;
  background: url(../img/share_btn.png) no-repeat;
  background-size: 100%;
  font-size: 0.14rem;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  color: rgba(157, 90, 14, 1);
  line-height: 0.2rem;
  border: 0;
  margin-right: 0.05rem;
  outline: 0;
}

.main-box .price-box {
  /* background: url(../img/price_bg.png) no-repeat;
  background-size: 100%; */
  width: 3.25rem;
  height: 2.16rem;
  margin: auto;
  margin-top: .1rem;
  background: #ffffff;
  border-radius: .05rem;
  box-shadow: 0px .02rem .1rem 0px rgba(34,2,71,0.22);
  padding: .15rem .15rem 0 .15rem;
  box-sizing: border-box;
}

.main-box {
  padding-bottom: 0.15rem;
  display: none;
}

.main-box .container {
  /* height: 1.5rem; */
  overflow: hidden;
  border-radius: 10px;
  height: 100%;
}
.container a,
.pop button {
  bottom: 0.15rem;
  /* background: #fdc64a;
  box-shadow: 0 3px 7px 0 rgba(123, 64, 12, 0.2); */
  background: url(../img/popBtn_bg.png) no-repeat;
  background-size: 100% 100%;
}
.container {
  /* padding: 0.08rem 0.15rem; */
  /* padding-top: 0.08rem;
  padding-left: 0.15rem;
  padding-bottom: 0.15rem; */
  position: relative;
}
.container a,
.pop {
  position: absolute;
  right: 0;
}
.container .wraper {
  height: 1.29rem;
  overflow: hidden;
}
.container .myRecord {
  width: 2.95rem;
  height: .4rem;
  line-height: .4rem;
  text-align: center;
  color: #9d5a0e;
  font-size: .14rem;
  margin: auto;
  margin-top: .15rem;
  background: linear-gradient(180deg,#ffe5b7, #ffae68 144%);
  border-radius: .2rem;
  box-shadow: 0px 0.015rem 0.035rem 0px rgba(123,64,12,0.2);
  position: static;
  font-weight: 500;
}
.container li {
  height: 0.21rem;
  font-size: 0.14rem;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  color: #905212;
  line-height: 0.21rem;
  text-align: left;
}

/* .container li:nth-of-type(odd) {
  background: #ffeaa2;
}

.container li:nth-of-type(even) {
  background: #fef3c5;
} */

.container a,
.main-box article .info h3 {
  font-family: PingFangSC-Medium;
  font-weight: 500;
}
.container a {
  display: inline-block;
  width: 1.8rem;
  height: 0.3rem;
  font-size: 0.14rem;
  color: #fff;
  line-height: 0.3rem;
  left: 0;
  margin: auto;
}
.cover,
.pop {
  left: 0;
  display: none;
}
.main-box article {
  margin-top: 0.25rem;
}
.main-box article .info h3 {
  height: 0.18rem;
  font-size: 0.12rem;
  color: rgba(251, 221, 78, 1);
  line-height: 0.18rem;
}
.main-box article .info p {
  font-size: 0.12rem;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  color: rgba(255, 255, 255, 1);
  line-height: 0.18rem;
  padding: 0.15rem;
  border: 1px solid #a989dc;
  border-radius: 0.05rem;
}
.pop button,
.pop section header,
.pop section p {
  font-size: 0.14rem;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  line-height: 0.2rem;
}
.pop {
  top: 1.12rem;
  height: 3.6rem;
  z-index: 10;
  width: 2.52rem;
  margin-left: auto;
  margin-right: auto;
}
.pop .no-prize {
  position: relative;
  height: 100%;
}
.pop button {
  position: absolute;
  left: 0;
  right: 0;
  margin: 0 auto;
  width: 1.9rem;
  height: 0.4rem;
  border: 0;
  outline: 0;
  color: rgba(157, 90, 14, 1);
}
.pop section {
  position: relative;
  background: url(../img/prize.png) no-repeat;
  background-size: 100%;
  height: 3.5rem;
  padding-top: 1.2rem;
}
.pop section header,
.pop section p {
  height: 0.2rem;
  color: rgba(255, 255, 255, 1);
}
.pop section img {
  width: 1.25rem;
  height: 1.25rem;
}
.cover {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9;
}
.no-count {
  color: #999 !important;
  text-shadow: none !important;
  background: 0 0 !important;
  -webkit-text-fill-color: #999 !important;
}
.count-num,
.notice {
  display: inline-block;
  position: absolute;
  font-family: PingFangSC-Regular;
  color: rgba(144, 82, 18, 1);
  text-align: center;
}
.count-num {
  width: 0.39rem;
  height: 0.28rem;
  min-width: 25px;
  min-height: 20px;
  right: -0.1rem;
  top: 0.1rem;
  line-height: 0.28rem;
  font-size: 0.12rem;
  background: url(../img/count_bg.png) no-repeat;
  background-size: 100%;
  font-weight: 600;
}
.notice {
  width: 1.84rem;
  height: 0.29rem;
  line-height: 0.27rem;
  overflow: hidden;
  left: -0.52rem;
  top: -.02rem;
  font-size: 0.12rem;
  font-weight: 400;
  background: url(../img/notice_bg.png) no-repeat;
  background-size: 100% 100%;
}

.box .activityRule {
  position: absolute;
  top: .08rem;
  right: 0;
  background: url(../img/activityRule_bg.png) no-repeat;
  background-size: 100% 100%;
  font-size: .12rem;
  line-height: .12rem;
  color: #9d5a0e;
  font-weight: 500;
  padding: .06rem .08rem;
}

.box .getmore {
  background: url(../img/getMoreBtn.gif) no-repeat;
  background-size: 100% 100%;
  width: 3.25rem;
  height: .45rem;
  margin: 0 auto;
  line-height: .45rem;
  font-size: .17rem;
  text-align: center;
  color: #fff;
  font-weight: 500;
  margin-bottom: .15rem;;
}
.box .pop_black_bg {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0,0,0,0.5);
  z-index: 90;
  display: none;
}
.box .activityRule_pop {
  position: fixed;
  top: 1.17rem;
  left: 50%;
  transform: translateX(-50%);
  width: 3.095rem;
  z-index: 100;
  display: none;
}
.box .activityRule_pop .content {
  background: url(../img/activityRule_pop_bg.png) no-repeat;
  background-size: 100% 100%;
  width: 100%;
  min-height: 3.365rem;
}
.box .activityRule_pop .content .info {
  color: #292933;
  padding: .39rem .18rem .21rem .18rem;
  font-size: .12rem;
  line-height: .16rem;
}
.box .activityRule_pop .closeRulePop {
  background: url(../img/close_img.png) no-repeat;
  background-size: 100% 100%;
  width: .27rem;
  height: .27rem;
  margin: auto;
  cursor: pointer;
  margin-top: .18rem;
}
.box .btn_decoration {
  background: url(../img/btn_bg.png) no-repeat center;
  background-size: 100%;
  width: 1.04rem;
  height: .21rem;
  font-size: .15rem;
  line-height: .21rem;
  color: #fff;
  text-align: center;
  margin: auto;
  margin-top: .15rem;
}
.box .opportunity_pop {
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  top: 1.17rem;
  width: 3.095rem;
  box-sizing: border-box;
  display: none;
  z-index: 10000;
}
.box .opportunity_pop .pop_content {
  width: 3.095rem;
  height: 3.365rem;
  padding: .44rem .18rem 0 .18rem;
  background: url(../img/moreList_bg.png) no-repeat;
  background-size: 100% 100%;
  box-sizing: border-box;
}
.box .opportunity_pop h4 {
  font-size: .17rem;
  color: #292933;
  line-height: .24rem;
  margin: 0;
  text-align: center;
  padding-bottom: .15rem;
}
.box .opportunity_pop .closeOppoPop {
  background: url(../img/close_img.png) no-repeat;
  background-size: 100% 100%;
  width: .27rem;
  height: .27rem;
  margin: auto;
  cursor: pointer;
  position: absolute;
  top: 3.55rem;
  left: 50%;
  transform: translateX(-50%);
}
.box .opportunity_pop .getMorePopList {
  max-height: 2.71rem;
  overflow-y: scroll;
}
.box .opportunity_pop .getMorePopList .one {
  display: flex;
  align-items: center;
  padding: .1rem 0 .1rem 0;
  border-bottom: .01rem#f5f5f5 solid;
}
.box .opportunity_pop .getMorePopList .one:first-child {
  padding-top: 0;
}
.box .opportunity_pop .getMorePopList .icon {
  width: .21rem;
  height: .21rem;
}
.box .opportunity_pop p {
  flex: 1;
  width: 1px;
  padding: 0 .1rem 0 .1rem;
  font-size: .12rem;
  color: #292923;
}
.box .opportunity_pop .oppoBtn {
  width: .66rem;
  height: .28rem;
  background-color: #FFF5EB;
  font-size: .12rem;
  text-align: center;
  line-height: .28rem;
  border-radius: .25rem;
  color: #FF6B07;
  font-weight: 500;
  display: block;
}
.box .opportunity_pop .oppoOn {
  background: url(../img/oppo_bg_on.png) no-repeat;
  background-size: 100% 100%;
  text-align: center;
  line-height: .28rem;
  border-radius: .25rem;
  color: #fff;
  font-weight: 500;
}

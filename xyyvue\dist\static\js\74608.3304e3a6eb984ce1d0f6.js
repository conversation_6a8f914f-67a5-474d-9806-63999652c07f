(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[74608],{61255:function(t,a,e){"use strict";e(91058),e(82772),e(9653);var s=e(59502),i=e(67087);a.Z={props:["dataId","productNum","isSplit","medNum","isPack","bgcolor","btncolor"],data:function(){return{goodsId:"",issplit:"",productValue:"",mednum:"",ispack:!1}},watch:{dataId:function(t){this.goodsId=t,this.productValue=this.productNum?this.productNum:0,this.issplit=this.isSplit,this.mednum=this.medNum,this.ispack=this.isPack},isExtend:function(){s.Z.$emit("cart_isExtend",this.isExtend,this.goodsId)}},methods:{addProductCart:function(t){if(this.btn_hidden)this.postCartData(this.mednum);else{var a=t.target.getAttribute("edit-cate"),e=parseInt(t.currentTarget.children[1].value),i=parseInt(this.mednum);if("add"==a)e+=i;else{if("min"!=a)return;e=1==this.issplit?e-1:e-i}e=e>0?e:0,this.productValue=e,s.Z.$emit("listenToChildEvent",{isExtend:this.isExtend,dataId:this.goodsId,productValue:this.productValue}),this.postCartData(e)}},inputCart:function(t){var a=parseInt(t.target.value);a=a>=0?a:0,this.productValue=a,s.Z.$emit("listenToChildEvent",{isExtend:this.isExtend,dataId:this.goodsId,productValue:this.productValue}),this.postCartData(t.target.value)},androidclick:function(t){if(navigator.userAgent.indexOf("Android")>=0){t.target.setAttribute("readonly",!0),t.target.blur();var a=t.target.value;s.Z.$emit("showandorideditcomp",{id:this.goodsId,val:a,showandoridedit:!0,split:this.issplit,medpack:this.mednum,package:this.ispack})}},postCartData:function(t){var a=this;if(t>0){var e=1==this.ispack?{merchantId:this.merchantId,amount:t,packageId:this.goodsId}:{merchantId:this.merchantId,amount:t,skuId:this.goodsId};this.putRequest("post","/app/changeCart",e).then((function(e){if("success"===e.data.status){a.btn_hidden&&s.Z.$emit("changeprompt",{dialog:"已添加到购物车!",showprompt:!0}),Number(e.data.data.qty)&&(0,i.M0)("h5_page_CommodityDetails_o",{commodityId:a.goodsId,real:1}),e.data.data.qty!=t&&(a.productValue=e.data.data.qty),null!=e.data.dialog&&(20==e.data.dialog.style?s.Z.$emit("changesureDialog",{dialogmsg:e.data.dialog.msg,showsureDialog:!0}):s.Z.$emit("changeprompt",{dialog:e.data.dialog.msg,showprompt:!0})),e.errorMsg&&s.Z.$emit("changeprompt",{dialog:e.errorMsg,showprompt:!0});try{var r=1==a.ispack?{proid:a.goodsId,pronum:a.productValue,isAdd:1,type:1}:{proid:a.goodsId,pronum:a.productValue,isAdd:1};window.webkit.messageHandlers.addPlanNumber.postMessage(r)}catch(t){}try{1==a.ispack?window.hybrid.addPlanNumber(a.goodsId,a.productValue,1,1):window.hybrid.addPlanNumber(a.goodsId,a.productValue,1)}catch(t){}}else a.productValue=0,s.Z.$emit("listenToChildEvent",{isExtend:a.isExtend,dataId:a.goodsId,productValue:a.productValue}),e.data.errorMsg?s.Z.$emit("changeprompt",{dialog:e.data.errorMsg,showprompt:!0}):e.data.msg?s.Z.$emit("changesureDialog",{dialogmsg:e.data.msg,showsureDialog:!0}):s.Z.$emit("changeprompt",{dialog:e.data.dialog.msg,showprompt:!0})})).catch((function(t){}))}else{var r=1==this.ispack?{merchantId:this.merchantId,packageIds:this.goodsId}:{merchantId:this.merchantId,ids:this.goodsId};this.putRequest("post","/app/batchRemoveProductFromCart",r).then((function(t){if("success"==t.data.status){a.btn_hidden&&s.Z.$emit("changeprompt",{dialog:"已添从购物车删除!",showprompt:!0}),a.productValue=0;try{var e=1==a.ispack?{proid:a.goodsId,pronum:0,isAdd:1,type:1}:{proid:a.goodsId,pronum:0,isAdd:1};window.webkit.messageHandlers.addPlanNumber.postMessage(e)}catch(t){}try{1==a.ispack?window.hybrid.addPlanNumber(a.goodsId,0,1,1):window.hybrid.addPlanNumber(a.goodsId,0,1)}catch(t){}}}))}}},created:function(){this.goodsId=this.dataId,this.productValue=this.productNum?this.productNum:0,this.issplit=this.isSplit,this.mednum=this.medNum,this.ispack=this.isPack}}},31340:function(t,a,e){t.exports=e.p+"static/img/clinicbanner.389865e.jpg"},45995:function(t,a,e){t.exports=e.p+"static/img/nodata.ddcad20.jpg"},88264:function(t,a,e){"use strict";e.d(a,{Z:function(){return i}});var s={mixins:[e(61255).Z]},i=(0,e(51900).Z)(s,(function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"green-btn",attrs:{"data-id":t.goodsId,"is-slipt":t.issplit,"med-pack-num":t.mednum},on:{click:function(a){return a.preventDefault(),t.addProductCart.apply(null,arguments)}}},[e("span",{staticClass:"min",attrs:{"edit-cate":"min"}},[t._v("-")]),t._v(" "),e("input",{directives:[{name:"model",rawName:"v-model",value:t.productValue,expression:"productValue"}],staticClass:"input-val",class:"input-val"+t.goodsId,attrs:{"edit-cate":"edit",type:"tel"},domProps:{value:t.productValue},on:{change:t.inputCart,click:function(a){return a.preventDefault(),t.androidclick.apply(null,arguments)},input:function(a){a.target.composing||(t.productValue=a.target.value)}}}),t._v(" "),e("span",{staticClass:"add",attrs:{"edit-cate":"add"}},[t._v("+")])])}),[],!1,null,"5d221bb3",null).exports},22178:function(t,a,e){"use strict";e.d(a,{Z:function(){return u}});e(56977),e(54678),e(54747),e(92222),e(74916),e(15306);var s=e(88264),i=(e(91058),{props:["saleTime"],data:function(){return{startSaleTime:"",timestr:"距离开抢还剩：0天00时00分00秒"}},methods:{timeStamp:function(t){var a=this,e=new Date(t.replace(/-/g,"/")).getTime(),s=setInterval((function(){var t=(new Date).getTime(),i=(e-t)/1e3;i<=0&&(clearInterval(s),a.$emit("refreshPackPage"));var r=parseInt(i)%60;r=r>=10?r:"0"+r;var n=parseInt(i/60)%60;n=n>=10?n:"0"+n;var d=parseInt(parseInt(i/60)/60)%24;d=d>=10?d:"0"+d;var o=parseInt(parseInt(parseInt(i/60)/60)/24);a.timestr=o>0?"距离开抢还剩："+o+"天"+d+"时"+n+"分"+r+"秒":"距离开抢还剩："+d+"时"+n+"分"+r+"秒"}),1e3)}},mounted:function(){this.startSaleTime=this.saleTime,this.timeStamp(this.startSaleTime)}}),r=e(51900),n=(0,r.Z)(i,(function(){var t=this,a=t.$createElement;return(t._self._c||a)("div",{staticClass:"counttime"},[t._v(t._s(t.timestr))])}),[],!1,null,"5aec43f4",null).exports,d=e(67087),o=e(59502),c={props:["goodsData","packType","licenseStatus"],data:function(){return{packList:[],imgUrl:"",packtitle:"",tagList:[]}},computed:{detailUrl:function(){return o.Z.detailBaseUrl}},filters:{fixedtwo:function(t){return parseFloat(t).toFixed(2)}},watch:{goodsData:function(t){this.packList=this.goodsData;var a=[];this.packList.forEach((function(t){t&&t.skuList&&(a=a.concat(t.skuList))})),(0,d.dA)("h5_page_ListPage_ExpStatic",{list:a},"sku"),this.packtitle=1==this.packType?"精品套餐":"诊所精品套餐"}},methods:{isStartSale:function(t){return(new Date).getTime()<(null==t?0:new Date(t.replace(/-/g,"/")).getTime())},refreshFatherPage:function(){this.$emit("refreshPage")}},mounted:function(){this.imgUrl=this.imgBaseUrl},components:{greenBtn:s.Z,packtime:n}},u=(0,r.Z)(c,(function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"packagecomp"},t._l(t.packList,(function(a){return e("div",{key:a.id,staticClass:"packageclinic-item",class:"pack-item"+a.id},[e("div",{staticClass:"title"},[t._v(t._s(t.packtitle))]),t._v(" "),e("div",{staticClass:"price-box"},[1===t.licenseStatus||5===t.licenseStatus?e("b",[t._v("价格认证资质可见")]):e("div",[10!=a.status?e("p",[e("span",[t._v("套餐价：")]),t._v(" "),e("i",[t._v("¥"+t._s(t._f("fixedtwo")(a.totalPrice)))])]):t._e(),t._v(" "),10!=a.status?e("p",[e("span",[t._v("原价：")]),t._v(" "),e("i",[t._v("¥"+t._s(t._f("fixedtwo")(a.discountPrice+a.totalPrice)))])]):t._e(),t._v(" "),10==a.status?e("b",[t._v("价格签署协议可见")]):t._e()])]),t._v(" "),t.isStartSale(a.startSaleTime)?e("packtime",{attrs:{"sale-time":a.startSaleTime},on:{refreshPackPage:t.refreshFatherPage}}):t._e(),t._v(" "),0==a.status||10==a.status||t.isStartSale(a.startSaleTime)?t._e():e("div",{staticClass:"sold-out"},[t._v("\n      售罄\n    ")]),t._v(" "),0!=a.status||t.isStartSale(a.startSaleTime)||1===t.licenseStatus||5===t.licenseStatus?t._e():e("greenBtn",{attrs:{"data-id":a.id,"is-pack":!0,"is-split":1,"product-num":a.packageCartCount,"med-num":1}}),t._v(" "),e("div",{staticClass:"pack-list clearfixed"},t._l(a.skuList,(function(a){return e("a",{key:a.sku.id,staticClass:"pack-item",attrs:{href:t.detailUrl+"product_id="+a.sku.id}},[e("div",{staticClass:"img"},[e("img",{attrs:{src:t.imgUrl+"/ybm/product/min/"+a.sku.imageUrl,alt:""}}),t._v(" "),a.sku.markerUrl&&!a.sku.reducePrice?e("img",{staticClass:"activity-token",attrs:{src:t.imgUrl+a.sku.markerUrl,alt:""}}):t._e(),t._v(" "),a.sku.markerUrl&&a.sku.reducePrice&&(1!=a.sku.isControl||1==a.sku.isPurchase&&1==a.sku.isControl)?e("div",{staticClass:"mark-text"},[e("img",{attrs:{src:t.imgUrl+a.sku.markerUrl,alt:""}}),t._v(" "),e("h4",[t._v("药采节价："+t._s(t._f("fixedtwo")(a.sku.reducePrice)))])]):t._e()]),t._v(" "),e("h2",{staticClass:"textellipsis"},[t._v(t._s(a.sku.commonName))]),t._v(" "),1===t.licenseStatus||5===t.licenseStatus?e("b",[t._v("价格认证资质可见")]):e("div",[10!=a.sku.status?e("p",[e("span",{staticClass:"fob"},[t._v("¥"+t._s(t._f("fixedtwo")(a.sku.fob)))]),t._v(" "),e("i",[t._v("x"+t._s(a.productNumber))])]):t._e(),t._v(" "),10==a.sku.status?e("b",[t._v("价格签署协议可见")]):t._e()]),t._v(" "),e("h3",{staticClass:"textellipsis"},[t._v(t._s(a.sku.spec))]),t._v(" "),e("div",{staticClass:"label-box"},[a.sku.tagList?e("div",{staticClass:"labels"},t._l(a.sku.tagList,(function(a,s){return"临期"==a.name||"近效期"==a.name?e("span",{key:s,class:"span"+a.uiType},[t._v(t._s(a.name))]):t._e()})),0):t._e()])])})),0)],1)})),0)}),[],!1,null,"6fd2fc78",null).exports},46445:function(t,a,e){"use strict";e.r(a),e.d(a,{default:function(){return d}});var s=[function(){var t=this.$createElement,a=this._self._c||t;return a("div",{staticClass:"banner"},[a("img",{attrs:{src:e(31340),alt:""}})])},function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"content"},[s("h2",[t._v("暂无套餐...")]),t._v(" "),s("img",{attrs:{src:e(45995),alt:""}})])}],i=e(22178),r=e(59502),n={data:function(){return{clinicData:[],licenseStatus:0,nodata:!1,nomoredata:!1}},methods:{getDataList:function(){var t=this;this.putRequest("post","/app/activityPackage/pageList?effectiveStatus=1&limit=100",{merchantId:this.merchantId,mark:"2",offset:"0"}).then((function(a){if("success"==a.data.status)if(null!=a.data.resultPage.rows&&a.data.resultPage.rows.length>0){var e=a.data.resultPage.rows;t.licenseStatus=a.data.resultPage.licenseStatus,t.clinicData.push.apply(t.clinicData,e)}else t.nodata=!0;t.$nextTick((function(){r.Z.$emit("changeloading",!1)}))})).catch((function(t){}))},moveEvent:function(){try{window.webkit.messageHandlers.hideKeyboard.postMessage({hide:"1"})}catch(t){}},refreshNowPage:function(){this.clinicData=[],this.getDataList()}},created:function(){this.getDataList()},components:{packagecomp:i.Z}},d=(0,e(51900).Z)(n,(function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{attrs:{id:"packageclinic"},on:{touchmove:t.moveEvent}},[t._m(0),t._v(" "),e("packagecomp",{attrs:{"goods-data":t.clinicData,"pack-type":2,"license-status":t.licenseStatus||0},on:{refreshPage:t.refreshNowPage}}),t._v(" "),t.nodata?e("div",{staticClass:"nodata"},[t._m(1)]):t._e(),t._v(" "),t.nomoredata?e("div",{staticClass:"nomore-data"},[t._v("无更多数据")]):t._e()],1)}),s,!1,null,"71692e46",null).exports}}]);
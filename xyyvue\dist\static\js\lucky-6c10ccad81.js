"use strict";

var lucky = {
  //只需要更换turnplate的id就可以更换相应的奖品
  countNum: 0,
  $cover: $(".cover"),
  $pop: $(".pop"),
  $pointer: $(".pointer"),
  $notice: $(".notice"),
  $countNum: $(".count-num"),
  $knowBtn: $(".know-btn"),
  $startTime: $("#startTime"),
  $turnBox: $(".turntable_box"),
  $mainBox: $(".main-box"),
  $activityRule: $(".activityRule"),
  $pop_black_bg: $(".pop_black_bg"),
  $activityRule_pop: $(".activityRule_pop"),
  $closeRulePop: $(".closeRulePop"),
  $getmore: $(".getmore"),
  $opportunity_pop: $(".opportunity_pop"),
  $closeOppoPop: $(".closeOppoPop"),
  isClick: true, //判读能否点击转盘,
  turnplate: {},
  testApi: location.origin + "/", //线上
  // testApi: 'https://new-app.stage.ybm100.com/', //预发
  // testApi: 'https://new-app.test.ybm100.com/',//测试
  imgUrl:
    location.origin.indexOf("https://app-v4.ybm100.com") >= 0
      ? "http://upload.ybm100.com"
      : location.origin.indexOf("stage") >= 0
      ? "https://upload.stage.ybm100.com"
      : "https://upload.test.ybm100.com", //线上
  // imgUrl: 'https://upload.stage.ybm100.com', //预发
  // imgUrl: 'http://upload.test.ybm100.com',//测试
  shareContent: {},
  count: 0,
  hasclick: true, //点击抽奖 -- 锁
  loading: false,
  hasEndActivity: true, //判断 活动开始活动结束
  alreadyDrawCount: 0, //今天抽奖次数
  timer: null,
  init: function init(cb) {
    //计算角度
    this.$cover.hide();
    this.$pop.hide();
    var len = this.turnplate.luckDrawPrizeList.length,
      html = "";
    if (len % 2 !== 0) {
      this.isClick = false;
      return;
    }
    $(".share-box")
      .find("header")
      .html(this.turnplate.broadcast);
    $(".info").html("<p>" + this.turnplate.drawRegulation + "</p>");
    //根据个数，调置转盘背景
    if (len >= 4 && len % 2 === 0) {
      if (len == 4) {
        $(".turntable_bg")
          .attr("src", "../static/img/new_inner_bg_" + len + ".png")
          .css("transform", "rotate(-45deg)");
      } else {
        $(".turntable_bg").attr(
          "src",
          "../static/img/new_inner_bg_" + len + ".png"
        );
      }
    }
    for (var i = 0; i < len; i++) {
      var _turnplate$luckDrawPr = this.turnplate.luckDrawPrizeList[i],
        titile = _turnplate$luckDrawPr.titile,
        id = _turnplate$luckDrawPr.id,
        drawPictureUrl = _turnplate$luckDrawPr.drawPictureUrl;

      var name = titile,
        url = drawPictureUrl;
      var color = i % 2 === 0 ? "#fff" : "#9D5A0E";
      html +=
        '<div class="item" style="transform:rotate(' +
        Math.floor(360 / len) * i +
        "deg);color:" +
        color +
        "\">\n\t\t\t\t\t<div id='" +
        id +
        "'>" +
        name +
        '</div>\n\t\t\t\t\t<img src="' +
        this.imgUrl +
        url +
        '" />\n\t\t\t\t</div>';
    }
    this.$turnBox.append(html);
    if (cb) {
      cb();
    }
  },
  computedDate: function computedDate(time) {
    var second = parseInt(time) % 60;
    second = second >= 10 ? second : "0" + second;
    var min = parseInt(time / 60) % 60;
    min = min >= 10 ? min : "0" + min;
    var hour = parseInt(parseInt(time / 60) / 60) % 24;
    hour = hour >= 10 ? hour : "0" + hour;
    var day = parseInt(parseInt(parseInt(time / 60) / 60) / 24);
    return day > 0
      ? day + "天" + hour + "时" + min + "分" + second + "秒"
      : hour + "时" + min + "分" + second + "秒";
  },
  timeStamp: function timeStamp(startTime, endTime) {
    var _this2 = this;

    var sTime = new Date(startTime).getTime();
    var eTime = new Date(endTime).getTime();
    lucky.timer = setInterval(function() {
      var nowTime = new Date().getTime();
      var difftime = (sTime - nowTime) / 1000;
      var distanceTime = (eTime - nowTime) / 1000;
      if (difftime > 0) {
        //活动未开始
        _this2.$startTime.html(
          "\u5012\u8BA1\u65F6\uFF1A" +
            lucky.computedDate(difftime)
        );
        lucky.$pointer
          .find("em")
          .html("即将开始")
          .removeClass("no-count")
          .siblings("img")
          .attr("src", "../static/img/point_start.png");
        lucky.hasEndActivity = false;
      } else if (difftime <= 0 && distanceTime >= 0) {
        //活动进行中
        _this2.$startTime.html(
          "\u5012\u8BA1\u65F6\uFF1A" +
            lucky.computedDate(distanceTime)
        );
        lucky.hasEndActivity = true;
      } else {
        clearInterval(lucky.timer);
        _this2.$startTime.html("活动已结束，欢迎下次再来");
        lucky.$pointer
          .find("em")
          .html("活动结束")
          .removeClass("no-count")
          .siblings("img")
          .attr("src", "../static/img/point_start.png");
        lucky.hasEndActivity = false;
      }
      if (difftime > -1 && difftime < 1) {
        $(".notice").remove();
        lucky.isClick = true;
        lucky.$pointer
          .find("em")
          .html("立即抽奖")
          .removeClass("no-count")
          .siblings("img")
          .attr("src", "../static/img/point_start.png");
      }
    }, 1000);
    this.timeStampOnce(startTime, endTime);
  },
  timeStampOnce: function timeStampOnce(startTime, endTime) {
    var sTime = new Date(startTime).getTime();
    var eTime = new Date(endTime).getTime();
    var nowTime = new Date().getTime();
    var difftime = (sTime - nowTime) / 1000;
    var distanceTime = (eTime - nowTime) / 1000;
    if (difftime > 0) {
      //活动未开始
      this.$startTime.html(
        "\u5012\u8BA1\u65F6\uFF1A" +
          lucky.computedDate(difftime)
      );
      lucky.hasEndActivity = false;
    } else if (difftime <= 0 && distanceTime >= 0) {
      //活动进行中
      this.$startTime.html(
        "\u5012\u8BA1\u65F6\uFF1A" +
          lucky.computedDate(distanceTime)
      );
    } else {
      //clearInterval(timer);
      this.$startTime.html("活动已结束，欢迎下次再来");
      lucky.hasEndActivity = false;
    }
  },
  prizeShow: function prizeShow(obj) {
    //获取中奖数据

    this.$cover.fadeIn();
    this.$pop.fadeIn();
    //显示次数
    this.$pointer
      .find("em")
      .html("立即抽奖")
      .removeClass("no-count")
      .siblings("img")
      .attr("src", "../static/img/point_start.png");
    this.$countNum.length !== 0
      ? this.$countNum.html(this.countNum)
      : this.$pointer.append(
          "<span class='animated fadeIn count-num'>\xD7" +
            this.countNum +
            "</span>"
        );
    if (obj.skuType === 4) {
      //没抽中
      this.$pop.find(".no-prize").fadeIn();
      this.$pop.find("section").hide();
    } else {
      this.$pop.find(".no-prize").hide();
      this.$pop.find("section").fadeIn();
      var titile = obj.titile,
        drawPictureUrl = obj.drawPictureUrl;

      this.$pop.find("section p").html("\u606D\u559C\u62BD\u4E2D" + titile);
      this.$pop
        .find("section img")
        .attr({ src: "" + this.imgUrl + drawPictureUrl, alt: titile });
    }
  },
  Marquee: function Marquee(id) {
    var container = document.getElementById(id),
      original = document.getElementById("list"),
      clone = document.getElementById("clone"),
      speed = arguments[1] || 10;
    if (container.offsetHeight < original.offsetHeight) {
      clone.innerHTML = original.innerHTML;
    }
    var rolling = function rolling() {
      if (container.scrollTop >= original.offsetHeight) {
        container.scrollTop = 0;
      } else {
        container.scrollTop++;
      }
    };
    var scrollMove = setInterval(rolling, speed); //数值越大，滚动速度越慢
    container.onmouseover = function() {
      clearInterval(scrollMove);
    };

    container.onmouseout = function() {
      scrollMove = setInterval(rolling, speed);
    };
  },
  rotateFn: function rotateFn(item, obj) {
    var len = this.turnplate.luckDrawPrizeList.length,
      angles = item * (360 / len) - 360 / (len * 2),
      moreAngle = 0,
      startAngle = 0;
    if (len % 2 === 0) moreAngle = 360 / (len * 2);
    $(".new_turntable_bg").stopRotate();
    var _this = this;
    $(".new_turntable_bg").rotate({
      angle: startAngle,
      animateTo: -angles + 1800 + moreAngle,
      duration: 8000,
      callback: function callback() {
        //调最弹窗,提交获奖信息
        _this.prizeShow(obj);
        _this.loading = false;
      }
    });
  },
  setupWebViewJavascriptBridge: function setupWebViewJavascriptBridge(
    callback
  ) {
    var _this3 = this;

    if (window.WebViewJavascriptBridge) {
      return callback(WebViewJavascriptBridge);
    } else {
      if (this.count < 3) {
        setTimeout(function() {
          _this3.count++;
          _this3.setupWebViewJavascriptBridge(function(bridge) {
            bridge.callHandler(
              "JSGetParameter",
              { key: "value" },
              function responseCallback(responseData) {
                localStorage.setItem("merchantId", responseData);
              }
            );
          });
        }, 100);
        return;
      }
    }
    if (window.WVJBCallbacks) {
      return window.WVJBCallbacks.push(callback);
    }
    window.WVJBCallbacks = [callback];
    var WVJBIframe = document.createElement("iframe");
    WVJBIframe.style.display = "none";
    WVJBIframe.src = "https://__bridge_loaded__";
    document.documentElement.appendChild(WVJBIframe);
    setTimeout(function() {
      document.documentElement.removeChild(WVJBIframe);
    }, 0);
  },

  //获取url传参
  QueryString: function QueryString(item) {
    var svalue = location.search.match(
      new RegExp("[?&]" + item + "=([^&]*)(&?)", "i")
    );
    return svalue ? svalue[1] : svalue;
  },
  getUrlParam: function getUrlParam(name) {
    var reg = new RegExp("(\\?|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
    var r = window.location.href.substr(2).match(reg); //匹配目标参数
    if (r != null) return unescape(r[2]);
    return null; //返回参数值
  },
  getMerchantId: function getMerchantId() {
    //先从url获取merchantId,获取不到再从终端获取
    var merchantId = this.QueryString("merchantId");
    if (merchantId) {
      localStorage.setItem("merchantId", merchantId);
    } else {
      if (this.isIOS()) {
        merchantId = this.getUrlParam("merchantId");
        // this.$turnBox.html("2"+merchantId)
        localStorage.setItem("merchantId", merchantId);
        // this.setupWebViewJavascriptBridge(function (bridge) {
        //   bridge.callHandler('JSGetParameter', {'key': 'value'}, function responseCallback(responseData) {
        //     localStorage.setItem("merchantId", responseData)
        //   })
        // })
      } else {
        if (window.hybrid) {
          window.hybrid.setRightMenu("1", "");
          localStorage.setItem("merchantId", window.hybrid.getMerchantId());
        }
      }
    }
  },
  isIOS: function isIOS() {
    return /iphone|ipad|ipod/i.test(navigator.userAgent);
  },
  isANDROID: function isANDROID() {
    return /android/i.test(navigator.userAgent);
  },
  share: function share() {
    // try {
    //   window._self_fn = {};
    //   window._self_fn.isShareSuccess = function () {
    //     //分享成功后 调用分享次数接口
    //     this.$turnBox.html("分享了111！")
    //     api.addLuckCount().then(async res => {
    //       this.$turnBox.html("分享了222！")
    //       if (res.success) {
    //         this.$turnBox.html("分享了333！")
    //         console.log('次数增加成功')
    //         //查询抽奖次数
    //         const c = await api.getLuckCount();
    //         if (!c.success) {
    //           return;
    //         }
    //         this.countNum = c.data;
    //         if ($('.notice').length > 0) {
    //           $('.notice').remove();
    //         }
    //
    //         this.$pointer.find('em').html('开始抽奖').removeClass('no-count').siblings('img').attr('src', '../static/img/point_start.png')
    //         this.isClick = true;
    //         $('.notice').hide();
    //         $('.countNum').length !== 0
    //           ? $('.countNum').html(this.countNum)
    //           : this.$pointer.append(`<span class='animated fadeIn count-num'>×${this.countNum}</span>`)
    //       } else {
    //         if ($('.countNum').length > 0) {
    //           $('.countNum').remove()
    //         }
    //         $('.notice').length !== 0
    //           ? $('.notice').html('今日次数已用完,明天再来转好运')
    //           : this.$pointer.append(`<span class='animated fadeInUp notice'>今日次数已用完,明天再来转好运</span>`);
    //         console.log(res.msg)
    //       }
    //     })
    //
    //   };
    // } catch (err) {
    //   console.log(err)
    // }
    /*
     * @title String 分享活动标题
     * @url   String 分享活动连接
     * @desc  String 分享到好友
     * @desc_pyq String 分享至朋友圈
     * */
    // 活动结束或未开始点击分享无效
    if (!lucky.hasEndActivity) return;
    //点击分享
    var _shareContent = this.shareContent,
      title = _shareContent.title,
      url = _shareContent.url,
      desc = _shareContent.desc,
      desc_pyq = _shareContent.desc_pyq;

    if (this.isIOS()) {
      window.webkit.messageHandlers.getShareLinks.postMessage({
        title: title,
        url: url,
        desc: desc,
        desc_pyq: desc_pyq
      });
    } else {
      if (window.hybrid) {
        window.hybrid.getShareLinks(title, url, desc, desc_pyq);
      }
    }
  }
};

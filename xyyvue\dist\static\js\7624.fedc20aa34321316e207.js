(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[7624],{61255:function(t,e,s){"use strict";s(91058),s(82772),s(9653);var a=s(59502),i=s(67087);e.Z={props:["dataId","productNum","isSplit","medNum","isPack","bgcolor","btncolor"],data:function(){return{goodsId:"",issplit:"",productValue:"",mednum:"",ispack:!1}},watch:{dataId:function(t){this.goodsId=t,this.productValue=this.productNum?this.productNum:0,this.issplit=this.isSplit,this.mednum=this.medNum,this.ispack=this.isPack},isExtend:function(){a.Z.$emit("cart_isExtend",this.isExtend,this.goodsId)}},methods:{addProductCart:function(t){if(this.btn_hidden)this.postCartData(this.mednum);else{var e=t.target.getAttribute("edit-cate"),s=parseInt(t.currentTarget.children[1].value),i=parseInt(this.mednum);if("add"==e)s+=i;else{if("min"!=e)return;s=1==this.issplit?s-1:s-i}s=s>0?s:0,this.productValue=s,a.Z.$emit("listenToChildEvent",{isExtend:this.isExtend,dataId:this.goodsId,productValue:this.productValue}),this.postCartData(s)}},inputCart:function(t){var e=parseInt(t.target.value);e=e>=0?e:0,this.productValue=e,a.Z.$emit("listenToChildEvent",{isExtend:this.isExtend,dataId:this.goodsId,productValue:this.productValue}),this.postCartData(t.target.value)},androidclick:function(t){if(navigator.userAgent.indexOf("Android")>=0){t.target.setAttribute("readonly",!0),t.target.blur();var e=t.target.value;a.Z.$emit("showandorideditcomp",{id:this.goodsId,val:e,showandoridedit:!0,split:this.issplit,medpack:this.mednum,package:this.ispack})}},postCartData:function(t){var e=this;if(t>0){var s=1==this.ispack?{merchantId:this.merchantId,amount:t,packageId:this.goodsId}:{merchantId:this.merchantId,amount:t,skuId:this.goodsId};this.putRequest("post","/app/changeCart",s).then((function(s){if("success"===s.data.status){e.btn_hidden&&a.Z.$emit("changeprompt",{dialog:"已添加到购物车!",showprompt:!0}),Number(s.data.data.qty)&&(0,i.M0)("h5_page_CommodityDetails_o",{commodityId:e.goodsId,real:1}),s.data.data.qty!=t&&(e.productValue=s.data.data.qty),null!=s.data.dialog&&(20==s.data.dialog.style?a.Z.$emit("changesureDialog",{dialogmsg:s.data.dialog.msg,showsureDialog:!0}):a.Z.$emit("changeprompt",{dialog:s.data.dialog.msg,showprompt:!0})),s.errorMsg&&a.Z.$emit("changeprompt",{dialog:s.errorMsg,showprompt:!0});try{var r=1==e.ispack?{proid:e.goodsId,pronum:e.productValue,isAdd:1,type:1}:{proid:e.goodsId,pronum:e.productValue,isAdd:1};window.webkit.messageHandlers.addPlanNumber.postMessage(r)}catch(t){}try{1==e.ispack?window.hybrid.addPlanNumber(e.goodsId,e.productValue,1,1):window.hybrid.addPlanNumber(e.goodsId,e.productValue,1)}catch(t){}}else e.productValue=0,a.Z.$emit("listenToChildEvent",{isExtend:e.isExtend,dataId:e.goodsId,productValue:e.productValue}),s.data.errorMsg?a.Z.$emit("changeprompt",{dialog:s.data.errorMsg,showprompt:!0}):s.data.msg?a.Z.$emit("changesureDialog",{dialogmsg:s.data.msg,showsureDialog:!0}):a.Z.$emit("changeprompt",{dialog:s.data.dialog.msg,showprompt:!0})})).catch((function(t){}))}else{var r=1==this.ispack?{merchantId:this.merchantId,packageIds:this.goodsId}:{merchantId:this.merchantId,ids:this.goodsId};this.putRequest("post","/app/batchRemoveProductFromCart",r).then((function(t){if("success"==t.data.status){e.btn_hidden&&a.Z.$emit("changeprompt",{dialog:"已添从购物车删除!",showprompt:!0}),e.productValue=0;try{var s=1==e.ispack?{proid:e.goodsId,pronum:0,isAdd:1,type:1}:{proid:e.goodsId,pronum:0,isAdd:1};window.webkit.messageHandlers.addPlanNumber.postMessage(s)}catch(t){}try{1==e.ispack?window.hybrid.addPlanNumber(e.goodsId,0,1,1):window.hybrid.addPlanNumber(e.goodsId,0,1)}catch(t){}}}))}}},created:function(){this.goodsId=this.dataId,this.productValue=this.productNum?this.productNum:0,this.issplit=this.isSplit,this.mednum=this.medNum,this.ispack=this.isPack}}},11932:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEIAAABCCAYAAADjVADoAAAKSklEQVR4Xu2beZAcdRXHv6+nZ9hNdpMYQCSEsJIh090Jm2R6l4iKJB4omiogcmiJIaAcymUACUQr4VBK0UJESIxRMSIlEAxqUQIekKCgyaY3sBW6e1NDEsMSRQrJLmGP6Zl+1m92ZulpZ3d6ruxS2d8/Se38jvc+/XvH7/26CeMtQ4BGkQNpWutc102dxixJoZC0xTTbOgC4oyHTqIFQ1ZY5gLuJGTNoUIp9RPRF0zS2HlYgFEV/kgif9CrNjKdt2/jYYQVCVeMHAZqYrzS/bVntDYcZCJ0LKWxZxqiY66gsKgCo6mEMIhpdMCkUSs4CMI1I2kSEkM9HpIlwruu6XQMDnNi794UDh8pMDsGO0MOaRme6Li8hgg5gMoAJzDyVKBsvstoyMxPRmwDeBtAD8A4iaWMyeeCpRCIxUEsotQIRam5uPjaVCl/AjCsBvL9CJfYx48eRiPNAR8fUfwObUxXO93/Dqw4iFmuJEWEpEZ/PzCcSkVQNoZnZJaLdAG8i4g2mucOsxry5OaoJIqRp8c8xYyVAwg/I1RTUM5fYDbuJ+DbTbH8YQFV2R1VATJ9+an1Dw8CFRHS3sH8/AGakifgtZnqTiP8G0LNE1CHLyb29vcd1RyL/ZFmWp/T3H3GCLGMOc3oRQB8EeCozTfI71ez8vQBWdndj/f79hvh/Ra1iEJoWjzLTTQCWAflRICtZAsBDrsvPhEL920zTPBhE4ubm5onJZKiFSPo4M84lglJgXBrgXxPhVtNsF+uU3SoCMWuWfpQk4WEifMRvCszoJuJ1qZS0Lhzu7TJNM1mOlJqmRVw3Mo0odAkzriHKRJ2hJnYbgC2yLC3bubPtlXLWEGPKBiHMobHRuR/gC/IFyzg103Xdizs7d2wvV7BC42Kx+S2SFFrLzPMLmMtj3d24sFwzKQuESIwikdS3mHG1T+C3iejBZDJ5eyLR0VVNCLm5YrH500IhupmZLgbgO6vgp44jX59IbO0pde1yQIRUNS4A3A7Q0AGJGf1E+LbjyPeUI0gpggv/4Tjh5QCEbxqCMSgD32xZ7feWGk1KBqFpp0aZk1tEmpwTfjDGS49MmMBLDcNwSlGq3L66rod7e7GWGct8ZrKfSPqMaba9UMrcJYEQjou5/hEAZ3n9FYCNjiNfWuud4FcsFos1SlLjGoC/kO/v+AnH6TmnlLS8RBDx85npwfwIwc8T4aJKw1cpT8/bNxptnh4Oyw8P5h1DLQXQJZa1/YGg8wYGoWlaA3P9nwB8wBO8BohwrWm2rwu6YC36aVr8cma6y5fMmUR9C4LmLYFBKEp8CRGtBzDVo4zhOO6ZicSO12uhYNA5o9H5R4fD9ChAIp/JNj7IjItsu31TkHkCgtDDqorvMfPV3kMUkXuWae74fZCFKu0TjUaPiEQmX+W6WExEzznOgTsTicRQmNS0+KeY6QmPAxdH+nscp3tFEF8RCISmaVNdt+5JImr1KPSCZRnzK1UwyPhBCJNuYqbVWafoAPRNy9r+fW/5X1Vb2gBu8cBoS6VSS4LkNIFAKMops4hShjdvAHClZRlrgihSSZ/ByDBxBZA5z3grWhuI+q7y+gBVbVkK8AavebguLwqS4QYCkV3gF54QdcB1+fTOznZxIVPLFtI0fTkzr/Y9hBQzVtm2cSeQOWtkmqLMayIK7QAwJecosg9sbTEhA4HQNP0eXzq9LZ1OLdm168VXiy1Qwe80e/YpZ7huWuQtk7y2D0jPp1Lpc/xOWpgwc73wE6d41l1vWcZlxeQIBEJV45sBOt0z2ca+vvRlNSyuSprWKoq49xHhKM+64grgCcdxLi9k99Om6RMmT4bYued5zON5y2r/UJVA6HsANHmeyr2pVM8NI3njpqaFdXV1PQJeeOJEetowghdPVLXl0wCL7TwjXwHeQoQvD5+8LZQVpecHRHSVZ9w+yzJOqBYIUVnO2Z2Y87uWZXzDa5/ehZqamurq64+8V5wDABHF8CCzvNy2t71RTKCZM+PRSIR+C2C2r+8+otR5pvnithHmkFRVvwPAinceGrpt2/DKXnB4QNPQRV1wyGMz4zbbNkQoK9hmzZp7XCgkC0eaS75SzPTzgwfDX+vq+nvfcOMURT8Z4J/5wrTo/rLr4tLOTuOZYiAVRb+VCKs8/dKWZRStnwYFkXc9x8y32Hb7rcMJpar6sQAMAOLfXEuKkrxtG9cV2kmqOu8kQN4A8KneeZnRJUnSMtNs+0sxCOJ3RYmvJqJbvH2DXCPWBIQ4pQL1d4jSmvARHqF6AbrGsrYLhzYU9mbMOPk9DQ3h9cxYAuRd+vQQsTjL/DLoexNjCoRQXNNa38fsioPQ5/22zsxX2nb74+LvTU3zptTVyXcBvDS/rsCi2rWyvh5rS6lxjDkQQkld14/q7eXf5B+GMs+8K5VyF9fVpROpVFg41Qt9R/sBIrrRNLEWKK3QMyZBCBhz5849LpmUn2JmzXfXKULyUwBfDNARnl0jssa7e3qwupxC7JgFIdJyRdE/QYSfACgaz5npfkD6epBQW8h5jmUQQt6QprUsBvhR5pGuAvmhvj73K5VkrGMdRObhaVr8MtelHxKhLj9EZu5C/tzfP7B0z56drwUJk8PnImMofA4npMg46+qOXE6EG3LJVvZe9I9E4RtMc2vFN9zvih0hAE2fPr2+oeHoeZJEZzBLkyUJz/X3J//68ssd/6lkJ+TGvmtAVEPZkeYYB5GlMw5iHES+oYzviPEdMb4jCgaPqpjGMcc0T5wyRT5bkuhEZh56LdBf6GDGZoA31zoUljc/LSTCQl/mOlSoISLXdXm349Dju3cb3bl+eYUZRWldBbjXA9zofyu2PKHG3ijxdi9AbwG4z7aNlcOAiL9GRO8de+JXXyJmft2224d09e2I+GaivPuL6kswRmZkxnO2bXy44I6IxRY0E6XWEGFBDd+cHVUU2dcR/+G6dN2uXduHrgYCFW/LlVxV9WcBnOYZv6+v743Y3r17+0eaM3tK3UaEk3P9mNFh28bccmUpNq6mIBRF/xURxPtNmSbsUpJCi0yz7aWRBNO01tnM6ccBGrpdA/A7yzLOLqZQub/XFISq6l8V3tkjnMNMt9h2w50jfGoQUpSWFYC7iuidWiYzrrdtQ1TFa9JqCkLceEmSnPBVpF4B5PMta2tbgYue0Jw58dnpNIlS//GenTQgy6GTKnnFuBi9moIQi6uq/h0AN3pf/2PmVwFpvSTRoz098m5Z/hdPmHD0TNfls4j4Ci+EQYvCj2zbuLaYMpX8XnMQM2e2Hh+JuH8AMMcnaBLg/cyZ5AZE3MiMY73mkO2/03Xp3M7O7Z2VKFpsbM1BCAE0TT9PPFUAxxQTyPf7f4lwhWkaG0scV3L3QwICWCir6luLxeUxETUH+CpAXDq/RITbTLPxsVp8w+UndYhADC4bjeozZTnzzcWXCrxZnwuxAwCtY6Y1tTYHL4xDCiK3sIgmRPJnJQkfJUITc+a7kT3MeFaWpY21jA7D2cyogCjZgA/BgHEQWcj/A6nj0H/qIYpjAAAAAElFTkSuQmCC"},85660:function(t,e,s){t.exports=s.p+"static/img/seeall.2541da9.png"},81195:function(t,e,s){t.exports=s.p+"static/img/icon_empty.fff9bd9.png"},84786:function(t,e,s){"use strict";s.d(e,{Z:function(){return d}});s(91058),s(47042),s(41539),s(68309),s(91038),s(78783),s(82526),s(41817),s(32165),s(66992),s(33948);var a=s(61255),i=s(59502);function r(t,e){var s="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!s){if(Array.isArray(t)||(s=function(t,e){if(!t)return;if("string"==typeof t)return n(t,e);var s=Object.prototype.toString.call(t).slice(8,-1);"Object"===s&&t.constructor&&(s=t.constructor.name);if("Map"===s||"Set"===s)return Array.from(t);if("Arguments"===s||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(s))return n(t,e)}(t))||e&&t&&"number"==typeof t.length){s&&(t=s);var a=0,i=function(){};return{s:i,n:function(){return a>=t.length?{done:!0}:{done:!1,value:t[a++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,o=!0,d=!1;return{s:function(){s=s.call(t)},n:function(){var t=s.next();return o=t.done,t},e:function(t){d=!0,r=t},f:function(){try{o||null==s.return||s.return()}finally{if(d)throw r}}}}function n(t,e){(null==e||e>t.length)&&(e=t.length);for(var s=0,a=new Array(e);s<e;s++)a[s]=t[s];return a}var o={props:{btn_hidden:{default:!1},from_tab:{type:String,default:""},goodItem:Object,big:{type:Boolean,default:!1}},computed:{isExtend:function(){return 0!=this.productValue}},mounted:function(){var t=this;i.Z.$on("listenToChildEvent",(function(e){var s=e.isExtend,a=e.dataId;parseInt(a)===parseInt(t.goodsId)&&(s||(t.productValue=0))})),i.Z.$on("update_cart",(function(e){var s,a=e.length,i=0,n=r(e);try{for(n.s();!(s=n.n()).done;){var o=s.value;if(o.item.id===t.goodsId){t.productValue=o.item.amount;break}i++}}catch(t){n.e(t)}finally{n.f()}a===i&&(t.productValue=0)}))},watch:{isExtend:function(){i.Z.$emit("cart_isExtend",this.isExtend,this.goodsId)}},mixins:[a.Z]},d=(0,s(51900).Z)(o,(function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"buybtn",class:{bigStyle:t.big},attrs:{"data-id":t.goodsId,"is-slipt":t.issplit,"med-pack-num":t.mednum,"is-extend":t.isExtend},on:{click:function(e){return e.stopPropagation(),e.preventDefault(),t.addProductCart.apply(null,arguments)}}},[t.btn_hidden?t._e():s("span",{directives:[{name:"show",rawName:"v-show",value:t.isExtend,expression:"isExtend"}],staticClass:"min",attrs:{"edit-cate":"min"}}),t._v(" "),t.btn_hidden?t._e():s("input",{directives:[{name:"show",rawName:"v-show",value:t.isExtend,expression:"isExtend"},{name:"model",rawName:"v-model",value:t.productValue,expression:"productValue"}],staticClass:"input-val",class:"input-val"+t.goodsId,attrs:{"edit-cate":"edit",type:"tel"},domProps:{value:t.productValue},on:{change:t.inputCart,click:function(e){return e.preventDefault(),e.stopPropagation(),t.androidclick.apply(null,arguments)},input:function(e){e.target.composing||(t.productValue=e.target.value)}}}),t._v(" "),s("span",{directives:[{name:"show",rawName:"v-show",value:t.isExtend,expression:"isExtend"}],staticClass:"add addshow",attrs:{"edit-cate":"add"}},[t._v("+")]),t._v(" "),s("span",{directives:[{name:"show",rawName:"v-show",value:!t.isExtend,expression:"!isExtend"}],staticClass:"plus",attrs:{"edit-cate":"add"}})])}),[],!1,null,"3c07d9fa",null).exports},46825:function(t,e,s){"use strict";s.d(e,{Z:function(){return r}});s(56977),s(54678);var a=s(59502),i={data:function(){return{isExtend:!1,productNum:""}},filters:{fixedtwo:function(t){return parseFloat(t).toFixed(2)}},props:["dataId","uniformPrice","suggestPrice","grossMargin","status"],watch:{productNum:function(){}},methods:{showExtend:function(){var t=this;a.Z.$on("listenToChildEvent",(function(e){t.dataId==e.dataId&&(t.isExtend=e.isExtend,t.productNum=e.productValue)}))}},mounted:function(){this.showExtend()}},r=(0,s(51900).Z)(i,(function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"priceBox",attrs:{"data-id":t.dataId,productNum:t.productNum,uniformPrice:t.uniformPrice,suggestPrice:t.suggestPrice,grossMargin:t.grossMargin,status:t.status}},[t.uniformPrice?s("div",{staticClass:"control-price"},[s("span",{staticClass:"text"},[t._v("控销价")]),s("span",{staticClass:"jiage"},[t._v("¥"+t._s(t._f("fixedtwo")(t.uniformPrice)))])]):t._e(),t._v(" "),t.suggestPrice?s("div",{staticClass:"purchase-price"},[s("span",{staticClass:"text"},[t._v("零售价")]),s("span",{staticClass:"jiage"},[t._v("¥"+t._s(t._f("fixedtwo")(t.suggestPrice)))])]):t._e(),t._v(" "),t.grossMargin?s("div",{staticClass:"gross-margin"},[2!=t.status?s("span",{directives:[{name:"show",rawName:"v-show",value:t.isExtend,expression:"isExtend"}],staticClass:"maolilv"},[t._v("...")]):t._e(),t._v(" "),2==t.status?s("span",{staticClass:"maolilv"},[t._v("...")]):t._e(),t._v(" "),2!=t.status?s("div",{directives:[{name:"show",rawName:"v-show",value:!t.isExtend,expression:"!isExtend"}]},[s("span",{staticClass:"text"},[t._v("(毛利率")]),s("span",{staticClass:"jiage"},[t._v(t._s(t.grossMargin)+")")])]):t._e()]):t._e()])}),[],!1,null,"481a41d3",null).exports},70240:function(t,e,s){"use strict";s.d(e,{Z:function(){return r}});s(56977),s(54678);var a=s(59502),i={data:function(){return{isExtend:!1}},filters:{fixedtwo:function(t){return parseFloat(t).toFixed(2)}},props:["dataId","fob"],methods:{showExtend:function(){var t=this;a.Z.$on("listenToChildEvent",(function(e){t.dataId==e.dataId&&(t.isExtend=e.isExtend)}))}},mounted:function(){this.showExtend()}},r=(0,s(51900).Z)(i,(function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"priceBox2",attrs:{"data-id":t.dataId,fob:t.fob}},[t.isExtend?s("h3",{staticClass:"maolilv"},[t._v("...")]):s("h3",[t._v("¥"+t._s(t._f("fixedtwo")(t.fob)))])])}),[],!1,null,"40297276",null).exports},97173:function(t,e,s){"use strict";s.r(e),s.d(e,{default:function(){return u}});s(54747),s(92222),s(74916),s(23123),s(15306),s(91058),s(82772),s(41539),s(39714),s(24603),s(4723),s(9653),s(56977),s(54678),s(47042),s(69600);var a=s(84786),i=s(46825),r=s(67087),n=s(59502),o={props:{goodsData:Array,licenseStatus:Number,from_tab:{type:String,default:""},trackingStatus:{type:Boolean,default:!0},eventName:{type:String,default:"h5_page_ListPage_ExpStatic"}},data:function(){return{datalist:[],imgUrl:"",isExtend:"",dataId:""}},computed:{detailUrl:function(){return n.Z.detailBaseUrl}},filters:{fixedtwo:function(t){return parseFloat(t).toFixed(2)},replace:function(t){return t.replace(/-/g,".")}},components:{cartBtn:a.Z,priceBox:i.Z},methods:{getIdList:function(t){var e=[];t&&t.length>0?(t.forEach((function(t,s){e.push(t.id)})),this.getPrice(t,e)):this.datalist=t},splitData:function(t){for(var e=[],s=0,a=t.length;s<a;s+=20)e.push(t.slice(s,s+20));return e},getPrice:function(t,e){var s=this,a=this.splitData(e),i=0;a.forEach((function(e,r){s.putRequest("post","/app/marketing/discount/satisfactoryInHandPrice",{merchantId:s.merchantId,skuIds:e.join(",")}).then((function(e){if(s.$nextTick((function(){n.Z.$emit("changeloading",!1)})),"success"==e.data.status){var r=e.data.data;r&&r.length>0&&r.forEach((function(e,s){t.forEach((function(t,s){e.skuId==t.id&&(t.zheHouPrice=e.price)}))}))}++i===a.length&&t&&t.length>0&&(s.datalist=[],t.forEach((function(t,e){s.datalist.push(t)})))})).catch((function(e){s.datalist=t}))}))},showExtend:function(t){this.isExtend=t.isExtend,this.dataId=t.dataId,this.productNum=t.productValue}},mounted:function(){var t=this.goodsData;this.getIdList(t),this.imgUrl=this.imgBaseUrl,(0,r.dA)(this.eventName,{list:this.datalist},"",this.trackingStatus)},watch:{goodsData:function(t,e){this.getIdList(t),(0,r.dA)(this.eventName,{list:t},"",this.trackingStatus)}}},d=s(51900),c=(0,d.Z)(o,(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"templist"},t._l(t.datalist,(function(e,i){return a("div",{key:i,staticClass:"templist-item"},[a("a",{staticClass:"img-box",attrs:{href:t.detailUrl+"product_id="+e.id}},[a("div",{staticClass:"images"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.imgUrl+"/ybm/product/min/"+e.imageUrl,expression:"imgUrl + '/ybm/product/min/' + item.imageUrl"}],staticClass:"pic",attrs:{alt:""}}),t._v(" "),e.markerUrl&&!e.reducePrice?a("img",{staticClass:"activity-token",attrs:{src:t.imgUrl+e.markerUrl,alt:""}}):t._e(),t._v(" "),e.markerUrl&&e.reducePrice&&(1!=e.isControl||1==e.isPurchase&&1==e.isControl)?a("div",{staticClass:"mark-text"},[a("img",{attrs:{src:t.imgUrl+e.markerUrl,alt:""}}),t._v(" "),a("h4",[t._v("药采节价："+t._s(e.reducePrice))])]):t._e(),t._v(" "),e.activityTag&&e.activityTag.tagNoteBackGroupUrl?a("div",{staticClass:"biaoqian"},[a("img",{attrs:{src:t.imgUrl+e.activityTag.tagNoteBackGroupUrl,alt:""}}),t._v(" "),a("div",{staticClass:"tejia806"},[a("span",{staticClass:"discount"},[t._v("\n              "+t._s(e.activityTag.timeStr)+"\n            ")]),t._v(" "),a("div",{staticClass:"labelBox"},t._l(e.activityTag.skuTagNotes,(function(e,s){return a("span",{key:s,staticClass:"price806",style:{color:"#"+e.textColor}},[t._v(t._s(e.text))])})),0)])]):t._e()]),t._v(" "),2==e.status?a("div",{staticClass:"sold-out posmiddle"},[t._v("售罄")]):t._e()]),t._v(" "),a("div",{staticClass:"med-mesg"},[a("a",{attrs:{href:t.detailUrl+"product_id="+e.id}},[a("div",{staticClass:"commonName"},[e.activityTag&&e.activityTag.tagUrl?a("span",{staticClass:"bq-hgj"},[a("img",{attrs:{src:t.imgUrl+e.activityTag.tagUrl,alt:""}})]):t._e(),t._v(" "),1==e.agent?a("span",{staticClass:"dujia"},[t._v("独家")]):t._e(),t._v(" "),a("span",{staticClass:"name"},[t._v(t._s(e.commonName))]),t._v(" "),a("span",{staticClass:"spec"},[t._v("/"+t._s(e.spec))])]),t._v(" "),a("div",{staticClass:"factory"},[a("div",{staticClass:"chang"}),t._v(" "),a("div",{staticClass:"name"},[t._v(t._s(e.manufacturer))])]),t._v(" "),a("div",{staticClass:"effect"},[a("div",{staticClass:"xiao"}),t._v(" "),e.nearEffect?a("div",{staticClass:"name"},[t._v("\n            "+t._s(t._f("replace")(e.nearEffect))+"\n          ")]):t._e(),t._v("\n          /\n          "),e.nearEffect?a("div",{staticClass:"name"},[t._v("\n            "+t._s(t._f("replace")(e.farEffect))+"\n          ")]):t._e(),t._v(" "),a("div",{staticClass:"midPack"},[t._v(t._s(e.mediumPackageTitle))])])]),t._v(" "),a("div",{staticClass:"price-btn-box"},[1===t.licenseStatus||5===t.licenseStatus?a("div",{staticClass:"qualifications"},[t._v("\n          价格认证资质可见\n        ")]):1!=e.isPurchase&&1==e.isControl?a("div",{staticClass:"nobuy"},[t._v("\n          暂无购买权限\n        ")]):a("div",{staticClass:"price-numer"},["true"==e.isOEM&&0==e.signStatus||0==e.showAgree?a("span",{staticClass:"price-permission"},[t._v("价格签署协议可见")]):2==e.priceType?a("i",[t._v("\n            ¥"+t._s(t._f("fixedtwo")(e.skuPriceRangeList[0].price))+"~"+t._s(t._f("fixedtwo")(e.skuPriceRangeList[e.skuPriceRangeList.length-1].price))+"\n          ")]):a("div",{staticClass:"pricewapper"},[a("div",{staticClass:"price-box clearfixed"},[a("div",{staticClass:"price-two"},[a("p",[a("span",[t._v("¥"+t._s(t._f("fixedtwo")(e.fob)))]),t._v(" "),e.zheHouPrice?a("span",{staticClass:"zhekou"},[t._v(t._s(e.zheHouPrice))]):t._e()])]),t._v(" "),a("div",{staticClass:"btn-box"},[2!==e.status&&1!==t.licenseStatus&&5!==t.licenseStatus?a("div",["true"==e.isOEM&&0==e.signStatus||0==e.showAgree?a("div"):a("div",[1!=e.isControl||1==e.isPurchase&&1==e.isControl?a("cartBtn",{attrs:{"is-pack":!1,"data-id":e.id,"is-split":e.isSplit,"product-num":e.cartProductNum,"med-num":e.mediumPackageNum}}):t._e()],1)]):a("img",{staticClass:"bell",attrs:{src:s(11932),alt:""}})])]),t._v(" "),a("div",{staticClass:"control-hid clearfixed"},[1!==t.licenseStatus&&5!==t.licenseStatus?a("div",{staticClass:"control-box"},["true"!=e.isOEM&&(1!=e.isControl||1==e.isPurchase&&1==e.isControl)||"true"==e.isOEM&&1==e.signStatus?a("priceBox",{attrs:{uniformPrice:e.uniformPrice,suggestPrice:e.suggestPrice,grossMargin:e.grossMargin}}):t._e()],1):t._e()])])])]),t._v(" "),a("div",{staticClass:"label-box",class:{"label-box-hide":e.tagList.length<=0&&!(1==e.isUsableMedicalStr)}},[a("div",{staticClass:"labels"},t._l(e.tagList.slice(0,3),(function(e,s){return a("span",{key:s,class:"span"+e.uiType},[t._v(t._s(e.name))])})),0)]),t._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:0!==e.isThirdCompany,expression:"item.isThirdCompany !== 0"}],staticClass:"enter-shop"},[a("i",{staticClass:"icon"}),t._v(" "),a("span",[a("span",{staticClass:"companyName"},[t._v(t._s(e.companyName))])])])])])})),0)}),[],!1,null,"2f0b4e1d",null).exports,l={data:function(){return{top:0,imgUrl:"",tabsDataList:[],licenseStatus:0,load_key:!0,in_top:!0,event_locationY:0,event_locationX:0,reloadText:"暂无为你推荐商品，看看其他的药品吧~",is_reload:!1,bottom_loading_msg:"上拉加载",top_loading_msg:"下拉加载",need_reload:!0}},computed:{first_enter:function(){return!(this.tabsDataList.length>0)},detailUrl:function(){return n.Z.detailBaseUrl}},watch:{tabsDataList:{handler:function(t){var e=[];t.forEach((function(t){t&&t.preferredBrandBuineseDto&&t.preferredBrandBuineseDto.skuVOList&&(e=e.concat(t.preferredBrandBuineseDto.skuVOList))})),(0,r.dA)("h5_page_ListPage_ExpStatic",{list:e})},deep:!0}},components:{priceBox2:s(70240).Z,templist:c},filters:{str_to_arr:function(t,e){return t.replace(/]|"|\[/g,"").split(",")[e]}},methods:{str_to_arr:function(t,e){return t.replace(/]|"|\[/g,"").split(",")[e]},touchmove:function(t){if(this.in_top&&this.load_key){var e=Math.pow(t.targetTouches[0].pageY-this.event_locationY,.8);e>35?(this.top_loading_msg="松开加载",this.top=35):(this.top_loading_msg="下拉加载",this.top=e)}},touchStart:function(t){this.in_top&&this.load_key&&(this.event_locationY=t.targetTouches[0].pageY,this.event_locationX=t.targetTouches[0].pageX)},touchend:function(t){if(this.in_top){if(this.load_key){if(this.in_top&&35===this.top)return this.load_key=!1,this.top_loading_msg="正在加载...",void this.getCommodityData("upFreshCount");this.top_loading_msg="下拉加载",this.top=0}}else this.top=0},reloadClick:function(){n.Z.$emit("changeloading",!0),this.getCommodityData("upFreshCount")},getCommodityData:function(t){var e=this;this.putRequest("post","/app/sku/recommendedSkuForFront",{merchantId:this.merchantId,hid:1,upFreshCount:parseInt(localStorage.getItem("upFreshCount")),downFreshCount:parseInt(localStorage.getItem("downFreshCount"))}).then((function(s){if(e.top=0,e.load_key=!0,n.Z.$emit("changeloading",!1),"success"!==s.data.status)throw new Error("获取数据失败!");var a=s.data.data.rows;if(e.licenseStatus=s.data.data.licenseStatus,0===a.length&&e.first_enter)e.is_reload=!0;else{if(e.is_reload=!1,"downFreshCount"===t){var i=parseInt(localStorage.getItem("downFreshCount"))+1;localStorage.setItem("downFreshCount",i),e.tabsDataList=e.tabsDataList.concat(a)}else if("upFreshCount"===t){var r=parseInt(localStorage.getItem("upFreshCount"))+1;localStorage.setItem("upFreshCount",r),e.tabsDataList=a.concat(e.tabsDataList)}e.$nextTick((function(){e.top_loading_msg="下拉加载",e.bottom_loading_msg="上拉加载"}))}})).catch((function(t){var s,a;e.top=0,e.top_loading_msg="下拉加载",e.bottom_loading_msg="上拉加载",e.load_key=!0,n.Z.$emit("changeloading",!1),t.toString().indexOf("timeout")>-1?n.Z.$emit("changeprompt",{dialog:"请求超时!",showprompt:!0}):n.Z.$emit("changeprompt",{dialog:"数据加载失败!",showprompt:!0}),e.first_enter&&(e.is_reload=!0),e.need_reload&&(e.merchantId=(s=new RegExp("(\\?|&)"+"merchantId"+"=([^&]*)(&|$)"),null!=(a=window.location.hash.substr(2).match(s))?unescape(a[2]):null),e.getCommodityData("upFreshCount"),e.need_reload=!1)}))},gitpiwik:function(){document.title="home_recommend";var t=t||[];t.push(["trackPageView"]),t.push(["enableLinkTracking"]),t.push(["logAllContentBlocksOnPage"]);var e=this;!function(){var s="https://piwik.ybm100.com/";t.push(["setUserId",e.merchantId]),t.push(["setTrackerUrl",s+"piwik.php"]),t.push(["setSiteId","1"]);var a=document,i=a.createElement("script"),r=a.getElementsByTagName("script")[0];i.type="text/javascript",i.async=!0,i.defer=!0,i.src=s+"piwik.js",r.parentNode.insertBefore(i,r)}()}},created:function(){this.imgUrl=this.imgBaseUrl,n.Z.$emit("changeloading",!0),this.getCommodityData("upFreshCount"),this.gitpiwik(),null!==localStorage.getItem("upFreshCount")&&null!==localStorage.getItem("downFreshCount")||(localStorage.setItem("upFreshCount","1"),localStorage.setItem("downFreshCount","1"))},mounted:function(){var t=this;document.querySelector("#recomlist").addEventListener("scroll",(function(){var e=this.scrollTop;this.scrollHeight-e-document.documentElement.clientHeight<200&&t.load_key&&(t.load_key=!1,t.bottom_loading_msg="正在加载...",t.getCommodityData("downFreshCount")),this.scrollTop<2?t.in_top=!0:t.in_top=!1,this.scrollTop>800?n.Z.$emit("showBtn",{isShow:!0,dom:"#recomlist"}):n.Z.$emit("showBtn",{isShow:!1,dom:"#recomlist"})}))},activated:function(){this.setAppTitle("为你推荐")},deactivated:function(){n.Z.$emit("showBtn",{isShow:!1,dom:""})}},u=(0,d.Z)(l,(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{attrs:{id:"recomlist"}},[t.tabsDataList.length>0?a("div",{staticClass:"top_loading"},[t._v("\n    "+t._s(t.top_loading_msg)+"\n  ")]):t._e(),t._v(" "),a("div",{staticClass:"recomlist-list",style:{transform:"translate3d(0, "+t.top+"px, 0)"},on:{touchend:function(e){return t.touchend(e)},touchstart:function(e){return t.touchStart(e)},touchmove:function(e){return t.touchmove(e)}}},[a("div",{staticClass:"recomlist-cont"},t._l(t.tabsDataList,(function(e,i){return 0!==t.tabsDataList.length?a("div",{key:i,staticClass:"brands-item"},[e.skuDtoList&&e.skuDtoList.length>0?a("div",{staticClass:"recom-list"},[a("templist",{attrs:{"goods-data":e.skuDtoList,"license-status":t.licenseStatus||0,eventName:"h5_page_ListPage_Exposure"}})],1):t._e(),t._v(" "),e.preferredBrandBuineseDto?a("div",[a("div",{staticClass:"brand-list"},[a("a",{attrs:{href:"ybmpage://commonh5activity?cache=0&url=https://app-v4.ybm100.com/static/xyyvue/dist/#"+e.preferredBrandBuineseDto.appUrl}},[e.preferredBrandBuineseDto.categoryQuantity?a("div",{staticClass:"title"},[a("div",{staticClass:"brand-logo"},[a("img",{attrs:{src:t.imgUrl+"/ybm/brand/"+e.preferredBrandBuineseDto.logoImg,alt:""}})]),t._v(" "),a("div",{staticClass:"brand-msg"},[a("p",{staticClass:"brand-name"},[a("span",[t._v(t._s(e.preferredBrandBuineseDto.manufacturer))]),t._v(" "),t._m(0,!0)]),t._v(" "),e.preferredBrandBuineseDto.categoryQuantity||e.preferredBrandBuineseDto.drugsCount?a("p",{staticClass:"brand-cont"},[a("span",{staticClass:"brand-first"},[t._v("\n                      品类\n                      "),a("span",{staticClass:"brand-num"},[t._v(t._s(e.preferredBrandBuineseDto.categoryQuantity))]),t._v("种\n                    ")]),t._v(" "),a("span",[t._v("\n                      药品\n                      "),a("span",{staticClass:"brand-num"},[t._v(t._s(e.preferredBrandBuineseDto.drugsCount))]),t._v("件\n                    ")])]):t._e(),t._v(" "),e.preferredBrandBuineseDto.customLabeB1&&e.preferredBrandBuineseDto.customLabeB2?a("p",[a("span",{staticClass:"brand-label"},[a("span",[t._v(t._s(e.preferredBrandBuineseDto.customLabeB1))])]),t._v(" "),a("span",{staticClass:"brand-label"},[a("span",[t._v(t._s(e.preferredBrandBuineseDto.customLabeB2))])])]):t._e(),t._v(" "),a("div",{staticClass:"brand-label-list"},[e.preferredBrandBuineseDto.customLabeA1?a("p",[a("span",{staticClass:"label-bg"},[a("span",{style:{background:"#"+t.str_to_arr(e.preferredBrandBuineseDto.customLabeA1,2)}},[t._v(t._s(t._f("str_to_arr")(e.preferredBrandBuineseDto.customLabeA1,0)))])]),t._v(" "),a("span",{staticClass:"label-text"},[t._v(t._s(t._f("str_to_arr")(e.preferredBrandBuineseDto.customLabeA1,1)))])]):t._e(),t._v(" "),e.preferredBrandBuineseDto.customLabeA2?a("p",[a("span",{staticClass:"label-bg"},[a("span",{style:{background:"#"+t.str_to_arr(e.preferredBrandBuineseDto.customLabeA2,2)}},[t._v(t._s(t._f("str_to_arr")(e.preferredBrandBuineseDto.customLabeA2,0)))])]),t._v(" "),a("span",{staticClass:"label-text"},[t._v(t._s(t._f("str_to_arr")(e.preferredBrandBuineseDto.customLabeA2,1)))])]):t._e(),t._v(" "),e.preferredBrandBuineseDto.customLabeA3?a("p",[a("span",{staticClass:"label-bg"},[a("span",{style:{background:"#"+t.str_to_arr(e.preferredBrandBuineseDto.customLabeA3,2)}},[t._v(t._s(t._f("str_to_arr")(e.preferredBrandBuineseDto.customLabeA3,0)))])]),t._v(" "),a("span",{staticClass:"label-text"},[t._v(t._s(t._f("str_to_arr")(e.preferredBrandBuineseDto.customLabeA3,1)))])]):t._e()])])]):a("div",{staticClass:"old_brands"},[a("img",{attrs:{src:t.imgUrl+"/ybm/brand/"+e.preferredBrandBuineseDto.backgroundImg,alt:""}})])]),t._v(" "),e.preferredBrandBuineseDto.skuVOList&&e.preferredBrandBuineseDto.skuVOList.length>0?a("div",{staticClass:"commodity-list"},[a("swipe",{staticClass:"swipe",attrs:{continuous:!1,auto:0}},[t._l(Math.ceil(e.preferredBrandBuineseDto.skuVOList.length/3),(function(i){return a("swipe-item",{key:i},[t._l(e.preferredBrandBuineseDto.skuVOList.slice(3*i-3,3*i),(function(e){return a("div",{key:e.id,staticClass:"commodity-item"},[a("a",{attrs:{href:t.detailUrl+"product_id="+e.id}},[a("img",{staticClass:"commodity-img",attrs:{src:t.imgUrl+"/ybm/product/min/"+e.imageUrl,alt:""}}),t._v(" "),e.activityTag.tagNoteBackGroupUrl?a("div",{staticClass:"biaoqian"},[a("img",{attrs:{src:t.imgUrl+e.activityTag.tagNoteBackGroupUrl,alt:""}}),t._v(" "),a("div",{staticClass:"weitejia806"},t._l(e.activityTag.skuTagNotes,(function(e,s){return a("span",{key:s,staticClass:"price806",style:{color:"#"+e.textColor}},[t._v(t._s(e.text))])})),0)]):t._e(),t._v(" "),a("h2",{staticClass:"textellipsis"},[e.activityTag.tagUrl?a("span",{staticClass:"bq-hgj"},[a("img",{attrs:{src:t.imgUrl+e.activityTag.tagUrl,alt:""}})]):t._e(),t._v("\n                        "+t._s(e.commonName)+"\n                      ")]),t._v(" "),a("h4",{staticClass:"textellipsis"},[t._v(t._s(e.spec))]),t._v(" "),1!==t.licenseStatus&&5!==t.licenseStatus?a("div",{staticClass:"price-box"},[a("priceBox2",{staticClass:"fob",attrs:{"data-id":e.id,fob:e.fob}})],1):t._e()])])})),t._v(" "),a("div",{staticClass:"commodity-item"},[a("a",{staticClass:"commodity-item-link",attrs:{href:"ybmpage://commonh5activity?cache=0&url=https://app-v4.ybm100.com/static/xyyvue/dist/#"+e.preferredBrandBuineseDto.appUrl}},[a("img",{attrs:{src:s(85660),alt:""}})])])],2)})),t._v(" "),e.preferredBrandBuineseDto.skuVOList.length%3==0?a("swipe-item",[a("div",{staticClass:"commodity-item"},[a("a",{staticClass:"commodity-item-link",attrs:{href:"ybmpage://commonh5activity?cache=0&url=https://app-v4.ybm100.com/static/xyyvue/dist/#"+e.preferredBrandBuineseDto.appUrl}},[a("img",{attrs:{src:s(85660),alt:""}})])])]):t._e()],2)],1):t._e()])]):t._e()]):t._e()})),0),t._v(" "),t.tabsDataList.length>0?a("div",{staticClass:"loading-box"},[t._v("\n      "+t._s(t.bottom_loading_msg)+"\n    ")]):t._e()]),t._v(" "),0===t.tabsDataList.length&&t.is_reload?a("div",{staticClass:"reload",on:{click:t.reloadClick}},[a("img",{attrs:{src:s(81195),alt:""}}),t._v(" "),a("div",{staticClass:"reloadText"},[t._v(t._s(t.reloadText))])]):t._e()])}),[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("span",{staticClass:"brand-more"},[s("span",[t._v("更多")]),t._v(" "),s("i")])}],!1,null,null,null).exports}}]);
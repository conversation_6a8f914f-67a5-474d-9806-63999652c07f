"use strict";(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[93910],{93910:function(t,e,s){s.r(e),s.d(e,{default:function(){return n}});s(74916),s(23123);var a={name:"privacy",data:function(){return{rules:[]}},methods:{initContent:function(){this.rules="药帮忙（以下或称“我们”）尊重并保护用户（以下或称“您”）的隐私，您的信任对我们非常重要，我们深知个人信息对您的重要性，我们将按法律法规要求，采取相应安全保护措施，尽力保护您的个人信息安全可控。鉴此，我们制定本《药帮忙隐私权政策》（下称“本政策”）向您说明，我们如何为您提供访问、更新、管理和保护您的信息的服务。\n本政策与您使用我们的服务关系紧密，我们建议您仔细阅读并理解本政策全部内容，做出您认为适当的选择。我们努力用通俗易懂、简明扼要的文字表达，并将本政策中与您的权益存在重大关系的条款，采用粗体字进行标注以提示您注意。\n您使用或继续使用我们的产品或服务，访问、浏览和/或使用我们的APP和/或任何我们的线上渠道，参与我们开展的其他活动，即意味着同意我们按照本政策收集、使用、储存和分享您的相关信息。\n\n<b>本政策将帮助您了解以下内容：</b>\n<b>一、本政策中关键词定义</b>\n<b>二、权限使用说明</b>\n<b>三、我们如何收集和使用您的个人信息</b>\n<b>四、我们如何使用Cookie和同类技术</b>\n<b>五、我们如何共享、转让、公开披露您的个人信息</b>\n<b>六、我们如何保护您的个人信息</b>\n<b>七、您如何管理个人信息</b>\n<b>八、我们如何处理未成年人的个人信息</b>\n<b>九、您的个人信息如何在全球范围转移</b>\n<b>十、信息安全</b>\n<b>十一、本隐私政策如何更新</b>\n<b>十二、与其他网站的链接</b>\n<b>十三、如何联系我们</b>\n\n\n<b>一、本政策中关键词定义</b>\n（一）本政策中的“用户信息”指：以电子或者其他方式记录的能够单独或者与其他信息结合识别特定自然人身份或者反映特定自然人活动情况的各种信息。\n（二）本政策中的“药帮忙”指：武汉小药药医药科技有限公司及武汉小药药医药科技有限公司的所有关联公司。公司注册地址为：<b>武汉市东湖新技术开发区光谷大道77号金融港后台服务中心一期A2栋第9层901室、第3层301室</b>。\n（三）本政策中的“用户敏感信息”是指：我们用于识别您身份的信息要素，例如：指包括身份证件号码、用户生物识别信息、银行账号、财产信息、行踪轨迹、交易信息、14岁以下（含）儿童信息等的用户信息（我们将在本隐私权政策中对具体用户敏感信息以粗体进行显著标识）。\n（四）本政策中的“用户信息删除”是指：在实现日常业务功能所涉及的系统中去除用户信息的行为，使其保持不可被检索、访问的状态。\n\n\n<b>二、权限使用说明</b>\n在您使用药帮忙服务的过程中，我们访问您的各项权限是为了向您提供服务、优化我们的服务以及保障您的帐号安全，我们会出于本政策所述的以下目的，收集和使用您的个人信息\n<b>（一）获取访问您的位置信息权限:</b>\n用于根据位置信息为您提供个性化推荐服务\n我们会在您开启位置权限后访问获取您的位置信息，根据您的位置信息提供更契合您需求的⻚面展示、产品及/或服务，比如首⻚向您推荐热销商品及排行榜，搜索结果⻚向您推荐区域精选补货必买商品等。\n位置信息：指您开启设备定位功能并使用我们基于位置提供的相关服务时，收集的有关您位置的信息（我们仅收集您当时所处的地理位置，但不会将您各时段的位置信息进行结合以判断您的行踪轨迹），包括您通过具有定位功能的移动设备使用我们的服务时，通过GPS或WLAN等方式收集的您的地理位置信息（例如您授权的GPS位置以及WLAN接入点、蓝牙和基站等传感器信息）；您或其他用户提供的包含您所处地理位置的实时信息，例如您提供的账户信息中包含的您所在地区信息；您可以通过关闭定位功能，停止对您的地理位置信息的收集。\n\n\n<b>(二) 获取向您发送通知权限:</b>\n用于订单状态、优惠信息提醒\n为改善我们的产品和/或服务、向您提供个性化的信息搜索及交易服务，我们会根据您的订单信息，提取您的偏好特征，基于特征标签通过APP推送、短信形式向您发送以“促销活动”为内容的优惠信息。\n如果您不想接受我们给您发送的优惠信息，您可以通过手机关闭相关权限，即可在手机设置-隐私管理-权限管理中更改状态(各厂商机型设置路径可能存在不一致，用户可参考厂商设置说明)来关闭来自APP的推送消息。\n<b>(三) 获取您的相机权限</b>\n当您使用扫码、搜索商品、拍摄照片上传审核、管理等需要提供此权限。<b>请您知晓，即使您已经同意开启相机权限，我们也仅会在您主动点击客户端内上述功能时通过相机获取相关信息。</b>若您不使用以上功能时，可以不允许此权限。\n<b>(四)获取加入您的照片/相册权限:</b>\n您可在开启相册权限后使用该功能上传您的照片/图片/视频，以实现经营资质上传、拍照购物、提交售后申请或与客服沟通提供证明等功能。我们可能会通过您所上传的照片/图片来识别您需要购买的商品或服务，或使用包含您所上传照片或图片的评论信息。若您不使用该功能，可以不允许此权限。\n<b>(五)读取及写入存储器权限</b>\n读取及写入存储器权限：用于缓存您在使用我们APP过程中产生的文本、图像、视频内容。当您安装APP时，APP向您申请获取此权限，以确保APP正常运行。我们承诺仅读取后缓存必要的信息。\n<b>(六)获取⻨克⻛权限</b>\n您可以选择开启系统的⻨克⻛权限，该权限可以在您使用在线客服以及语音搜索功能时，通过发送语音进行沟通。我们会收集您在使用智能语音技术过程中录入的语音信息用于机器识别、在线交互并满足您的查询活动或咨询信息等需求（例如您在使用“在线客服”功能时，我们会根据您的需求进行信息的搜索、展示）。此项功能您可以在系统权限中关闭，一旦关闭您将无法实现在线语音交互功能，但不会影响您继续浏览我们的APP⻚面。请您知晓，即使您已同意开启⻨克⻛权限，我们也仅会在您主动点击⻨克⻛图标时通过⻨克⻛获取语音信息。\n<b>(七)基于读取电话状态的扩展功能</b>\n当您运行药帮忙APP时，我们会向您申请获取此权限，目的是<b>正常识别您的本机识别码，以便完成安全⻛控、进行统计和服务推送。</b>\n<b>(八)拨打电话功能\n当您运行药帮忙App时，<b>该权限会用于拨打客服电话的快捷方式。</b>此权限禁止后不会影响app运行，但需要拨打客服电话的操作需要用户手动完成。\n<b>(九)基于剪切板的扩展功能</b>\n<b>当您复制药口令，短信验证码或者商品信息时，分享或接收被分享信息时，您的剪切板功能可能会被调用。剪切板信息仅会在您的设备上进行处理，我们不会存储您的剪切板信息用于任何其他途径。</b>\n您理解并同意，上述附加服务可能需要您在您的设备中开启您的位置信息 （地理位置）、摄像头（相机）、相册（图片库）、读取及写入权限、读取电话状态的访问权限，以实现这些权限所涉及信息的收集和使用。您可在您的设备手机设置-隐私管理-权限管理(各厂商机型设置路径可能存在不一致，用户可参考厂商设置说明)中逐项查看上述权限的状态，并可自行决定这些权限随时的开启或关闭。<b>请您注意，您开启任一权限即代表您授权我们可以收集和使用相关个人信息来为您提供对应服务，您一旦关闭任一权限即代表您取消了授权，我们将不再基于对应权限继续收集和使用相关个人信息，也无法为您提供该权限所对应的服务。但是，您关闭权限的决定不会影响此前基于您的授权所进行的信息收集及使用。</b>\n\n\n<b>三、我们如何收集和使用您的个人信息</b>个人信息是指以电子或者其他方式记录的，能够单独或者与其他信息结合识别特定自然人身份或者反映特定自然人活动情况的各种信息。本隐私权政策涉及的个人信息包括：个人基本信息（包括个人姓名、地址、个人电话号码）；网络身份标识信息（包括登入手机账号、验证码）；个人财产信息（交易和消费记录、支付信息、订单信息）；个人设备信息（包括设备型号、设备MAC地址、操作系统类型、唯一设备识别码）；个人位置信息（包括精准定位信息）；\n个人敏感信息是指一旦泄露、非法提供或滥用可能危害人身和财产安全，极易导致个人名誉、身心健康受到损害或歧视性待遇等的个人信息，本隐私权政策涉及的个人敏感信息包括：个人财产信息（交易和消费记录、支付信息、订单信息）、网络身份标识信息（登入手机账号、验证码），其他信息（个人电话号码、精准定位），未成年人的个人信息。\n我们会出于本政策所述的以下目的，收集和使用您的个人信息\n<b>(一)帮助您成为我们的会员</b>\n为成为我们的用户，以便我们为您提供服务，您需要提供手机号码和地址信息，并创建用户。在您成为注册用户后，我们还会在您使用此帐户及/或参与帐户所涉活动的过程中持续地向您收集相关信息。\n在您主动注销账号时，我们将根据适用法律法规的要求尽快使其匿名或删除您的个人信息。\n<b>(二)为您展示和推送商品或服务</b>\n为改善我们的产品和/或服务、向您提供个性化的信息搜索及交易服务，我们会根据您的订单信息，提取您的常购商品，通过APP推送的形式向您发送促销优惠信息。\n如果您不想接受我们给您发送的优惠信息，您可以通过手机关闭相关权限，即可在手机设置-隐私管理-权限管理中更改状态(各厂商机型设置路径可能存在不一致，用户可参考厂商设置说明)来关闭来自APP的推送消息。\n<b>(三)满足您的使用需求:</b>\n<b>1、使用我们的App</b>\n如果您使用我们的APP，我们会向您收集相关信息，包括您的用户名、电话号码、收货地址、或我们APP的使用信息（包括：操作系统信息、您的地理位置信息）以及您登录我们APP所使用的设备信息（例如：设备的序列号、唯一设备标识符以及移动网络信息）。\n<b>2、我们收集个人信息的使用方式、目的及范围</b>\na 注册成为我们的用户，填写用户名称、填写手机号码方便您登录APP，通过获取申请图片的权限方便您上传企业资质，审核通过方便您下单；\nb 接受和处理您的订单并交付相应产品和/或服务；\nc 方便您通过第三方支付平台或者其他金融机构完成订单支付并为您提供相关的售后服务；\nd 为您展示所在地区相关产品与优惠信息；\ne 处理和分析您购买、使用的产品和/或服务的信息和消费行为，包括通过记录在您注册帐户中的消费信息设计个性化的促销活动；\nf 处理您就药帮忙产品和/或服务、药帮忙APP或任何其他促销活动提出的查询、疑问及投诉，并为您进行提供相关的售后服务；\ng 通过您所注册的账号或其他联系方式，向您发送产品优惠券；\nh 根据您的要求和您提供的有关信息，为您开具发票；\n您可以拒绝向药帮忙提供您的个人信息。但在这种情况下，我们可能无法向您提供您所需要的某些产品和/或服务，或者您可能无法使用App的部分功能。例如，您在注册药帮忙帐户时，您只有在同意接受本政策项下有关个人信息使用的规定时，您的帐户才可以注册成功。\n若药帮忙有需要将您的个人信息用于本政策未载明的其他目的或用途时，我们会在使用和处理您的个人信息前另行征求您的同意（但法律法规另有不同规定的情形除外）。\n<b>(四)向您提供商品或服务</b>\n<b>1、您向我们提供的信息</b>\n当您在我们的APP中订购具体产品和/或服务时，我们会通过系统为您生成购买该产品和/或服务的订单。在下单过程中，您需提供<b>收货人姓名、收货地址、收货人联系电话</b>，同时该订单中会载明您购买的产品名称、支付金额和订单创建的日期，我们收集这些信息是为了帮助您顺利完成交易、保障您的交易安全、提供客服与售后服务及其他我们明确告知的目的。如果我们委托第三方向您交付相关产品和/或服务时，为了保证产品和/或服务能够顺利、安全、准确送达，我们会在征得您同意后将上述信息共享给第三方，您知晓并同意相应物流主体不可避免的获得及使用您的配送信息，用于完成交付目的。如果您拒绝提供此类信息，我们将无法完成相关交付服务。\n为完成订单支付，您需要提供第三方支付账号（包括支付宝、花呗支付、银联支付、微信支付、广发白条）并选择付款方式，以便我们了解您的支付状态。您同意我们可从你选择的第三方支付平台（包括支付宝、花呗支付、银联支付、微信支付、广发白条）处收集与支付相关的信息；如果您使用其他金融机构为您提供支付服务(包括国际信用卡支付)，需要您填写银行卡号、安全码、有效期在内的银行卡支付必要信息来完成支付，为使我们及时获悉并确认您的支付进度和状态，为您提供售后与争议解决服务。\n<b>2、我们在您使用服务过程中收集的信息</b>\n为向您提供更契合您需求的⻚面展示和搜索结果、了解产品适配性、识别账号异常状态，我们会收集关于您使用的服务以及使用方式的信息并将这些信息进行关联，这些信息包括：\n<b>设备信息:</b>我们会根据您在软件安装及使用中授予的具体权限， 接收并记录您所使用的设备相关信息(例如设备型号、操作系统版本、设备设置、唯一设备标识符、使用的移动应用和其他软件信息等软硬件特征信息)。\n<b>日志信息:</b>当您使用我们的客户端提供的产品或服务时，我们会自动收集您对我们服务的详细使用情况，作为有关网络日志保存。例如您的搜索查询内容、IP地址浏览器的类型、电信运营商、使用的语言、访问日期和时间及您访问的网⻚记录等。\n<b>位置信息:</b>指您开启设备定位功能并使用我们基于位置提供的相关服务时，收集的有关您位置的信息（我们仅收集您当时所处的地理位置，但不会将您各时段的位置信息进行结合以判断您的行踪轨迹），包括您通过具有定位功能的移动设备使用我们的服务时，通过GPS或WLAN等方式收集的您的地理位置信息（例如IP地址、GPS位置以及能够提供相关信息的WLAN接入点、蓝牙和基站等传感器信息）；您或其他用户提供的包含您所处地理位置的实时信息，例如您提供的账户信息中包含的您所在地区信息；您可以通过关闭定位功能，停止对您的地理位置信息的收集。\n请注意，单独的设备信息、日志信息、位置信息等是无法识别特定自然人身份的信息。如果我们将这类非个人信息与其他信息结合用于识別特定自然人身份，或者将其与个人信息结合使用，则在结合使用期间，这类非个人信息将被视为个人信息，除取得您授权或法律法规另有规定外我们会将该类个人信息做匿名化、去标识化处理。\n为展示您账户的订单信息，我们会收集您在使用我们服务过程中产生的订单信息用于向您展示及便于您对订单进行管理。\n当您与我们联系时，我们可能会保存您的通信／通话记录和内容或您留下的联系方式等信息，以便与您联系或帮助您解決问题，或记录相关问题的处理方案及结果。\n<b>3、我们通过间接获得方式收集到的您的个人信息为确认交易状态及为您提供售后解决服务，我们会将您的交易信息共享给必要的支付、配送等服务提供者。当您通过我们产品或服务使用上述服务时，您授权我们根据实际业务及合作需要从我们关联公司处接收、汇总、分析我们确认其来源合法或您授权同意其向我们提供的您的个人信息或交易信息。</b>\n<b>4、应您的要求开具发票</b>\n如果您向我们申请开具发票，我们会向您收集相关信息，包括您的电子邮箱、姓名、开票单位信息（包括单位名称、纳税人识别号、注册地址、注册电话、开户银行、银行账号等）、购买日期和支付的金额等。\n<b>(五)您参与调查、互动或其他</b>\n如果您参与了一项调查或与我们以其他各种方式互动，我们会在征得您同意的基础上，向您收集您的相关信息，包括您的用户名、收入、婚姻状态、工作、教育背景等人口统计信息以及与您可能感兴趣的主题相关的信息。\n我们也可能通过与某个特定事件或活动相关的其他信息（例如为申请在我们工作而在线提交的简历）获取您的信息。\n<b>(六)为您提供安全保障</b>\n为了提高您使用服务的安全性，防止您用户信息被不法分子获取，我们需要记录您使用的服务类别、方式及相关操作信息，例如：设备型号、IP地址、设备软件版本信息、设备识别码、设备标识符、所在位置、网络使用习惯以及其他与服务相关的日志信息。\n为提高您使用我们及我们关联公司、合作伙伴提供服务的安全性，保护您或其他用户或公众的人身财产安全免遭侵害，更好地预防钓⻥网站、欺诈、网络漏洞、计算机病毒、网络攻击、网络侵入等安全⻛险，更准确地识别违反法律法规或药帮忙相关协议规则的情况，我们可能使用或整合您的会员信息、交易信息、设备信息、有关网络日志以及我们关联公司、合作伙伴取得您授权或依据法律共享的信息，来综合判断您账号及交易⻛险、进行身份验证、检测及防范安全事件，并依法采取必要的记录、审计、分析、处置措施。\n<b>(七)其他用途</b>\n我们将信息用于本政策未载明的其他用途，或者将基于特定目的收集而来的信息用于其他目的时，会事先征求您的同意。\n<b>(八)征得授权同意的例外</b>\n您充分理解并同意，我们在以下情况下收集、使用您的用户信息无需您的授权同意，且我们可能不会响应您提出的更正/修改、删除、注销、撤回同意、索取信息的请求\n1、与国家安全、国防安全有关的；\n2、与公共安全、公共卫生、重大公共利益有关的；\n3、与犯罪侦查、起诉、审判和判決执行等有关的；\n4、出于维护个人信息主体或其他个人的生命、财产等重大合法权益但又很难得到您本人同意的；\n5、所收集的个人信息是您自行向社会公众公开的；\n6、从合法公开披露的信息中收集个人信息的，如合法的新闻报道、政府信息公开等渠道；\n7、根据您的要求签订合同所必需的；\n8、用于维护所提供的产品及/或服务的安全稳定运行所必需的，例如发现、处置产品或服务的故障；\n9、为合法的新闻报道所必需的；\n10、学术研究机构基于公共利益开展统计或学术研究所必要，且对外提供学术研究或描述的结果时，对结果中所包含的个人信息进行去标识化处理的；\n11、法律法规规定的其他情形。\n请知悉，根据相关法律规定，若我们对个人信息采取技术措施和其他必要措施处理，使得数据接收方法无法重新识别特定个人且不能复原，或我们可能会对收集的信息进行去标识化的研究、统计分析和预测，用于改善瑞幸APP的内容和布局，为商业决策提供产品和服务支撑，以及改进我们的产品和服务（包括使用匿名数据进行机器学习或模型算法训练），则此类处理后数据的使用无需另行向您通知并征得您的同意。\n如我们停止运营药帮忙产品或服务，我们将及时停止继续收集您个人信息的活动，将停止运营的通知以逐一送达或公告的形式通知您，对所持有的个人信息进行删除或匿名化处理。\n\n\n<b>四、我们如何使用Cookie、Beacon、Proxy等技术</b>\n<b>(一)Cookie 的使用</b>\n为使您获得更轻松的访问体验，您访问我们的网站或使用各项服务时，我们可能会通过小型数据文件识别您的身份，这么做可帮您省去重复输入注册信息的步骤，或者帮助判断您的账户安全状态。这些数据文件可能是Cookie，Flash Cookie，您的浏览器或关联应用程序提供的其他本地存储（以下简称“Cookie”）。请您理解，某些服务只能通过使用Cookie才可得到实现。如您的浏览器或浏览器附加服务允许，您可以修改对Cookie的接受程度或者拒绝相关Cookie。多数浏览器工具条中的“帮助”部分会告诉您怎样防止您的浏览器接受新的Cookie，怎样让您的浏览器在您收到一条新Cookie时通知您或者怎样彻底关闭Cookie。此外，您可以通过改变浏览器附加程序的设置，或通过访问提供商的网页，来关闭或删除浏览器附加程序使用的类似数据（例如：Flash Cookie）。但这一举动在某些情况下可能会影响您安全访问我们的网站和使用相关服务。\n<b>(二)网络Beacon和同类技术的使用</b>\n除Cookie外，我们网站上还可能包含一些电子图像（以下简称“单像素GIF文件”或“网络Beacon”），使用网络Beacon可以帮助网站计算浏览网⻚的用户或访问某些Cookie，我们会通过网络Beacon收集您浏览网⻚活动的信息，例如：您访问的⻚面地址、您先前访问的援引⻚面的位置、您的浏览环境以及显示设定。如您通过我们的网站或APP，使用了由第三方而非我们的提供的服务时，我们无法保证这些第三方会按照我们的要求采取保护措施，为尽力确保您的账号安全，使您获得更安全的访问体验，我们可能会使用专用的网络协议及代理技术（以下简称“专用网络通道”或“网络代理”）。使用专用网络通道，可以帮助您识别到我们已知的高⻛险站点，减少由此引起的钓⻥、账号泄露等⻛险，同时更有利于保障您和第三方的共同权益，阻止不法分子篡改您和您希望访问的第三方之间正常服务内容，例如：不安全路由器、非法基站等引起的广告注入、非法内容篡改等。在此过程中，我们也可能会获得和保存关于您计算机的相关信息，例如：IP地址、硬件ID。\n\n\n<b>五、我们如何共享、转让、公开披露您的个人信息</b>\n<b>(一)共享</b>\n我们不会与药帮忙服务提供者以外的公司、组织和用户共享您的用户信息，但以下情况除外:\n1、我们承诺对您的信息进行保密。除法律法规及监管部⻔另有规定外，我们仅在以下情形中与第三方共享您的信息，第三方包括关联公司、供应商、服务提供商、合作金融机构以及其他合作伙伴。在将信息提供给第三方前，我们将尽商业上合理的努力评估该第三方收集信息的合法性、正当性、必要性。我们会与第三方签订相关法律文件并要求第三方处理您的用户信息时遵守法律法规，要求第三方对您的信息采取保护措施。\n2、为了提升服务效率、降低服务成本或提高服务质量，某些产品或服务可能由第三方提供或由我们与第三方共同提供，我们也可能会委托专业服务机构提供协助，因此，为了更好的客户服务和用户体验，共享您的信息。\n3、如您选择参与我们和第三方联合开展的抽奖、竞赛或类似推广活动，我们可能与其共享活动过程中产生的、为完成活动所必要的信息，以便第三方能及时向您发放奖品或为您提供服务，我们会依据法律法规或国家标准的要求，在活动规则⻚面或通过其他途径向您明确告知需要向第三方提供何种用户信息。\n4、事先获得您明确同意的情况下，我们会在法律法规允许且不违背公序良俗的范围内，依据您的授权范围与第三方共享您的信息。\n5、您主动选择情况下共享：您通过药帮忙平台购买产品或服务，我们会根据您的选择，将您订单信息中与交易有关的必要信息共享给相关商品或服务提供者，以实现您交易及售后服务需求。\n6、在不透露单个用户用户信息资料的前提下，为了给用户提供更好的服务，药帮忙可能会对整个用户数据库进行分析并对用户数据库进行商业上的利用（包括但不限于公布、分析或以其它合法方式使用用户访问量、访问时段、用户偏好等用户数据信息）。\n7、在法定情形下的共享：我们可能会根据法律法规规定、诉讼争议解决需要，或按行政、司法机关依法提出的要求，对外共享您的用户信息。\n8、与关联公司间共享:<b>为便于我们基于药帮忙账户向您提供产品和服务，推荐您可能感兴趣的信息，识别会员账号异常，保护药帮忙关联公司或其他用户或公众的人身财产安全免遭侵害，您的用 户信息可能会与我们的关联公司和/或其指定的服务提供商共享。</b>我们只会共享必要的用户信息，且受本隐私政策中所声明目的的约束，如果我们共享您的用户敏感信息或关联公司改变用户信息的使用及处理目的，将再次征求您的授权同意。\n9、与授权合作伙伴共享:我们可能会向授权合作伙伴等第三方共享您的订单信息、账户信息、设备信息以及位置信息，以保障为您提供的服务顺利完成。但我们仅会出于合法、正当、必要、特定、明确的目的共享您的用户信息，并且只会共享提供服务所必要的用户信息。我们的合作伙伴无权将共享的用户信息用于任何其他用途。我们会要求授权合作伙伴根据我们的指示并遵循本政策以及其他任何相应的保密和安全措施来为我们处理这些信息。对于涉及儿童用户信息的，我们不允许合作伙伴进行转委托。\n目前，我们的授权合作伙伴包括以下类型:\n广告、分析服务类的授权合作伙伴，除非得到您的许可，否则我们不会将您的用户身份信息与提供广告、分析服务的合作伙伴共享。我们会委托这些合作伙伴处理与广告覆盖面和有效性相关的信息，但不会提供您的用户身份信息，或者我们将这些信息进行去标识化处理，以便它不会识别您用户。这类合作伙伴可能将上述信息与他们合法获取的其他数据相结合，以执行我们委托的广告服务或决策建议。\n供应商、服务提供商和其他合作伙伴:我们业务的供应商、服务提供商和其他合作伙伴，这些支持包括受我们委托提供的技术基础设施服务、分析我们服务的使用方式、衡量广告和服务的有效性、提供客户服务、支付便利或进行学术研究和调查。\n需要说明的是，我们会对授权合作伙伴获取有关信息的应用程序接口(API)、软件工具开发包(SDK)进行严格的安全检测，并与授权合作伙伴约定严格的数据保护措施，令其按照我们的委托目的、服务说明、本隐私权政策以及其他任何相关的保密和安全措施来处理个人信息。\n<table class='relative-table confluenceTable tablesorter tablesorter-default stickyTableHeaders' style='text-align: center; width: 100%; padding: 0px;' role='grid' resolved=''><thead class='tableFloatingHeaderOriginal'><tr role='row' class='tablesorter-headerRow'><th class='confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted' data-column='0' tabindex='0' scope='col' role='columnheader' aria-disabled='false' unselectable='on' aria-sort='none' aria-label='第三方SDK名称: No sort applied, activate to apply an ascending sort' style='user-select: none;'><div class='tablesorter-header-inner'><p><strong>第三方SDK名称</strong></p></div></th><th class='confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted' data-column='1' tabindex='0' scope='col' role='columnheader' aria-disabled='false' unselectable='on' aria-sort='none' aria-label='所属公司: No sort applied, activate to apply an ascending sort' style='user-select: none;'><div class='tablesorter-header-inner'><p><strong>所属公司</strong></p></div></th><th class='confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted' data-column='2' tabindex='0' scope='col' role='columnheader' aria-disabled='false' unselectable='on' aria-sort='none' aria-label='信息获取: No sort applied, activate to apply an ascending sort' style='user-select: none;'><div class='tablesorter-header-inner'><p><strong>信息获取</strong></p></div></th><th class='confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted' data-column='3' tabindex='0' scope='col' role='columnheader' aria-disabled='false' unselectable='on' aria-sort='none' aria-label='使用目的: No sort applied, activate to apply an ascending sort' style='user-select: none;'><div class='tablesorter-header-inner'><p><strong>使用目的</strong></p></div></th><th class='confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted' data-column='4' tabindex='0' scope='col' role='columnheader' aria-disabled='false' unselectable='on' aria-sort='none' aria-label='隐私协议链接或 sdk url: No sort applied, activate to apply an ascending sort' style='user-select: none;'><div class='tablesorter-header-inner'><p><strong>隐私协议链接或 sdk url</strong></p></div></th></tr></thead><thead class='tableFloatingHeader' style='display: none;'><tr role='row' class='tablesorter-headerRow'><th class='confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted' data-column='0' tabindex='0' scope='col' role='columnheader' aria-disabled='false' unselectable='on' aria-sort='none' aria-label='第三方SDK名称: No sort applied, activate to apply an ascending sort' style='user-select: none;'><div class='tablesorter-header-inner'><p><strong>第三方SDK名称</strong></p></div></th><th class='confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted' data-column='1' tabindex='0' scope='col' role='columnheader' aria-disabled='false' unselectable='on' aria-sort='none' aria-label='所属公司: No sort applied, activate to apply an ascending sort' style='user-select: none;'><div class='tablesorter-header-inner'><p><strong>所属公司</strong></p></div></th><th class='confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted' data-column='2' tabindex='0' scope='col' role='columnheader' aria-disabled='false' unselectable='on' aria-sort='none' aria-label='信息获取: No sort applied, activate to apply an ascending sort' style='user-select: none;'><div class='tablesorter-header-inner'><p><strong>信息获取</strong></p></div></th><th class='confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted' data-column='3' tabindex='0' scope='col' role='columnheader' aria-disabled='false' unselectable='on' aria-sort='none' aria-label='使用目的: No sort applied, activate to apply an ascending sort' style='user-select: none;'><div class='tablesorter-header-inner'><p><strong>使用目的</strong></p></div></th><th class='confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted' data-column='4' tabindex='0' scope='col' role='columnheader' aria-disabled='false' unselectable='on' aria-sort='none' aria-label='隐私协议链接或 sdk url: No sort applied, activate to apply an ascending sort' style='user-select: none;'><div class='tablesorter-header-inner'><p><strong>隐私协议链接或 sdk url</strong></p></div></th></tr></thead><tbody aria-live='polite' aria-relevant='all'><tr role='row'><td colspan='1' class='confluenceTd'>极光推送</td><td colspan='1' class='confluenceTd'>极光</td><td colspan='1' class='confluenceTd'><span>外部存储状态，网络类型</span></td><td colspan='1' class='confluenceTd'>消息推送</td><td colspan='1' class='confluenceTd'><a class='external-link' href='https://docs.jiguang.cn/jpush/guideline/intro/' rel='nofollow'>极光推送 - JPush 产品简介 - 极光文档 (jiguang.cn)</a></td></tr><tr role='row'><td colspan='1' class='confluenceTd'>华为推送</td><td colspan='1' class='confluenceTd'>华为</td><td colspan='1' class='confluenceTd'><span>外部存储状态，网络类型</span></td><td colspan='1' class='confluenceTd'><span>消息推送</span></td><td colspan='1' class='confluenceTd'><a class='external-link' href='https://developer.huawei.com/consumer/cn/doc/development/HMSCore-Guides/service-introduction-0000001050040060' rel='nofollow'>推送服务-产品说明 (huawei.com)</a></td></tr><tr role='row'><td colspan='1' class='confluenceTd'>小米推送</td><td colspan='1' class='confluenceTd'>小米</td><td colspan='1' class='confluenceTd'><span>外部存储状态，网络类型</span></td><td colspan='1' class='confluenceTd'><span>消息推送</span></td><td colspan='1' class='confluenceTd'><a class='external-link' href='https://dev.mi.com/console/doc/detail?pId=68' rel='nofollow'>文档中心 (mi.com)</a></td></tr><tr role='row'><td colspan='1' class='confluenceTd'>阿里云http DNS</td><td colspan='1' class='confluenceTd'>阿里云</td><td colspan='1' class='confluenceTd'><span>外部存储状态，网络类型</span></td><td colspan='1' class='confluenceTd'>dns解析</td><td colspan='1' class='confluenceTd'><a class='external-link' href='https://help.aliyun.com/document_detail/150879.html' rel='nofollow'>Android SDK接入 - HTTPDNS - 阿里云 (aliyun.com)</a></td></tr><tr role='row'><td colspan='1' class='confluenceTd'>百度地图</td><td colspan='1' class='confluenceTd'>百度</td><td colspan='1' class='confluenceTd'><span>网络，</span><span>外部存储状态，网络类型，ip地址，定位</span></td><td colspan='1' class='confluenceTd'>定位</td><td colspan='1' class='confluenceTd'><a class='external-link' href='https://lbsyun.baidu.com/index.php?title=androidsdk' rel='nofollow'>Android地图SDK | 百度地图API SDK (baidu.com)</a></td></tr><tr role='row'><td class='confluenceTd'><p>腾讯播放器</p></td><td class='confluenceTd'><p>腾讯</p></td><td class='confluenceTd'><p>系统语言，网络类型，ANDROID_ID，IP 地址，相册</p></td><td class='confluenceTd'><p>直播服务</p></td><td class='confluenceTd'><p><a class='external-link' href='https://cloud.tencent.com/document/product/301/11470' rel='nofollow'>https://cloud.tencent.com/document/product/301/11470</a></p></td></tr><tr role='row'><td class='confluenceTd'><p>bugly</p></td><td class='confluenceTd'><p>腾讯</p></td><td class='confluenceTd'><p>系统唯一标识符，SIM 卡序列号，ANDROID_ID</p></td><td class='confluenceTd'><p>收集崩溃信息</p></td><td class='confluenceTd'><p><a class='external-link' href='https://www.qq.com/contract.shtml' rel='nofollow'>https://www.qq.com/contract.shtml</a></p></td></tr><tr role='row'><td colspan='1' class='confluenceTd'>科大讯飞</td><td colspan='1' class='confluenceTd'>科大讯飞</td><td colspan='1' class='confluenceTd'><span>外部存储状态，网络类型，设备型号，设备制造商，录音权限，收集信息权限</span></td><td colspan='1' class='confluenceTd'>语音识别</td><td colspan='1' class='confluenceTd'><a class='external-link' href='https://www.xfyun.cn/doc/asr/voicedictation/Android-SDK.html' rel='nofollow'>语音听写 Android SDK 文档 | 讯飞开放平台文档中心 (xfyun.cn)</a></td></tr><tr role='row'><td colspan='1' class='confluenceTd'><span style='color: rgb(76,76,76);'>腾讯浏览服务</span></td><td colspan='1' class='confluenceTd'>腾讯</td><td colspan='1' class='confluenceTd'>网络，<span>外部存储状态，网络类型，设备型号，设备制造商</span></td><td colspan='1' class='confluenceTd'>内置浏览器</td><td colspan='1' class='confluenceTd'><a class='external-link' href='https://x5.tencent.com/docs/access.html' rel='nofollow'>腾讯浏览服务 (tencent.com)</a></td></tr><tr role='row'><td colspan='1' class='confluenceTd'>微信支付</td><td colspan='1' class='confluenceTd'>腾讯</td><td colspan='1' class='confluenceTd'><span>网络，</span><span>外部存储状态</span></td><td colspan='1' class='confluenceTd'>微信支付</td><td colspan='1' class='confluenceTd'><a class='external-link' href='https://pay.weixin.qq.com/wiki/doc/api/app/app.php?chapter=11_1' rel='nofollow'>https://pay.weixin.qq.com/wiki/doc/api/app/app.php?chapter=11_1</a></td></tr><tr role='row'><td class='confluenceTd'><p>阿里支付</p></td><td class='confluenceTd'><p>支付宝</p></td><td class='confluenceTd'><p>系统语言，系统唯一标识符，外部存储状态，网络类型，设备型号，设备制造商，SIM 卡序列号，IMEI，MAC 地址，ANDROID_ID，IP 地址，WiFi 信息，应用安装列表，OpenUDID</p></td><td class='confluenceTd'><p>支付宝支付</p></td><td class='confluenceTd'><p><a class='external-link' href='https://render.alipay.com/p/c/k2cx0tg8' rel='nofollow'>https://render.alipay.com/p/c/k2cx0tg8</a></p></td></tr><tr role='row'><td class='confluenceTd'><p>银联支付</p></td><td class='confluenceTd'><p>银联</p></td><td class='confluenceTd'><p>网络位置信息，网络类型，手机号码，SIM 卡状态，IMEI，MAC 地址，WiFi 信息</p></td><td class='confluenceTd'><p>银联支付</p></td><td class='confluenceTd'><p><a class='external-link' href='https://www.chinaums.com/chinaums/dsfdy/qmfapplb/201708/t20170828_25547.shtml' rel='nofollow'>https://www.chinaums.com/chinaums/dsfdy/qmfapplb/201708/t20170828_25547.shtml</a></p></td></tr></tbody></table><b>(二)转让</b>\n我们不会将您的用户信息转让给任何公司、组织和用户，但以下情况除外:\n1、在获取明确同意的情況下转让:获得您的明确同意后，我们会向其他方转让您的个人信息。\n2、根据法律法规或强制性的行政或司法要求。\n3、转让经去标识化处理的用户信息，且确保数据接收方无法重新识别或者关联用户信息主体。\n4、在涉及资产转让、收购、兼并、重组或破产时，如涉及到用户信息转让，我们会向您告知有关情况，并要求新的持有您用户信息的公司、组织继续受本政策的约束。如变更用户信息使用目的时，我们将要求该公司、组织重新取得您的明示同意。如破产且无承接方的，我们将对数据做删除处理。\n<b>(三)公开披露</b>\n我们仅在以下情况下，公开披露您的用户信息：\n1、在公布中奖活动名单时会脱敏展示中奖者手机号或账户登录名。\n2、获得您明确同意或基于您的主动选择，我们可能会公开披露您的用户信息。\n3、如果我们确定您出现违反法律法规或严重违反药帮忙相关协议规则的情况，或为保护药帮忙及其关联公司用户或公众的人身财产安全免遭侵害，我们可能会依据法律法规或药帮忙相关协议规则披露关于您的用户信息及您的店铺主体信息与处罚情况。\n4、基于法律的披露：在法律、法律程序、诉讼或政府主管部⻔强制性要求的情況下，我们可能会公开披露您的个人信息。\n5、出于维护国家安全、国防安全、公共安全、公共卫生、重大公共利益有关的目的。\n除以上情形外，原则上我们不会将您的信息进行公开披露。如确需公开披露时，我们会向您告知并征得您的同意。\n<b>（四）委托处理</b>\n为了提升信息处理效率，降低信息处理成本，或提高信息处理准确性，我们可能会委托有能力的关联公司或其他专业机构代表我们来处理用户信息，但我们会通过书面协议、现场审计等方式要求受托公司遵守严格的保密义务及采取有效的保密措施，禁止其将这些信息用于未经您授权的用途。在委托关系解除时，要求受托公司不再保存用户信息。\n<b>（五）共享、转让、公开披露个人信息时事先征得授权同意的例外</b>\n以下情形中，共享、转让、公开披露您的个人信息无需事先征得您的授权同意：\n1、与国家安全、国防安全有关的；\n2、与公共安全、公共卫生、重大公共利益有关的；\n3、与犯罪侦査、起诉、审判和判決执行等有关的；\n4、出于维护您或其他个人的生命、财产等重大合法权益但又很难得到本人同意的；\n5、您自行向社会公众公开的个人信息；\n6、从合法公开披露的信息中收集个人信息的，如合法的不属于个人信息的对外共享、转让及公开披露行为，对此类数据的保存及处理将无需另行向您通知并征得您的同意。\n请知悉，根据现行有效的法律规定，<b>若我们对个人信息采取技术措施和其他必要措施进行处理，使得数据接收方无法重新识别特定个人且不能复原，则此类处理后数据的共享、转让、公开披露无需另行向您通知并征得您的同意。</b>\n\n\n<b>六、我们如何保护您的个人信息</b>\n(一)我们已采取符合业界标准、合理可行的安全防护措施保护您提供的个人信息安全，防止个人信息遭到未经授权访问、公开披露、使用、修改、损坏或丢失。例如，在您的浏览器与服务器之间交換数据时受SSL协议加密保护;我们会使用加密技术提高个人信息的安全性;我们会使用受信赖的保护机制防止个人信息遭到恶意攻击;我们会部署访问控制机制，尽力确保只有授权入员才可访问个人信息;以及我们会举办安全和隐私保护培训课程，加强员工对于保护个人信息重要性的认识。\n(二)我们有行业先进的以数据为核心，围绕数据生命周期进行的数据安全管理体系，从组织建设、制度设计、人员管理、产品技术等方面多维度提升整个系统的安全性。目前，我们的重要信息系统已经通过网络安全等级保护三级认证。\n(三)我们只会在达成本政策所述目的所需的期限內保留您的个人信息，<b>除非延⻓保留期征得您的同意或法律有强制的存留要求。</b>\n(四)互联网并非绝对安全的环境，我们强烈建议您不要使用非药帮忙推荐的通信方式发送您的信息。如您发现自己的个人信息尤其是您的账户或密码发生泄露，请您立即联络药帮忙客服，以便我们根据您的申请采取相应措施。\n请注意，您在使用我们服务时自愿共享甚至公开分享的信息，可能会涉及您或他人的个人信息甚至个人敏感信息，如您在评价时选择上传包含个人信息的图片。请您更加谨慎地考虑，是否在使用我们的服务时共享甚至公开分享相关信息。\n请协助我们保证您的账号安全。我们将尽力保障您发送给我们的任何信息的安全性。如果我们的物理、技术或管理防护设施遭到破坏，导致信息被非授权访问、公开披露、簒改或毀坏，导致您的合法权益受损，我们将承担相应的法律责任。\n(五)在不幸发生个人信息安全事件后，我们将按照法律法规的要求向您告知：安全事件的基本情况和可能的影响、我们已采取或将要采取的处置措施、您可自主防范和降低⻛险的建议、对您的补救措施等。事件相关情況我们将以邮件、信函、电话、推送通知等方式告知您，难以逐一告知个人信息主体时，我们会采取合理、有效的方式发布公告。\n同时，我们还将按照监管部⻔要求，上报个人信息安全事件的处置情况。\n\n\n<b>七、您如何管理您的个人信息</b>\n我们将尽一切可能采取适当的技术手段，保证您可以访问、更新、更正和删除自己的注册信息或使用我们的服务时提供的其他个人信息。<b>在访问、更新、更正和删除前述信息时，我们可能会要求您进行身份验证，以保障账户安全。您有权访问、更新、更正和删除自己的信息(包括用户名、性别、电子邮箱、支付渠道信息、邮寄地址、偏好语言以及与帐户或其相关活动的运作有关的您自行提供的信息)，改变授权范围并保护自己的隐私和安全。</b>\n<b>(一)访问您的个人信息</b>\n除法律法规规定的例外情况，无论您何时使用我们的服务，我们都会力求让您顺利访问自己的个人信息。您可以通过如下方式行使您的访问权利\n1.帐户信息:如果您希望访问或编辑您的帐户中的个人资料信息、添加安全信息等，您可以在您使用的产品中执行此类操作。\n2.订单信息:您可以在我们系统中查阅或清除您的订单记录，交易记录及发票记录等。\n<b>(二)更正或补充您的个人信息</b>\n当您需要更新您的个人信息时，或发现我们处理您的个人信息有错误时，您有权作出更正或更新。您可以自行在我们系统内进行更正，或通过反馈与报错等将您的更正申请提交给我们。在您进行更新或更正前，我们可能会先要求您验证自己的身份，然后再处理您的请求。\n<b>(三)删除您的个人信息</b>\n除法律法规规定的情形外，您可以向我们提出删除个人信息的请求。若我们响应您的删除请求，我们还将同时尽可能通知从我们处获得您的个人信息的主体，要求其及时删除。但如果这些主体已经获得您独立/单独的授权，需要您另行向该主体提出请求。\n您确认知悉:<b>当您从我们的服务中删除信息后，由于法律和安全技术等原因，我们可能会不能立即从备份系统中删除相应的信息， 我们将安全的存储您的个人信息并将其与任何进一步处理隔离，直到备份可以清除或实现匿名。</b>\n<b>(四)改变您授权同意的范围或注销账户</b>\n1.您可以通过修改个人资料、关闭部分功能等方式撤回部分授权。当您撤回同意或授权后，我们无法继续为您提供撤回同意或授权所对应的服务，也将不再处理您相应的个人信息。<b>但您撤回同意或授权的決定，不会影响此前基于您的同意或授权而开展的个人信息处理。</b>\n2.您可以通过注销账户的方式停止授权。如您确认需注销账 户，您可在本APP上申请注销账户。我们将审核您的账户是否符合下列注销条件:\na)账户内没有未完成的订单，且所有订单完成均在6个月之前;\nb)该账户没有被盗、被封⻛险及被限制的记录，最近一个月没有发生更改注册手机号、更改密码等敏感操作;\nc)该账户没有未处理完成的举报或被投诉，且举报或被投诉均应发生在6个月之前;\nd)该账户已解除与其他网站、程序及APP的绑定。\n当您符合账户注销条件后，您在本APP上提供的所有信息将被清空，我们也将停止收集、使用和共享您在该账户上留存的相关个人信息，除非法律有强制留存信息的要求。如，依据《中华人⺠共和国电子商务法》的规定，要求商品和服务信息、交易信息保存的时间自交易完成之日起保存不少于三年，因此，我们将依据法律的规定，对您在使用本APP期间的数据进行保留。\n关于前述期限的判断标准:完成与您相关的交易目的、维护相应交易及业务记录、应对您可能的查询或投诉;保证我们为您提供服务的安全和质量;您是否同意更⻓的留存期间;是否存在保留期限的其他特别约定。\n<b>在您的个人信息超出保留期间后，我们会根据法律的要求删除您的个人信息，或使其匿名化处理。</b>\n<b>(五)约束信息系统自动决策</b>\n在某些业务功能中，我们可能仅依据信息系统、算法等在内的非人工自动决策机制做出决定。如果这些决定显著影响您的合法权益，您有权要求我们做出解释，我们也将在不侵害药帮忙商业秘密或其他用户权益、社会公共利益的前提下提供申诉方法。\n<b>(六)响应您的上述请求</b>\n为保障安全，您可能需要提供书面请求，或以其他方式证明您的身份。我们可能会先要求您验证自己的身份，然后再处理您的请求。\n我们将在15个工作日内做出答复。如您不满意，还可以通过药帮忙客服发起投诉。\n对于您合理的请求，我们原则上不收取费用，但对多次重复、超出合理限度的请求，我们将视情收取一定成本费用。对于与您的身份不直接关联的信息、无端重复的信息，或者需要过多技术手段(例如，需要开发新系统或从根本上改变现行惯例)、给他人合法权益带来⻛险或者不切实际的请求，我们可能会予以拒绝。\n在以下情形中，按照法律法规要求，我们将无法响应您的请求:\n1、与国家安全、国防安全有关的;\n2、与公共安全、公共卫生、重大公共利益有关的;\n3、与犯罪侦查、起诉、审判和执行判決等有关的;\n4、有充分证据表明个人信息主体存在主观恶意或滥用权利的;\n5、响应您的请求将导致您或其他个人、组织的合法权益受到严重损害的;\n6、涉及商业秘密的;\n7、以及法律法规规定的其他情形。\n\n\n<b>八、我们如何处理未成年人的个人信息</b>\n<b>在电子商务活动中我们推定您具有相应的⺠事行为能力。如您为未成年人的，我们建议您请您的父母或监护人仔细阅读本隐私权政策，并在征得您的父母或监护人同意的前提下使用我们的服务或向我们提供信息。</b>\n<b>对于经父母或监护人同意使用我们的产品或服务而收集未成年人个人信息的情況，我们只会在法律法规允许、父母或监护人明确同意或者保护未成年人所必要的情况下使用、共享、转让或披露此信息。</b>\n\n\n<b>九、您的个人信息如何在全球范围转移<b>\n我们在中华人⺠共和国境内运营中收集和产生的个人信息，将存储在中国境内，以下情形除外:\n1、法律法规有明确规定;\n2、获得您的明确授权;\n3、您通过互联网进行跨境交易等个人主动行为。\n如部分产品或服务涉及跨境，我们需要向境外传输您的个人信息，我们会确保依据本隐私权政策对您的个人信息提供足够的保护。\n\n\n<b>十、信息安全</b>\n我们高度重视并承诺保护您的个人信息。我们会采用适当的技术措施来保护您的个人信息以防止您的个人信息在未经授权的情况下被修改、被访问、被披露、被使用或被删除。\n\n\n<b>十一、本隐私政策如何更新</b>\n我们的隐私权政策可能变更未经您明确同意，我们不会限制您按照本隐私权政策所应享有的权利。我们会在专⻔⻚面(药帮忙APP“我的→我的客服→隐私权政策”)上展示最新版的隐私权政策。我们鼓励您经常查看本政策，以便了解任何修订的内容。\n如果对本政策的修订可能对我们使用或披露您个人信息的方式产生实质性影响，在您已经向我们提供了您的电子邮箱的情形下，您同意我们可以通过电子邮件的形式告知该等修订内容。\n对于重大变更，我们还会提供更为显著的通知(包括我们会通过药帮忙公示的方式进行通知甚至向您提供弹窗提示)\n 本政策所指的重大変更包括:\n1、我们的服务模式发⽣重⼤变化。如处理个⼈信息的⽬的、处理的个⼈信息类型、个⼈信息的使⽤⽅式等;\n2、我们在控制权等方面发生重大变化。如并购重组等引起的所有者变更等;\n3、个人信息共享、转让或公开披露的主要对象发生变化;\n4、您参与个人信息处理方面的权利及其行使方式发生重大变化;\n5、我们负责处理个⼈信息安全的责任部⻔、联络⽅式及投诉渠道发⽣变化时\n6、个⼈信息安全影响评估报告表明存在⾼⻛险时。\n\n\n<b>十二、与其他网站的链接</b>\n请注意，药帮忙APP可能包含了可连接⾄其他⽹站的链接。这些⽹站或APP并不⼀定遵循本政策的规定。请您查阅并了解这些⽹站或APP各⾃对于隐私权保护的相关规定。\n\n\n<b>十三、如何联系我们</b>\n如您对本政策存在任何疑问，或对于您的用户信息处理存在任何投诉、意⻅，请通过相应的客服热线: 400-0505-111、在线客服联系我们，客服部⻔将及时答复您。\n为保障您的信息安全，我们需要先验证您的身份和凭证资料。一般来说，我们将在验证通过后的三个工作日内处理完成，特殊情形下最⻓将在不超过15个工作日或法律法规规定期限内作出答复(特殊情况下对于更正、删除用户信息及注销用户账号的请求不超过15个工作日内，对于查询用户信息、获取用户信息副本、撤回已同意的授权的请求不超过30天内)。\n".split("\n")}},activated:function(){this.setAppRightMenu("",""),this.initContent()}},n=(0,s(51900).Z)(a,(function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"privacy"},[s("div",{staticClass:"main"},[s("h1",{staticClass:"title"},[t._v("隐私政策")]),t._v(" "),s("p",{staticClass:"float_right_first"},[t._v("版本更新日期：2023年10月11日")]),t._v(" "),s("p",{staticClass:"float_right"},[t._v("版本生效日期：2023年10月11日")]),t._v(" "),s("p",{staticClass:"explain"},[t._v("\n      药帮忙（以下或称“我们”）尊重并保护用户（以下或称“您”）的隐私，您的信任对我们非常重要，我们深知个人信息对您的重要性，我们将按法律法规要求，采取相应安全保护措施，尽力保护您的个人信息安全可控。鉴此，我们制定本《药帮忙隐私权政策》（下称“本政策”）向您说明，我们如何为您提供访问、更新、管理和保护您的信息的服务。\n    ")]),t._v(" "),s("p",{staticClass:"explain"},[t._v("\n      本政策与您使用我们的服务关系紧密，我们建议您仔细阅读并理解本政策全部内容，做出您认为适当的选择。我们努力用通俗易懂、简明扼要的文字表达，并将本政策中与您的权益存在重大关系的条款，采用粗体字进行标注以提示您注意。")]),t._v(" "),s("p",{staticClass:"explain"},[t._v("您使用或继续使用我们的产品或服务，访问、浏览和/或使用我们的APP和/或任何我们的线上渠道，参与我们开展的其他活动，即意味着同意我们按照本政策收集、使用、储存和分享您的相关信息。")]),t._v(" "),s("p",{staticClass:"summary"},[t._v("本政策将帮助您了解以下内容：")]),t._v(" "),s("p",{staticClass:"summary"},[t._v("一、本政策中关键词定义")]),t._v(" "),s("p",{staticClass:"summary"},[t._v("二、我们如何收集和使用您的个人信息")]),t._v(" "),s("p",{staticClass:"summary"},[t._v("三、我们如何使用Cookie、Beacon、Proxy技术")]),t._v(" "),s("p",{staticClass:"summary"},[t._v("四、我们如何共享、转让、公开披露您的个人信息")]),t._v(" "),s("p",{staticClass:"summary"},[t._v("五、我们如何保护您的个人信息")]),t._v(" "),s("p",{staticClass:"summary"},[t._v("六、您对个人信息享有的控制权")]),t._v(" "),s("p",{staticClass:"summary"},[t._v("七、我们如何处理未成年人的个人信息")]),t._v(" "),s("p",{staticClass:"summary"},[t._v("八、您的个人信息如何在全球范围转移")]),t._v(" "),s("p",{staticClass:"summary"},[t._v("九、信息安全")]),t._v(" "),s("p",{staticClass:"summary"},[t._v("十、本隐私政策如何更新")]),t._v(" "),s("p",{staticClass:"summary"},[t._v("十一、与其他网站的链接")]),t._v(" "),s("p",{staticClass:"summary"},[t._v("十二、如何联系我们")]),t._v(" "),s("p",{staticClass:"summary"},[t._v("十三、其他")]),t._v(" "),s("br"),t._v(" "),s("br"),t._v(" "),t._m(0),t._v(" "),s("p",{staticClass:"text"},[t._v("（一）本政策中的“用户信息”指：以电子或者其他方式记录的能够单独或者与其他信息结合识别特定自然人身份或者反映特定自然人活动情况的各种信息。")]),t._v(" "),t._m(1),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      （三）本政策中的“用户敏感信息”是指：我们用于识别您身份的信息要素，包括：指包括身份证件号码、用户生物识别信息、银行账号、财产信息、行踪轨迹、交易信息、14岁以下（含）儿童信息等的用户信息（我们将在本隐私权政策中对具体用户敏感信息以粗体进行显著标识）。\n    ")]),t._v(" "),s("p",{staticClass:"text"},[t._v("（四）本政策中的“用户信息删除”是指：在实现日常业务功能所涉及的系统中去除用户信息的行为，使其保持不可被检索、访问的状态。")]),t._v(" "),s("br"),t._v(" "),s("br"),t._v(" "),t._m(2),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      个人信息是指以电子或者其他方式记录的，能够单独或者与其他信息结合识别特定自然人身份或者反映特定自然人活动情况的各种信息。本隐私权政策涉及的个人信息包括：个人基本信息（包括个人姓名、地址、个人电话号码）；网络身份标识信息（包括登入手机账号、验证码）；个人财产信息（交易和消费记录、支付信息、订单信息）；个人设备信息（包括设备型号、设备MAC地址、操作系统类型、IMEI、IMSI、Android_ID、SIM卡序列号）；个人位置信息（包括精准定位信息）；\n    ")]),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      个人敏感信息是指一旦泄露、非法提供或滥用可能危害人身和财产安全，极易导致个人名誉、身心健康受到损害或歧视性待遇等的个人信息，本隐私权政策涉及的个人敏感信息包括：个人财产信息（交易和消费记录、支付信息、订单信息）、网络身份标识信息（登入手机账号、验证码），其他信息（个人电话号码、精准定位），未成年人的个人信息。\n    ")]),t._v(" "),t._m(3),t._v(" "),s("p",{staticClass:"text"},[t._v("我们的产品与/或服务包括一些核心功能，这些功能包含了实现网上购物所必须的功能及保障交易所必须的功能。我们可能会收集、保存和使用下列与您有关的信息才能实现上述这些功能。")]),t._v(" "),t._m(4),t._v(" "),t._m(5),t._v(" "),t._m(6),t._v(" "),t._m(7),t._v(" "),t._m(8),t._v(" "),t._m(9),t._v(" "),t._m(10),t._v(" "),s("p",{staticClass:"text"},[t._v("个性化商品：当您开启个性化推荐开关，我们可能会使用到您如下信息：您在访问或使用药帮忙网站或客户端时的服务日志，包括浏览记录、点击查看记录、搜索查询记录、点赞、收藏、添加至购物车、交易、售后等，为您推荐您可能感兴趣的商品或服务，以便更好的为您提供贴合您个人需求的个性化商品及服务，提高您的购物效率及体验。若您不需要使用此功能，可在我的-设置-推荐管理中关闭。")]),t._v(" "),t._m(11),t._v(" "),t._m(12),t._v(" "),t._m(13),t._v(" "),t._m(14),t._v(" "),t._m(15),t._v(" "),t._m(16),t._v(" "),t._m(17),t._v(" "),t._m(18),t._v(" "),t._m(19),t._v(" "),t._m(20),t._v(" "),t._m(21),t._v(" "),t._m(22),t._v(" "),t._m(23),t._v(" "),t._m(24),t._v(" "),s("p",{staticClass:"text"},[t._v("位置权限是您设备上的一项设置，您可以通过设备设置页面进行管理。")]),t._v(" "),t._m(25),t._v(" "),t._m(26),t._v(" "),s("p",{staticClass:"text"},[t._v("在我们的产品与/或服务中，我们会根据平台运营策略将订单状态、优惠信息提醒通过手机通知/短信方式推送至您的手机")]),t._v(" "),t._m(27),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      如果您不想接受我们给您发送的优惠信息，您可以通过手机关闭相关权限，即可在手机设置-隐私管理-权限管理中更改状态(各厂商机型设置路径可能存在不一致，用户可参考厂商设置说明)来关闭来自APP的推送消息。")]),t._v(" "),s("p",{staticClass:"text"},[t._v("为确保本应用处于关闭或后台运行状态下可正常接收到客户端推送的广播信息，本应用须使用(自启动)能力，将存在一定频率通过系统发送广播唤醒本应用自启动或关联启动行为，是因实现功能及服务所必要的。")]),t._v(" "),s("p",{staticClass:"text"},[t._v("当您打开药帮忙app内容类推送消息，在征得您的明确同意后，会跳转药帮忙app打开相关内容。在未征得您同意的情况下，则不会有关联启动。")]),t._v(" "),s("p",{staticClass:"text"},[t._v("当您打开药帮忙app内部下载的文件后，会关联启动第三方APP。")]),t._v(" "),t._m(28),t._v(" "),t._m(29),t._v(" "),t._m(30),t._v(" "),s("p",{staticClass:"text"},[t._v("读取及写入存储器权限主要用于缓存您在使用我们APP过程中产生的文本、图像、视频内容。")]),t._v(" "),t._m(31),t._v(" "),t._m(32),t._v(" "),t._m(33),t._v(" "),t._m(34),t._v(" "),t._m(35),t._v(" "),t._m(36),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      请知悉，根据相关法律规定，若我们对个人信息采取技术措施和其他必要措施处理，使得数据接收方法无法重新识别特定个人且不能复原，或我们可能会对收集的信息进行去标识化的研究、统计分析和预测，用于改善药帮忙APP的内容和布局，为商业决策提供产品和服务支撑，以及改进我们的产品和服务（包括使用匿名数据进行机器学习或模型算法训练），则此类处理后数据的使用无需另行向您通知并征得您的同意。\n    ")]),t._v(" "),s("p",{staticClass:"text"},[t._v("如我们停止运营药帮忙产品或服务，我们将及时停止继续收集您个人信息的活动，将停止运营的通知以逐一送达或公告的形式通知您，对所持有的个人信息进行删除或匿名化处理。 ")]),t._v(" "),s("br"),t._v(" "),s("br"),t._v(" "),t._m(37),t._v(" "),t._m(38),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      为使您获得更轻松的访问体验，您访问我们的网站或使用各项服务时，我们可能会通过小型数据文件识别您的身份，这么做可帮您省去重复输入注册信息的步骤，或者帮助判断您的账户安全状态。这些数据文件可能是Cookie，Flash\n      Cookie，您的浏览器或关联应用程序提供的其他本地存储（以下简称“Cookie”）。请您理解，某些服务只能通过使用Cookie才可得到实现。如您的浏览器或浏览器附加服务允许，您可以修改对Cookie的接受程度或者拒绝相关Cookie。多数浏览器工具条中的“帮助”部分会告诉您怎样防止您的浏览器接受新的Cookie，怎样让您的浏览器在您收到一条新Cookie时通知您或者怎样彻底关闭Cookie。此外，您可以通过改变浏览器附加程序的设置，或通过访问提供商的网页，来关闭或删除浏览器附加程序使用的类似数据（例如：Flash\n      Cookie）。但这一举动在某些情况下可能会影响您安全访问我们的网站和使用相关服务。")]),t._v(" "),t._m(39),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      除Cookie外，我们网站上还可能包含一些电子图像（以下简称“单像素GIF文件”或“网络Beacon”），使用网络Beacon可以帮助网站计算浏览网⻚的用户或访问某些Cookie，我们会通过网络Beacon收集您浏览网⻚活动的信息，包括：您访问的⻚面地址、您先前访问的援引⻚面的位置、您的浏览环境以及显示设定。如您通过我们的网站或APP，使用了由第三方而非我们的提供的服务时，我们无法保证这些第三方会按照我们的要求采取保护措施，为尽力确保您的账号安全，使您获得更安全的访问体验，我们可能会使用专用的网络协议及代理技术（以下简称“专用网络通道”或“网络代理”）。使用专用网络通道，可以帮助您识别到我们已知的高⻛险站点，减少由此引起的钓⻥、账号泄露⻛险，同时更有利于保障您和第三方的共同权益，阻止不法分子篡改您和您希望访问的第三方之间正常服务内容，包括：不安全路由器、非法基站引起的广告注入、非法内容篡改。在此过程中，我们也可能会获得和保存关于您计算机的相关信息，例如：IP地址、硬件ID。\n    ")]),t._v(" "),s("br"),t._v(" "),s("br"),t._v(" "),t._m(40),t._v(" "),t._m(41),t._v(" "),s("p",{staticClass:"text"},[t._v("我们不会与药帮忙服务提供者以外的公司、组织和用户共享您的用户信息，但以下情况除外:")]),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      1、我们承诺对您的信息进行保密。除法律法规及监管部⻔另有规定外，我们仅在以下情形中与第三方共享您的信息，第三方包括关联公司、供应商、服务提供商、合作金融机构以及其他合作伙伴。在将信息提供给第三方前，我们将尽商业上合理的努力评估该第三方收集信息的合法性、正当性、必要性。我们会与第三方签订相关法律文件并要求第三方处理您的用户信息时遵守法律法规，要求第三方对您的信息采取保护措施。\n    ")]),t._v(" "),s("p",{staticClass:"text"},[t._v("2、为了提升服务效率、降低服务成本或提高服务质量，某些产品或服务可能由第三方提供或由我们与第三方共同提供，我们也可能会委托专业服务机构提供协助，因此，为了更好的客户服务和用户体验，共享您的信息。\n    ")]),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      3、如您选择参与我们和第三方联合开展的抽奖、竞赛或类似推广活动，我们可能与其共享活动过程中产生的、为完成活动所必要的信息，以便第三方能及时向您发放奖品或为您提供服务，我们会依据法律法规或国家标准的要求，在活动规则⻚面或通过其他途径向您明确告知需要向第三方提供何种用户信息。\n    ")]),t._v(" "),s("p",{staticClass:"text"},[t._v("4、事先获得您明确同意的情况下，我们会在法律法规允许且不违背公序良俗的范围内，依据您的授权范围与第三方共享您的信息。")]),t._v(" "),s("p",{staticClass:"text"},[t._v("5、您主动选择情况下共享：您通过药帮忙平台购买产品或服务，我们会根据您的选择，将您订单信息中与交易有关的必要信息共享给相关商品或服务提供者，以实现您交易及售后服务需求。")]),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      6、在不透露单个用户用户信息资料的前提下，为了给用户提供更好的服务，药帮忙可能会对整个用户数据库进行分析并对用户数据库进行商业上的利用（包括但不限于公布、分析或以其它合法方式使用用户访问量、访问时段、用户偏好等用户数据信息）。\n    ")]),t._v(" "),s("p",{staticClass:"text"},[t._v("7、在法定情形下的共享：我们可能会根据法律法规规定、诉讼争议解决需要，或按行政、司法机关依法提出的要求，对外共享您的用户信息。")]),t._v(" "),t._m(42),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      9、与授权合作伙伴共享:我们可能会向授权合作伙伴第三方共享您的订单信息、账户信息、设备信息以及位置信息，以保障为您提供的服务顺利完成。但我们仅会出于合法、正当、必要、特定、明确的目的共享您的用户信息，并且只会共享提供服务所必要的用户信息。我们的合作伙伴无权将共享的用户信息用于任何其他用途。我们会要求授权合作伙伴根据我们的指示并遵循本政策以及其他任何相应的保密和安全措施来为我们处理这些信息。对于涉及儿童用户信息的，我们不允许合作伙伴进行转委托。\n    ")]),t._v(" "),s("p",{staticClass:"text"},[t._v("目前，我们的授权合作伙伴包括以下类型:")]),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      广告、分析服务类的授权合作伙伴，除非得到您的许可，否则我们不会将您的用户身份信息与提供广告、分析服务的合作伙伴共享。我们会委托这些合作伙伴处理与广告覆盖面和有效性相关的信息，但不会提供您的用户身份信息，或者我们将这些信息进行去标识化处理，以便它不会识别您用户。这类合作伙伴可能将上述信息与他们合法获取的其他数据相结合，以执行我们委托的广告服务或决策建议。\n    ")]),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      供应商、服务提供商和其他合作伙伴:我们业务的供应商、服务提供商和其他合作伙伴，这些支持包括受我们委托提供的技术基础设施服务、分析我们服务的使用方式、衡量广告和服务的有效性、提供客户服务、支付便利或进行学术研究和调查。\n    ")]),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      需要说明的是，我们会对授权合作伙伴获取有关信息的应用程序接口(API)、软件工具开发包(SDK)进行严格的安全检测，并与授权合作伙伴约定严格的数据保护措施，令其按照我们的委托目的、服务说明、本隐私权政策以及其他任何相关的保密和安全措施来处理个人信息。\n    ")]),t._v(" "),s("br"),t._v("\n    我们使用的第三方SDK如下列表：\n    "),s("table",{staticStyle:{"text-align":"center",padding:"0px","border-collapse":"collapse",border:"1px solid #666"},attrs:{border:"1"}},[t._m(43),t._v(" "),s("tbody",{attrs:{"aria-live":"polite","aria-relevant":"all"}},[t._m(44),t._v(" "),t._m(45),t._v(" "),t._m(46),t._v(" "),t._m(47),t._v(" "),t._m(48),t._v(" "),t._m(49),t._v(" "),t._m(50),t._v(" "),t._m(51),t._v(" "),s("tr",{attrs:{role:"row"}},[s("td",{attrs:{colspan:"1"}},[t._v("腾讯播放器")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("腾讯科技（深圳）有限公司")]),t._v(" "),t._m(52),t._v(" "),t._m(53),t._v(" "),t._m(54),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("a",{staticStyle:{"word-break":"break-all"},attrs:{href:"https://cloud.tencent.com/document/product/301/11470",rel:"nofollow"}},[t._v(t._s("https://cloud.tencent.com/document/product/301/11470"))])])]),t._v(" "),t._m(55),t._v(" "),t._m(56),t._v(" "),t._m(57),t._v(" "),t._m(58),t._v(" "),t._m(59),t._v(" "),t._m(60),t._v(" "),t._m(61),t._v(" "),t._m(62),t._v(" "),t._m(63),t._v(" "),t._m(64),t._v(" "),t._m(65),t._v(" "),t._m(66),t._v(" "),t._m(67),t._v(" "),t._m(68)])]),t._v(" "),s("br"),t._v(" "),t._m(69),t._v(" "),s("p",{staticClass:"text"},[t._v("我们不会将您的用户信息转让给任何公司、组织和用户，但以下情况除外:")]),t._v(" "),s("p",{staticClass:"text"},[t._v("1、在获取明确同意的情況下转让:获得您的明确同意后，我们会向其他方转让您的个人信息。")]),t._v(" "),s("p",{staticClass:"text"},[t._v("2、根据法律法规或强制性的行政或司法要求。")]),t._v(" "),s("p",{staticClass:"text"},[t._v("3、转让经去标识化处理的用户信息，且确保数据接收方无法重新识别或者关联用户信息主体。")]),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      4、在涉及资产转让、收购、兼并、重组或破产时，如涉及到用户信息转让，我们会向您告知有关情况，并要求新的持有您用户信息的公司、组织继续受本政策的约束。如变更用户信息使用目的时，我们将要求该公司、组织重新取得您的明示同意。如破产且无承接方的，我们将对数据做删除处理。\n    ")]),t._v(" "),t._m(70),t._v(" "),s("p",{staticClass:"text"},[t._v("我们仅在以下情况下，公开披露您的用户信息：")]),t._v(" "),s("p",{staticClass:"text"},[t._v("1、在公布中奖活动名单时会脱敏展示中奖者手机号或账户登录名。")]),t._v(" "),s("p",{staticClass:"text"},[t._v("2、获得您明确同意或基于您的主动选择，我们可能会公开披露您的用户信息。")]),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      3、如果我们确定您出现违反法律法规或严重违反药帮忙相关协议规则的情况，或为保护药帮忙及其关联公司用户或公众的人身财产安全免遭侵害，我们可能会依据法律法规或药帮忙相关协议规则披露关于您的用户信息及您的店铺主体信息与处罚情况。\n    ")]),t._v(" "),s("p",{staticClass:"text"},[t._v("4、基于法律的披露：在法律、法律程序、诉讼或政府主管部⻔强制性要求的情況下，我们可能会公开披露您的个人信息。")]),t._v(" "),s("p",{staticClass:"text"},[t._v("5、出于维护国家安全、国防安全、公共安全、公共卫生、重大公共利益有关的目的。")]),t._v(" "),s("p",{staticClass:"text"},[t._v("除以上情形外，原则上我们不会将您的信息进行公开披露。如确需公开披露时，我们会向您告知并征得您的同意。")]),t._v(" "),t._m(71),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      为了提升信息处理效率，降低信息处理成本，或提高信息处理准确性，我们可能会委托有能力的关联公司或其他专业机构代表我们来处理用户信息，但我们会通过书面协议、现场审计方式要求受托公司遵守严格的保密义务及采取有效的保密措施，禁止其将这些信息用于未经您授权的用途。在委托关系解除时，要求受托公司不再保存用户信息。\n    ")]),t._v(" "),t._m(72),t._v(" "),s("p",{staticClass:"text"},[t._v("以下情形中，共享、转让、公开披露您的个人信息无需事先征得您的授权同意：")]),t._v(" "),s("p",{staticClass:"text"},[t._v("1、与国家安全、国防安全有关的；")]),t._v(" "),s("p",{staticClass:"text"},[t._v("2、与公共安全、公共卫生、重大公共利益有关的；")]),t._v(" "),s("p",{staticClass:"text"},[t._v("3、与犯罪侦査、起诉、审判和判決执行等有关的；")]),t._v(" "),s("p",{staticClass:"text"},[t._v("4、出于维护您或其他个人的生命、财产等重大合法权益但又很难得到本人同意的；")]),t._v(" "),s("p",{staticClass:"text"},[t._v("5、您自行向社会公众公开的个人信息；")]),t._v(" "),s("p",{staticClass:"text"},[t._v("6、从合法公开披露的信息中收集个人信息的，如合法的不属于个人信息的对外共享、转让及公开披露行为，对此类数据的保存及处理将无需另行向您通知并征得您的同意。")]),t._v(" "),t._m(73),t._v(" "),s("br"),t._v(" "),s("br"),t._v(" "),t._m(74),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      (一)我们已采取符合业界标准、合理可行的安全防护措施保护您提供的个人信息安全，防止个人信息遭到未经授权访问、公开披露、使用、修改、损坏或丢失。例如，在您的浏览器与服务器之间交換数据时受SSL协议加密保护;我们会使用加密技术提高个人信息的安全性;我们会使用受信赖的保护机制防止个人信息遭到恶意攻击;我们会部署访问控制机制，尽力确保只有授权入员才可访问个人信息;以及我们会举办安全和隐私保护培训课程，加强员工对于保护个人信息重要性的认识。\n    ")]),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      (二)我们有行业先进的以数据为核心，围绕数据生命周期进行的数据安全管理体系，从组织建设、制度设计、人员管理、产品技术等方面多维度提升整个系统的安全性。目前，我们的重要信息系统已经通过网络安全等级保护三级认证。\n    ")]),t._v(" "),t._m(75),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      (四)互联网并非绝对安全的环境，我们强烈建议您不要使用非药帮忙推荐的通信方式发送您的信息。如您发现自己的个人信息尤其是您的账户或密码发生泄露，请您立即联络药帮忙客服，以便我们根据您的申请采取相应措施。")]),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      请注意，您在使用我们服务时自愿共享甚至公开分享的信息，可能会涉及您或他人的个人信息甚至个人敏感信息，如您在评价时选择上传包含个人信息的图片。请您更加谨慎地考虑，是否在使用我们的服务时共享甚至公开分享相关信息。\n    ")]),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      请协助我们保证您的账号安全。我们将尽力保障您发送给我们的任何信息的安全性。如果我们的物理、技术或管理防护设施遭到破坏，导致信息被非授权访问、公开披露、簒改或毀坏，导致您的合法权益受损，我们将承担相应的法律责任。\n    ")]),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      (五)在不幸发生个人信息安全事件后，我们将按照法律法规的要求向您告知：安全事件的基本情况和可能的影响、我们已采取或将要采取的处置措施、您可自主防范和降低⻛险的建议、对您的补救措施。事件相关情況我们将以邮件、信函、电话、推送通知方式告知您，难以逐一告知个人信息主体时，我们会采取合理、有效的方式发布公告。\n    ")]),t._v(" "),s("p",{staticClass:"text"},[t._v("同时，我们还将按照监管部⻔要求，上报个人信息安全事件的处置情况。")]),t._v(" "),s("br"),t._v(" "),s("br"),t._v(" "),t._m(76),t._v(" "),t._m(77),t._v(" "),t._m(78),t._v(" "),s("p",{staticClass:"text"},[t._v("除法律法规规定的例外情况，无论您何时使用我们的服务，我们都会力求让您顺利访问自己的个人信息。您可以通过如下方式行使您的访问权利")]),t._v(" "),s("p",{staticClass:"text"},[t._v("1.帐户信息:如果您希望访问或编辑您的帐户中的个人资料信息、添加安全信息，您可以在您使用的产品中执行此类操作。个人资料信息在药帮忙App中的访问路径为：我的--更多账号信息--基本信息；")]),t._v(" "),s("p",{staticClass:"text"},[t._v("2.订单信息:您可以在我们系统中查阅或清除您的订单记录，交易记录及发票记录。订单信息在药帮忙App中的访问路径为：我的--我的订单--全部订单；")]),t._v(" "),s("p",{staticClass:"text"},[t._v("3.其他信息:如您在此访问过程中遇到操作问题的或如需获取其他前述无法获知的个人信息内容，您可通过文末提供的方式联系我们，我们将在核实您的身份后在合理期限内向您提供，但法律法规另有规定的或本政策另有约定的除外。")]),t._v(" "),t._m(79),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      当您需要更新您的个人信息时，或发现我们处理您的个人信息有错误时，您有权作出更正或更新。您可以自行在我们系统内进行更正，或通过反馈与报错将您的更正申请提交给我们。在您进行更新或更正前，我们可能会先要求您验证自己的身份，然后再处理您的请求。\n    ")]),t._v(" "),s("p",{staticClass:"text"},[t._v("对于您的部分个人信息，我们在产品的相关功能页面为您提供了操作指引和操作设置，您可以直接进行更正/修改，药店资质信息在药帮忙App中的更正/修改路径为：我的--资质管理")]),t._v(" "),s("p",{staticClass:"text"},[t._v("对于您在行使上述权利过程中遇到的困难，或其他可能未/无法向您提供在线自行更正/修改权限的， 经对您的身份进行验证，且更正不影响信息的客观性和准确性的情况下，您有权对错误或不完整的信息作出更正或修改，或在特定情况下，尤其是数据错误时，通过我们公布的反馈与报错等措施将您的更正/修改申请提交给我们，要求我们更正或修改您的数据，但法律法规另有规定的除外。但出于安全性和身份识别的考虑，您可能无法修改注册时提交的某些初始注册信息。")]),t._v(" "),t._m(80),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      除法律法规规定的情形外，您可以向我们提出删除个人信息的请求。若我们响应您的删除请求，我们还将同时尽可能通知从我们处获得您的个人信息的主体，要求其及时删除。但如果这些主体已经获得您独立/单独的授权，需要您另行向该主体提出请求。\n    ")]),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      对于您的部分个人信息，我们在产品的相关功能页面为您提供了操作指引和操作设置，您可以直接进行删除，订单记录信息在药帮忙App中的删除路径为：我的—全部订单。一旦您删除后，我们即会对此类信息进行删除或匿名化处理，除非法律法规另有规定。\n    ")]),t._v(" "),t._m(81),t._v(" "),t._m(82),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      如您需要您的个人信息的副本，您可以通过本《隐私政策》文末提供的方式联系我们，在核实您的身份后，我们将向您提供您在我们的服务中的个人信息副本（包括基本资料、身份信息），但法律法规另有规定的或本政策另有约定的除外。\n    ")]),t._v(" "),t._m(83),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      如您想更改相关权限的授权（存储控件、电话状态、麦克风、位置、相机），您可以通过您的硬件设备进行修改、或在我们的产品或服务中的相关功能设置界面进行操作处理（撤回同意路径为：我的--设置--系统权限）。\n    ")]),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      您也可以通过注销账号的方式，永久撤回我们继续收集您个人信息的全部授权。如您在此过程中遇到操作问题的，可以通过本《隐私政策》文末提供的方式联系我们。\n    ")]),t._v(" "),t._m(84),t._v(" "),t._m(85),t._v(" "),t._m(86),t._v(" "),t._m(87),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      我们将持续为您提供优质服务，若因特殊原因导致我们的部分或全部产品与/或服务被迫停止运营，我们将提前在显著位置或向您发送推送消息或以其他方式通知您，并将停止对您个人信息的收集，同时在超出法律法规规定的必需且最短期限后，我们将会对所持有的您的个人信息进行删除或匿名化处理。\n    ")]),t._v(" "),t._m(88),t._v(" "),s("p",{staticClass:"text"},[t._v("为保障安全，您可能需要提供书面请求，或以其他方式证明您的身份。我们可能会先要求您验证自己的身份，然后再处理您的请求。")]),t._v(" "),s("p",{staticClass:"text"},[t._v("我们将在15个工作日内做出答复。如您不满意，还可以通过药帮忙客服发起投诉。")]),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      对于您合理的请求，我们原则上不收取费用，但对多次重复、超出合理限度的请求，我们将视情收取一定成本费用。对于与您的身份不直接关联的信息、无端重复的信息，或者需要过多技术手段(例如，需要开发新系统或从根本上改变现行惯例)、给他人合法权益带来⻛险或者不切实际的请求，我们可能会予以拒绝。\n    ")]),t._v(" "),s("p",{staticClass:"text"},[t._v("在以下情形中，按照法律法规要求，我们将无法响应您的请求:")]),t._v(" "),s("p",{staticClass:"text"},[t._v("1、与国家安全、国防安全有关的;")]),t._v(" "),s("p",{staticClass:"text"},[t._v("2、与公共安全、公共卫生、重大公共利益有关的;")]),t._v(" "),s("p",{staticClass:"text"},[t._v("3、与犯罪侦查、起诉、审判和执行判決等有关的;")]),t._v(" "),s("p",{staticClass:"text"},[t._v("4、有充分证据表明个人信息主体存在主观恶意或滥用权利的;")]),t._v(" "),s("p",{staticClass:"text"},[t._v("5、响应您的请求将导致您或其他个人、组织的合法权益受到严重损害的;")]),t._v(" "),s("p",{staticClass:"text"},[t._v("6、涉及商业秘密的;")]),t._v(" "),s("p",{staticClass:"text"},[t._v("7、以及法律法规规定的其他情形。")]),t._v(" "),s("br"),t._v(" "),s("br"),t._v(" "),t._m(89),t._v(" "),t._m(90),t._v(" "),t._m(91),t._v(" "),s("br"),t._v(" "),s("br"),t._v(" "),t._m(92),t._v(" "),s("p",{staticClass:"text"},[t._v("我们在中华人⺠共和国境内运营中收集和产生的个人信息，将存储在中国境内，以下情形除外:")]),t._v(" "),s("p",{staticClass:"text"},[t._v("1、法律法规有明确规定;")]),t._v(" "),s("p",{staticClass:"text"},[t._v("2、获得您的明确授权;")]),t._v(" "),s("p",{staticClass:"text"},[t._v("3、您通过互联网进行跨境交易等个人主动行为。")]),t._v(" "),s("p",{staticClass:"text"},[t._v("如部分产品或服务涉及跨境，我们需要向境外传输您的个人信息，我们会确保依据本隐私权政策对您的个人信息提供足够的保护。")]),t._v(" "),s("br"),t._v(" "),s("br"),t._v(" "),t._m(93),t._v(" "),s("p",{staticClass:"text"},[t._v("我们高度重视并承诺保护您的个人信息。我们会采用适当的技术措施来保护您的个人信息以防止您的个人信息在未经授权的情况下被修改、被访问、被披露、被使用或被删除。")]),t._v(" "),s("br"),t._v(" "),s("br"),t._v(" "),t._m(94),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      我们的隐私权政策可能变更未经您明确同意，我们不会限制您按照本隐私权政策所应享有的权利。我们会在专⻔⻚面(药帮忙APP“我的→我的客服→隐私权政策”)上展示最新版的隐私权政策。我们鼓励您经常查看本政策，以便了解任何修订的内容。\n    ")]),t._v(" "),s("p",{staticClass:"text"},[t._v("如果对本政策的修订可能对我们使用或披露您个人信息的方式产生实质性影响，在您已经向我们提供了您的电子邮箱的情形下，您同意我们可以通过电子邮件的形式告知该等修订内容。")]),t._v(" "),s("p",{staticClass:"text"},[t._v("对于重大变更，我们还会提供更为显著的通知(包括我们会通过药帮忙公示的方式进行通知甚至向您提供弹窗提示)")]),t._v(" "),s("p",{staticClass:"text"},[t._v(" 本政策所指的重大変更包括:")]),t._v(" "),s("p",{staticClass:"text"},[t._v("1、我们的服务模式发⽣重⼤变化。如处理个⼈信息的⽬的、处理的个⼈信息类型、个⼈信息的使⽤⽅式等;")]),t._v(" "),s("p",{staticClass:"text"},[t._v("2、我们在控制权等方面发生重大变化。如并购重组等引起的所有者变更等;")]),t._v(" "),s("p",{staticClass:"text"},[t._v("3、个人信息共享、转让或公开披露的主要对象发生变化;")]),t._v(" "),s("p",{staticClass:"text"},[t._v("4、您参与个人信息处理方面的权利及其行使方式发生重大变化;")]),t._v(" "),s("p",{staticClass:"text"},[t._v("5、我们负责处理个⼈信息安全的责任部⻔、联络⽅式及投诉渠道发⽣变化时")]),t._v(" "),s("p",{staticClass:"text"},[t._v("6、个⼈信息安全影响评估报告表明存在⾼⻛险时。")]),t._v(" "),s("br"),t._v(" "),s("br"),t._v(" "),t._m(95),t._v(" "),s("p",{staticClass:"text"},[t._v("请注意，药帮忙APP可能包含了可连接⾄其他⽹站的链接。这些⽹站或APP并不⼀定遵循本政策的规定。请您查阅并了解这些⽹站或APP各⾃对于隐私权保护的相关规定。")]),t._v(" "),s("br"),t._v(" "),s("br"),t._v(" "),t._m(96),t._v(" "),s("p",{staticClass:"text"},[t._v("如您对本政策存在任何疑问，或对于您的用户信息处理存在任何投诉、意⻅，我们专门为您提供了如下反馈通道，希望为您提供满意的解决方案：")]),t._v(" "),s("p",{staticClass:"text"},[t._v("在线客服/其他在线意见反馈通道：您可与我们平台上产品功能页面的在线客服联系或者在线提交意见反馈；")]),t._v(" "),s("p",{staticClass:"text"},[t._v("人工客服通道：请您拨打相应的客服热线: 400-0505-111、在线客服联系我们，客服部⻔将及时答复您；")]),t._v(" "),s("p",{staticClass:"text"},[t._v("个人信息保护负责人：您可发送邮件至*********************来与其联系")]),t._v(" "),s("p",{staticClass:"text"},[t._v("\n      为保障您的信息安全，我们需要先验证您的身份和凭证资料。一般来说，我们将在验证通过后的三个工作日内处理完成，特殊情形下最⻓将在不超过15个工作日或法律法规规定期限内作出答复(特殊情况下对于更正、删除用户信息及注销用户账号的请求不超过15个工作日内)。\n    ")]),t._v(";\n    "),t._m(97),t._v(" "),s("p",{staticClass:"text"},[t._v("本《隐私政策》的解释及争议解决均应适用中华人民共和国大陆地区法律。如就本政策的签订、履行等发生任何争议的，双方应尽量友好协商解决；协商不成时，任何一方均可向被告住所地享有管辖权的人民法院提起诉讼。")])])])}),[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text subject"},[s("b",[t._v("一、本政策中关键词定义")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[t._v("\n      （二）本政策中的“药帮忙”指：武汉小药药医药科技有限公司及武汉小药药医药科技有限公司的所有关联公司。公司注册地址为："),s("b",[t._v("武汉市东湖新技术开发区光谷大道77号金融港后台服务中心一期A2栋第9层901室、第3层301室")]),t._v("。\n    ")])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text subject"},[s("b",[t._v("二、我们如何收集和使用您的个人信息")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text subject"},[s("b",[t._v("(一)您须授权我们收集和使用您个人信息的情形")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[s("b",[t._v("(1)账户注册、登录与资质验证")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[t._v("登录/注册：为成为我们的用户，以便我们为您提供服务，您可能需要提供"),s("b",[t._v("手机号码、姓名、位置信息")]),t._v("，并创建用户。我们将通过发送短信验证码的方式来验证您的身份是否有效。"),s("b",[t._v("请注意：收集此类信息是为了满足相关法律法规的实名制要求。")]),t._v("另外在您进行关联店铺过程中我们可能会请求"),s("b",[t._v("定位权限")]),t._v("来获取您附近的店铺，"),s("b",[t._v("您可以选择拒绝该权限申请，通过手动输入来关联店铺，拒绝该权限申请并不会影响您的注册流程。")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[t._v("资质管理：为满足相关法律规定及监管要求，您在浏览商品和购买商品之前，需要您提供药品销售相关资质照片进行审核验证，我们将申请"),s("b",[t._v("相机权限（CAMERA）、读取/写入外置存储器（READ_EXTERNAL_STORAGE）（WRITE_EXTERNAL_STORAGE）")]),t._v("来访问拍照功能和手机相册功能，从而对资质相关图片进行拍照、浏览、保存、上传。我们将根据您的客户类型，可能收集如下信息："),s("b",[t._v("身份证、营业执照、药品生产许可证、药品经营许可证、医疗机构执业许可证")]),t._v("。相关业务场景可能包括：商品信息浏览、商品加购、购物车结算。为实现身份认证的目的，您同意我们可以自行或委托政府机构提供、查询、核对您的前述身份信息。")])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[s("b",[t._v("(2)商品信息展示和搜索")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[t._v("商品浏览：为了让您快速地找到您所需要的商品，我们可能会申请"),s("b",[t._v("获取任务（GET_TASKS）用于与第三方APP分享交互")]),s("b",[t._v("电话状态权限（READ_PHONE_STATE）、定位权限（LOCATION）")]),t._v("来收集您的设备信息（包括"),s("b",[t._v("设备名称、设备型号、MAC地址、IMEI、IMSI、Android_ID、SIM卡序列号及移动应用列表软硬件特征信息、操作系统和应用程序版本、语言设置、分辨率）、设备所在位置相关信息（包括您授权的GPS位置信息以及WLAN接入点、蓝牙和基站传感器信息）、浏览器类型")]),t._v("来为您提供商品信息展示的最优方式。我们也会为了不断改进和优化上述的功能来使用您的上述个人信息。 ")])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[t._v("商品搜索：您也可以通过搜索来精准地找到您所需要的商品或服务。我们会保留您的搜索内容以方便您重复输入或为您展示与您搜索内容相关联的商品或服务。同时当您使用搜索功能时，我们会自动收集您的一些信息，包括如下个人信息："),s("b",[t._v("搜索的字词（包括语音信息）、浏览记录和时间、搜索的时间以及与它们互动的次数。")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[t._v("语音搜索：当您使用语音搜索商品时，我们会请求"),s("b",[t._v("⻨克⻛权限（READ_AUDIO）")]),t._v("来记录您的语音信息。我们会收集您在使用智能语音技术过程中录入的语音信息用于机器识别、在线交互并满足您的查询活动需求。此项功能权限申请您可以选择拒绝，也可以在系统权限中关闭，一旦关闭您将无法实现在线语音交互功能，但不会影响您继续浏览我们的APP⻚面。请您知晓，即使您已同意开启⻨克⻛权限，我们也仅会在您主动点击⻨克⻛图标时通过⻨克⻛获取语音信息。")])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[s("b",[t._v("请您注意，单独的设备信息、浏览器类型或仅搜索关键词信息无法单独识别您的身份，不属于您的个人信息，我们有权以任何的目的对其进行使用；只有当您的单独的设备信息、浏览器类型或搜索关键词信息与您的其他信息相互结合使用并可以识别您的身份时，则在结合使用期间，我们会将您的设备信息、浏览器类型或搜索关键词信息作为您的个人信息，按照本隐私政策对其进行处理与保护。")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[s("b",[t._v("(3)下单及订单管理")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[t._v("下单结算：当您准备对您购物车内的商品进行结算时，系统会生成您购买该商品的订单。"),s("b",[t._v("您需要在订单中至少填写您的收货人姓名、收货地址以及手机号码，同时该订单中会载明订单号、您所购买的商品或服务信息、下单时间、您应支付的货款金额及支付方式；您可以另外填写收货人的联系电话、邮箱地址信息以增加更多的联系方式确保商品可以准确送达，但不填写这些信息不影响您订单的生成。若您需要开具发票，还需要提供发票抬头、纳税人识别号以及接收发票电子邮箱。")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[t._v("\n      上述所有信息构成您的"),s("b",[t._v("“订单信息”")]),t._v("，我们将使用您的订单信息来（包括与为您提供上述服务的第三方）进行您的身份核验、确定交易、支付结算、完成配送、为您查询订单以及提供客服咨询与售后服务。我们还会使用您的订单信息来判断您的交易是否存在异常以保护您的交易安全。\n    ")])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[s("b",[t._v("(4)支付功能")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[t._v("\n      订单支付：在您下单后，为完成订单支付，您需要提供第三方支付账号（包括支付宝、花呗支付、银联支付、微信支付、广发白条）并选择付款方式，以便我们了解您的支付状态。您同意我们可从你选择的第三方支付平台（包括支付宝、花呗支付、银联支付、微信支付、广发白条）处收集与支付相关的信息；如果您使用其他金融机构为您提供支付服务(包括国际信用卡支付)，需要您填写"),s("b",[t._v("银行卡号、安全码、有效期")]),t._v("在内的银行卡支付必要信息来完成支付，为使我们及时获悉并确认您的支付进度和状态，为您提供售后与争议解决服务。\n    ")])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[s("b",[t._v("(5)产品安全保障功能")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[t._v("\n      我们需要通过申请"),s("b",[t._v("电话状态权限（READ_PHONE_STATE）、定位权限（LOCATION）")]),t._v("从而收集您的一些信息来保障您使用我们的产品与/或服务时的账号与系统安全，以防产生任何危害用户、社会的行为，包括您的如下个人信息："),s("b",[t._v("账号登录地、个人常用设备信息（硬件型号、设备MAC地址、IMEI、IMSI、Android_ID、SIM卡序列号）、登录IP地址、产品版本号、浏览记录、网络使用习惯、WIFI列表、服务故障信息，以及个人敏感信息：交易信息、实名认证信息、企业资质信息。")]),t._v("我们会根据上述信息来综合判断您账号、账户及交易风险、进行身份验证、客户服务、检测及防范安全事件、诈骗监测、存档和备份用途，并依法采取必要的记录、审计、分析、处置措施，一旦我们检测出存在或疑似存在账号安全风险时，我们会使用相关信息进行安全验证与风险排除，确保我们向您提供的产品和服务的安全性，以用来保障您的权益不受侵害。同时，当发生账号或系统安全问题时，我们会收集这些信息来优化我们的产品和服务。\n    ")])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[t._v("\n      此外，为确保您设备操作环境的安全以及提供我们的产品与/或服务所必需，防止恶意程序和反作弊，我们会在您同意本《隐私政策》后获取您设备上已安装或正在运行的必要的"),s("b",[t._v("应用/软件列表信息")]),t._v("（包括应用/软件来源、应用/软件总体运行情况、崩溃情况、使用频率）。请您知悉，单独的应用/软件列表信息无法识别您的特定身份。\n    ")])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text subject"},[s("b",[t._v("(二)您可自主选择提供的个人信息的情形")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[t._v("在您使用药帮忙服务的过程中，我们访问您的各项权限是为了向您提供服务、优化我们的服务以及保障您的帐号安全，我们会出于以下目的，收集和使用您的个人信息。"),s("b",[t._v("如果您不提供这些个人信息，您依然可以进行网上购物，但您可能无法使用为您带来购物乐趣的扩展功能或在购买某些商品时需要重复填写一些信息。")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[s("b",[t._v("（1）基于位置权限的附加业务功能（LOCATION）")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[t._v("当您浏览部分商品服务内容时，我们会请求您授权"),s("b",[t._v("定位权限（LOCATION）,在您开启位置权限后访问获取您的位置信息，根据您的位置信息提供更契合您需求的⻚面展示、产品及/或服务，包括：首⻚向您推荐热销商品及排行榜，搜索结果⻚区域精选补货必买商品")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[t._v("\n      位置信息：指您开启设备定位功能并使用我们基于位置提供的相关服务时，收集的有关您位置的信息（我们仅收集您当时所处的地理位置，但不会将您各时段的位置信息进行结合以判断您的行踪轨迹），包括您通过具有定位功能的移动设备使用我们的服务时，"),s("b",[t._v("通过GPS或WLAN方式收集的您的地理位置信息（您授权的GPS位置以及WLAN接入点、蓝牙和基站传感器信息）；您或其他用户提供的包含您所处地理位置的实时信息，")]),t._v("包括：您提供的账户信息中包含的您所在地区信息。\n    ")])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[s("b",[t._v("(2) 基于推送通知权限的附加业务功能")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[t._v("您理解并同意隐私协议后极光推送会申请"),s("b",[t._v("MEID（移动设备识别码）, BSSID")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[t._v("我们会根据您的"),s("b",[t._v("订单信息")]),t._v("，提取您的偏好特征，基于特征标签通过APP推送、短信形式向您发送以“促销活动”为内容的优惠信息。")])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[s("b",[t._v("(3) 基于摄像头（相机）的附加业务功能（CAMERA）")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[t._v("\n      当您使用首页扫码、搜索商品扫码、拍摄照片上传资质审核、管理功能时，我们会请求"),s("b",[t._v("相机权限（CAMERA）")]),t._v("，用于扫码、拍照。请您知晓，即使您已经同意开启相机权限，我们也仅会在您主动点击客户端内上述功能时通过相机获取相关信息。若您不使用以上功能时，可以不允许此权限。\n    ")])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[s("b",[t._v("(4)基于读取/写入外置存储器的附加业务功能（WRITE_EXTERNAL_STORAGE）（READ_EXTERNAL_STORAGE）")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[t._v("\n      当您使用资质上传、拍照购物、提交售后申请或与客服沟通提供证明功能时，我们会请求"),s("b",[t._v("读取/写入外置存储器权限（WRITE_EXTERNAL_STORAGE）（READ_EXTERNAL_STORAGE）")]),t._v("您可在开启相册权限后使用该功能上传您的照片/图片/视频，我们可能会通过您所上传的照片/图片来识别您需要购买的商品或服务，或使用包含您所上传照片或图片用于资质验证。若您不使用该功能，可以不允许此权限。\n    ")])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[s("b",[t._v("(5)基于⻨克⻛权限的附加业务功能（READ_AUDIO）")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[t._v("\n      当您使用在线客服发送语音以及语音搜索商品时，我们会请求"),s("b",[t._v("⻨克⻛权限（READ_AUDIO）")]),t._v("，通过发送语音进行沟通。我们会收集您在使用智能语音技术过程中录入的语音信息用于机器识别、在线交互并满足您的查询活动或咨询信息需求（包括：您在使用“在线客服”功能时，我们会根据您的需求进行信息的搜索、展示）。此项功能您可以在系统权限中关闭，一旦关闭您将无法实现在线语音交互功能，但不会影响您继续浏览我们的APP⻚面。请您知晓，即使您已同意开启⻨克⻛权限，我们也仅会在您主动点击⻨克⻛图标时通过⻨克⻛获取语音信息。\n    ")])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[s("b",[t._v("(6)基于读取电话状态的扩展功能（READ_PHONE_STATE）")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[t._v("当您运行药帮忙APP时，我们会向您申请获取此权限，目的是"),s("b",[t._v("正常识别您的本机识别码，以便完成安全⻛控、进行统计和服务推送。")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[t._v("您理解并同意，上述附加服务可能需要您在您的设备中开启您的"),s("b",[t._v("位置（LOCATION）、摄像头（CAMERA）、读取及写入外部存储器（WRITE_EXTERNAL_STORAGE）（READ_EXTERNAL_STORAGE）、麦克风（READ_AUDIO）、读取电话状态（READ_PHONE_STATE）")]),t._v("的访问权限，以实现这些权限所涉及信息的收集和使用。您可在您的设备手机设置-隐私管理-权限管理(各厂商机型设置路径可能存在不一致，用户可参考厂商设置说明)中逐项查看上述权限的状态，并可自行决定这些权限随时的开启或关闭。"),s("b",[t._v("请您注意，您开启任一权限即代表您授权我们可以收集和使用相关个人信息来为您提供对应服务，您一旦关闭任一权限即代表您取消了授权，我们将不再基于对应权限继续收集和使用相关个人信息，也无法为您提供该权限所对应的服务。但是，您关闭权限的决定不会影响此前基于您的授权所进行的信息收集及使用。")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text subject"},[s("b",[t._v("三、我们如何使用Cookie、Beacon、Proxy技术")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[s("b",[t._v("(一)Cookie 的使用")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[s("b",[t._v("(二)网络Beacon和同类技术的使用")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text subject"},[s("b",[t._v("四、我们如何共享、转让、公开披露您的个人信息")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[s("b",[t._v("(一)共享")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[t._v("8、与关联公司间共享:"),s("b",[t._v("为便于我们基于药帮忙账户向您提供产品和服务，识别会员账号异常，保护药帮忙关联公司或其他用户或公众的人身财产安全免遭侵害，您的用\n        户信息可能会与我们的关联公司和/或其指定的服务提供商共享。")]),t._v("我们只会共享必要的用户信息，且受本隐私政策中所声明目的的约束，如果我们共享您的用户敏感信息或关联公司改变用户信息的使用及处理目的，将再次征求您的授权同意。\n    ")])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("thead",[s("tr",{attrs:{role:"row"}},[s("th",[s("p",[s("strong",[t._v("第三方SDK名称")])])]),t._v(" "),s("th",[s("p",[s("strong",[t._v("所属公司")])])]),t._v(" "),s("th",[s("p",[s("strong",[t._v("功能类型")])])]),t._v(" "),s("th",[s("p",[s("strong",[t._v("个人信息类型")])])]),t._v(" "),s("th",[s("p",[s("strong",[t._v("收集使用目的")])])]),t._v(" "),s("th",[s("p",[s("strong",[t._v("隐私协议链接或 sdk url")])])])])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("tr",{attrs:{role:"row"}},[s("td",{attrs:{colspan:"1"}},[t._v("极光推送")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("深圳市和讯华谷信息技术有限公司")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("推送消息通知")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",{staticStyle:{"word-break":"break-all"}},[t._v("设备信息（如：AndroidID,MAC地址，WIFI的BSSID，SSID，软件列表信息（包括软件列表及软件运行列表信息)）")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("网络信息（包括网络类型、运营商名称、IP地址、WIFI状态信息）")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("根据用户设备类型，提供通知推送功能，用于判定模糊位置信息，选择就近推送服务节点提供推送服务，提升推送触达率。")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("a",{staticStyle:{"word-break":"break-all"},attrs:{href:"https://docs.jiguang.cn/jpush",rel:"nofollow"}},[t._v("极光推送 - JPush 产品简介 - 极光文档\n          (jiguang.cn)")])])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("tr",{attrs:{role:"row"}},[s("td",{attrs:{colspan:"1"}},[t._v("华为推送")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("华为技术有限公司")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("推送消息通知")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",{staticStyle:{"word-break":"break-all"}},[t._v("设备信息（如：AndroidID,MAC地址，软件列表信息（包括软件列表及软件运行列表信息)）")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("根据用户设备类型，提供通知推送功能")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("a",{staticStyle:{"word-break":"break-all"},attrs:{href:"https://developer.huawei.com/consumer/cn/doc/development/HMSCore-Guides/service-introduction-0000001050040060",rel:"nofollow"}},[t._v("推送服务-产品说明 (huawei.com)")])])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("tr",{attrs:{role:"row"}},[s("td",{attrs:{colspan:"1"}},[t._v("小米推送")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("小米科技有限责任公司")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("推送消息通知")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",{staticStyle:{"word-break":"break-all"}},[t._v("设备信息（如：AndroidID,MAC地址，软件列表信息（包括软件列表及软件运行列表信息)）")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("根据用户设备类型，提供通知推送功能")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("a",{staticStyle:{"word-break":"break-all"},attrs:{href:"https://dev.mi.com/console/doc/detail?pId=68",rel:"nofollow"}},[t._v("文档中心 (mi.com)")])])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("tr",{attrs:{role:"row"}},[s("td",{attrs:{colspan:"1"}},[t._v("oppo推送")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("OPPO广东移动通信有限公司")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("推送消息通知")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",{staticStyle:{"word-break":"break-all"}},[t._v("设备信息（如：AndroidID,MAC地址，软件列表信息（包括软件列表及软件运行列表信息)）")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("根据用户设备类型，提供通知推送功能")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("a",{staticStyle:{"word-break":"break-all"},attrs:{href:"https://privacy.oppo.com/cn/policy/",rel:"nofollow"}},[t._v("OPPO安全隐私 | OPPO中国")])])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("tr",{attrs:{role:"row"}},[s("td",{attrs:{colspan:"1"}},[t._v("vivo推送")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("维沃移动通信有限公司")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("推送消息通知，IMEI，MAC地址，SSID")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",{staticStyle:{"word-break":"break-all"}},[t._v("设备信息（如：AndroidID,MAC地址，软件列表信息（包括软件列表及软件运行列表信息)）")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("根据用户设备类型，提供通知推送功能")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("a",{staticStyle:{"word-break":"break-all"},attrs:{href:"https://dev.vivo.com.cn/documentCenter/doc/366",rel:"nofollow"}},[t._v("vivo消息推送平台服务协议")])])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("tr",{attrs:{role:"row"}},[s("td",{attrs:{colspan:"1"}},[t._v("阿里云http DNS")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("阿里云计算有限公司")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("dns解析")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("网络权限、地理位置、标识码、网络类型、网络名称及WIFI Mac地址、存储空间等信息")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("dns解析")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("a",{staticStyle:{"word-break":"break-all"},attrs:{href:"https://help.aliyun.com/document_detail/150879.html",rel:"nofollow"}},[t._v("Android SDK接入 - HTTPDNS\n          - 阿里云 (aliyun.com)")])])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("tr",{attrs:{role:"row"}},[s("td",{attrs:{colspan:"1"}},[t._v("Web应用防火墙")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("阿里云计算有限公司")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("安全防护")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("设备信息、日志信息")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("安全防护")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("a",{staticStyle:{"word-break":"break-all"},attrs:{href:"https://terms.aliyun.com/legal-agreement/terms/suit_bu1_ali_cloud/suit_bu1_ali_cloud202103181143_65567.html",rel:"nofollow"}},[t._v("Web应用防火墙APP防护SDK隐私权政策")])])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("tr",{attrs:{role:"row"}},[s("td",{attrs:{colspan:"1"}},[t._v("百度地图")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("北京百度网讯科技有限公司")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("定位")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("网络，")]),s("span",[t._v("外部存储状态，网络类型，ip地址，定位")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("定位")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("a",{staticStyle:{"word-break":"break-all"},attrs:{href:"https://lbsyun.baidu.com/index.php?title=androidsdk",rel:"nofollow"}},[t._v("Android地图SDK | 百度地图API\n          SDK (baidu.com)")])])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("td",{attrs:{colspan:"1"}},[s("span",[t._v("直播服务")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("td",{attrs:{colspan:"1"}},[s("span",[t._v("系统语言，网络类型，ANDROID_ID，IP 地址，相册")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("td",{attrs:{colspan:"1"}},[s("span",[t._v("直播服务")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("tr",{attrs:{role:"row"}},[s("td",{attrs:{colspan:"1"}},[t._v("bugly")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("腾讯科技（深圳）有限公司")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("收集崩溃信息")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("系统唯一标识符，SIM 卡序列号，ANDROID_ID")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("收集崩溃信息")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("a",{staticStyle:{"word-break":"break-all"},attrs:{href:"https://www.qq.com/contract.shtml",rel:"nofollow"}},[t._v("https://www.qq.com/contract.shtml")])])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("tr",{attrs:{role:"row"}},[s("td",{attrs:{colspan:"1"}},[t._v("科大讯飞")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("科大讯飞股份有限公司")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("语音识别")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("外部存储状态，网络类型，设备型号，设备制造商，录音权限，收集信息权限")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("语音识别服务")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("a",{staticStyle:{"word-break":"break-all"},attrs:{href:"https://www.xfyun.cn/doc/asr/voicedictation/Android-SDK.html",rel:"nofollow"}},[t._v("语音听写 Android\n          SDK 文档 | 讯飞开放平台文档中心 (xfyun.cn)")])])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("tr",{attrs:{role:"row"}},[s("td",{attrs:{colspan:"1"}},[s("span",{staticStyle:{color:"rgb(76,76,76)"}},[t._v("腾讯TBS浏览服务")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("腾讯科技（深圳）有限公司")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("内置浏览器")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("网络，"),s("span",[t._v("外部存储状态，网络类型，设备型号，设备制造商")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("内置浏览器")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("a",{staticStyle:{"word-break":"break-all"},attrs:{href:"https://x5.tencent.com/docs/access.html",rel:"nofollow"}},[t._v("腾讯浏览服务 (tencent.com)")])])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("tr",{attrs:{role:"row"}},[s("td",{attrs:{colspan:"1"}},[s("span",{staticStyle:{color:"rgb(76,76,76)"}},[t._v("QQ互联")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("腾讯科技（深圳）有限公司")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("分享服务")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("网络，"),s("span",[t._v("外部存储状态")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("用于将页面分享至QQ好友或朋友圈")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("a",{staticStyle:{"word-break":"break-all"},attrs:{href:"https://wiki.connect.qq.com/qq%e4%ba%92%e8%81%94sdk%e9%9a%90%e7%a7%81%e4%bf%9d%e6%8a%a4%e5%a3%b0%e6%98%8e",rel:"nofollow"}},[t._v("qq互联sdk隐私保护声明")])])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("tr",{attrs:{role:"row"}},[s("td",{attrs:{colspan:"1"}},[t._v("微信支付")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("腾讯科技（深圳）有限公司")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("微信支付")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("网络，")]),s("span",[t._v("外部存储状态")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("根据用户选择为用户提供支付功能")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("a",{staticStyle:{"word-break":"break-all"},attrs:{href:"https://pay.weixin.qq.com/wiki/doc/api/app/app.php?chapter=11_1",rel:"nofollow"}},[t._v("https://pay.weixin.qq.com/wiki/doc/api/app/app.php?chapter=11_1")])])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("tr",{attrs:{role:"row"}},[s("td",{attrs:{colspan:"1"}},[t._v("阿里支付")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("支付宝(中国)网络技术有限公司")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("支付宝支付")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("系统语言，系统唯一标识符，外部存储状态，网络类型，设备型号，设备制造商，SIM 卡序列号，IMEI，MAC 地址，ANDROID_ID，IP\n              地址，WiFi信息，应用安装列表，OpenUDID")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("据用户选择为用户提供支付功能")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("a",{staticStyle:{"word-break":"break-all"},attrs:{href:"https://render.alipay.com/p/c/k2cx0tg8",rel:"nofollow"}},[t._v("https://render.alipay.com/p/c/k2cx0tg8")])])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("tr",{attrs:{role:"row"}},[s("td",{attrs:{colspan:"1"}},[t._v("银联手机支付控件")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("中国银联股份有限公司")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("银联支付")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("网络位置信息，网络类型，手机号码，SIM 卡状态，IMEI，MAC 地址，WiFi 信息")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("据用户选择为用户提供支付功能")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("a",{staticStyle:{"word-break":"break-all"},attrs:{href:"https://www.chinaums.com/chinaums/dsfdy/qmfapplb/201708/t20170828_25547.shtml",rel:"nofollow"}},[t._v("https://www.chinaums.com/chinaums/dsfdy/qmfapplb/201708/t20170828_25547.shtml")])])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("tr",{attrs:{role:"row"}},[s("td",{attrs:{colspan:"1"}},[t._v("华为update")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("华为技术有限公司")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("华为update")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("外部存储状态，网络类型")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("华为update")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("a",{staticStyle:{"word-break":"break-all"},attrs:{href:"https://developer.huawei.com/consumer/cn/doc/development/HMSCore-Guides/service-introduction-0000001050040060",rel:"nofollow"}},[t._v("https://developer.huawei.com/consumer/cn/doc/development/HMSCore-Guides/service-introduction-0000001050040060")])])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("tr",{attrs:{role:"row"}},[s("td",{attrs:{colspan:"1"}},[t._v("华为AppGallery Connect core SDK")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("华为技术有限公司")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("华为SDK基础工具")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("外部存储状态，网络类型")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("华为SDK基础工具")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("a",{staticStyle:{"word-break":"break-all"},attrs:{href:"https://developer.huawei.com/consumer/cn/doc/development/AppGallery-connect-Guides/agc-introduction",rel:"nofollow"}},[t._v("https://developer.huawei.com/consumer/cn/doc/development/AppGallery-connect-Guides/agc-introduction")])])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("tr",{attrs:{role:"row"}},[s("td",{attrs:{colspan:"1"}},[t._v("Android Support Library Compat")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("Android Jetpack 社区")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("基本工具，开发框架")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("无")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("基本工具，开发框架")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("a",{staticStyle:{"word-break":"break-all"},attrs:{href:"https://mvnrepository.com/artifact/androidx.core/core",rel:"nofollow"}},[t._v("https://mvnrepository.com/artifact/androidx.core/core")])])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("tr",{attrs:{role:"row"}},[s("td",{attrs:{colspan:"1"}},[t._v("flutter")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("Google")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("跨平台UI开发框架")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("无")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("跨平台UI开发框架")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("a",{staticStyle:{"word-break":"break-all"},attrs:{href:"https://github.com/flutter/flutter",rel:"nofollow"}},[t._v("https://github.com/flutter/flutter")])])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("tr",{attrs:{role:"row"}},[s("td",{attrs:{colspan:"1"}},[t._v("uCrop")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("Yalantis")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("图片剪裁")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("无")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("图片剪裁")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("a",{staticStyle:{"word-break":"break-all"},attrs:{href:"https://jitpack.io/#com.github.yalantis/ucrop",rel:"nofollow"}},[t._v("https://jitpack.io/#com.github.yalantis/ucrop")])])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("tr",{attrs:{role:"row"}},[s("td",{attrs:{colspan:"1"}},[t._v("Android Lifecycle ViewModel")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("Android Jetpack 社区")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("基础工具")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("无")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("基础工具")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("a",{staticStyle:{"word-break":"break-all"},attrs:{href:"https://mvnrepository.com/artifact/androidx.lifecycle/lifecycle-viewmodel",rel:"nofollow"}},[t._v("https://mvnrepository.com/artifact/androidx.lifecycle/lifecycle-viewmodel")])])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("tr",{attrs:{role:"row"}},[s("td",{attrs:{colspan:"1"}},[t._v("LuckSiegePictureSelector SDK")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[t._v("LuckSiege")]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("图片选择")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("无")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("span",[t._v("图片选择")])]),t._v(" "),s("td",{attrs:{colspan:"1"}},[s("a",{staticStyle:{"word-break":"break-all"},attrs:{href:"https://mvnrepository.com/artifact/com.github.LuckSiege.PictureSelector/picture_library",rel:"nofollow"}},[t._v("https://mvnrepository.com/artifact/com.github.LuckSiege.PictureSelector/picture_library")])])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[s("b",[t._v("(二)转让")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[s("b",[t._v("(三)公开披露")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[s("b",[t._v("（四）委托处理")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[s("b",[t._v("（五）共享、转让、公开披露个人信息时事先征得授权同意的例外")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[t._v("\n      请知悉，根据现行有效的法律规定，"),s("b",[t._v("若我们对个人信息采取技术措施和其他必要措施进行处理，使得数据接收方无法重新识别特定个人且不能复原，则此类处理后数据的共享、转让、公开披露无需另行向您通知并征得您的同意。")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text subject"},[s("b",[t._v("五、我们如何保护您的个人信息")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[t._v("(三)我们只会在达成本政策所述目的所需的期限內保留您的个人信息，"),s("b",[t._v("除非延⻓保留期征得您的同意或法律有强制的存留要求。")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text subject"},[s("b",[t._v("六、您对个人信息享有的控制权")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[t._v("\n      我们将尽一切可能采取适当的技术手段，保证您可以访问、更新、更正和删除自己的注册信息或使用我们的服务时提供的其他个人信息。"),s("b",[t._v("在访问、更新、更正和删除前述信息时，我们可能会要求您进行身份验证，以保障账户安全。您有权访问、更新、更正和删除自己的信息(包括用户名、性别、电子邮箱、支付渠道信息、邮寄地址、偏好语言以及与帐户或其相关活动的运作有关的您自行提供的信息)，改变授权范围并保护自己的隐私和安全。")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[s("b",[t._v("(一)访问您的个人信息")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[s("b",[t._v("(二)更正或补充您的个人信息")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[s("b",[t._v("(三)删除您的个人信息")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[t._v("您确认知悉:"),s("b",[t._v("当您从我们的服务中删除信息后，由于法律和安全技术等原因，我们可能会不能立即从备份系统中删除相应的信息，\n        我们将安全的存储您的个人信息并将其与任何进一步处理隔离，直到备份可以清除或实现匿名。")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[s("b",[t._v("(四)个人信息副本获取权")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[s("b",[t._v("(五)撤回同意权")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[s("b",[t._v("(六)注销权")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[t._v("我们为您提供账号注销的多种途径，您可以通过在线申请注销或联系我们的客服或通过其他我们公示的方式申请注销您的账号。"),s("b",[t._v("在您注销药帮忙账号后，您将无法再以此账号登录和使用药帮忙相关产品与服务；该账号曾获得的余额、优惠券、积分、订单视为您自行放弃，将无法继续使用；该账号下的内容、信息、数据、记录将会被删除或匿名化处理（但法律法规另有规定或监管部门另有要求的除外）；同时，药帮忙账号一旦注销完成，将无法恢复，请您在操作之前自行备份药帮忙账户相关的所有信息和数据。")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[t._v("账号注销的处理期限为15日，更多关于药帮忙账号注销的流程、注意事项等详见"),s("a",{attrs:{href:"/static/xyyvue/dist/#/userLogout"}},[t._v("《药帮忙注销须知》")]),t._v("。如您在谨慎考虑后仍执意决定注销您的药帮忙账号的，您可以在您使用的我们的产品与/或服务的相关功能设置页面或根据操作指引向我们提交注销申请，例如：药帮忙App中的注销路径为：我的--设置--账号注销。")])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[s("b",[t._v("(七)提前获知产品与/或服务停止运营权")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[s("b",[t._v("(八)响应您的上述请求")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text subject"},[s("b",[t._v("七、我们如何处理未成年人的个人信息")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[s("b",[t._v("在电子商务活动中我们推定您具有相应的⺠事行为能力。如您为未成年人的，我们建议您请您的父母或监护人仔细阅读本隐私权政策，并在征得您的父母或监护人同意的前提下使用我们的服务或向我们提供信息。")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text"},[s("b",[t._v("对于经父母或监护人同意使用我们的产品或服务而收集未成年人个人信息的情況，我们只会在法律法规允许、父母或监护人明确同意或者保护未成年人所必要的情况下使用、共享、转让或披露此信息。")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text subject"},[s("b",[t._v("八、您的个人信息如何在全球范围转移")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text subject"},[s("b",[t._v("九、信息安全")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text subject"},[s("b",[t._v("十、本隐私政策如何更新")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text subject"},[s("b",[t._v("十一、与其他网站的链接")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text subject"},[s("b",[t._v("十二、如何联系我们")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("p",{staticClass:"text subject"},[s("b",[t._v("十三、其他")])])}],!1,null,"a84e08ba",null).exports}}]);
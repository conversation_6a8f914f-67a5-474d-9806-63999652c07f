(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[76863],{37894:function(t,a,s){t.exports=s.p+"static/img/hots20.4ab96c5.jpg"},20783:function(t,a,s){t.exports=s.p+"static/img/hots22.cb3e8f6.jpg"},71428:function(t,a,s){t.exports=s.p+"static/img/colmed1.fb1cfd1.jpg"},13237:function(t,a,s){t.exports=s.p+"static/img/colmed2.8027388.jpg"},95477:function(t,a,s){t.exports=s.p+"static/img/colmed3.8784c69.jpg"},169:function(t,a,s){t.exports=s.p+"static/img/colmed4.d4f8857.jpg"},72787:function(t,a,s){t.exports=s.p+"static/img/colmed5.65e5d5d.jpg"},23980:function(t,a,s){t.exports=s.p+"static/img/colmed6.3e2ba7b.jpg"},88343:function(t,a,s){t.exports=s.p+"static/img/colmed7.7425615.jpg"},3877:function(t,a,s){t.exports=s.p+"static/img/colmed8.b631f0e.jpg"},32991:function(t,a,s){"use strict";s.d(a,{Z:function(){return o}});s(54678),s(56977),s(91058),s(9653);var i=s(59502),e=s(67087),d={props:["dataId","productNum","isSplit","medNum","isPack","discounts"],data:function(){return{goodsId:"",issplit:"",productValue:"",mednum:"",count:"",ispack:!1}},watch:{dataId:function(t){this.goodsId=t,this.productValue=this.productNum?this.productNum:0,this.issplit=this.isSplit,this.mednum=this.medNum,this.ispack=this.isPack,this.count=parseFloat(this.discounts);var a=Math.floor(this.productValue/this.mednum);document.querySelector(".text"+this.goodsId).innerHTML=0==a?this.count:(a*this.count).toFixed(2)}},methods:{addProductCart:function(t){var a=t.target.getAttribute("edit-cate"),s=parseInt(t.currentTarget.children[1].value),i=parseInt(this.mednum);if("add"==a)s+=i;else{if("min"!=a)return;s=1==this.issplit?s-1:s-i}s=s>0?s:0,this.productValue=s,this.postCartData(s)},inputCart:function(t){var a=parseInt(t.target.value);a=a||0,this.productValue=a,a=a>=0?a:0,this.postCartData(t.target.value)},postCartData:function(t){var a=this;if(t>0){var s=1==this.ispack?{merchantId:this.merchantId,amount:t,packageId:this.goodsId}:{merchantId:this.merchantId,amount:t,skuId:this.goodsId};this.putRequest("post","/app/changeCart",s).then((function(s){if("success"==s.data.status){Number(s.data.data.qty)&&(0,e.M0)("h5_page_CommodityDetails_o",{commodityId:a.goodsId,real:1}),s.data.data.qty!=t&&(a.productValue=s.data.data.qty);var d=Math.floor(s.data.data.qty/a.mednum);document.querySelector(".text"+a.goodsId).innerHTML=0==d?a.count:(d*a.count).toFixed(2),null!=s.data.dialog&&(20==s.data.dialog.style?i.Z.$emit("changesureDialog",{dialogmsg:s.data.dialog.msg,showsureDialog:!0}):i.Z.$emit("changeprompt",{dialog:s.data.dialog.msg,showprompt:!0})),s.errorMsg&&i.Z.$emit("changeprompt",{dialog:s.errorMsg,showprompt:!0});try{var o=1==a.ispack?{proid:a.goodsId,pronum:a.productValue,isAdd:1,type:1}:{proid:a.goodsId,pronum:a.productValue,isAdd:1};window.webkit.messageHandlers.addPlanNumber.postMessage(o)}catch(t){}try{1==a.ispack?window.hybrid.addPlanNumber(a.goodsId,a.productValue,1,1):window.hybrid.addPlanNumber(a.goodsId,a.productValue,1)}catch(t){}}else a.productValue=0,s.data.errorMsg?i.Z.$emit("changeprompt",{dialog:s.data.errorMsg,showprompt:!0}):s.data.msg?i.Z.$emit("changesureDialog",{dialogmsg:s.data.msg,showsureDialog:!0}):i.Z.$emit("changeprompt",{dialog:s.data.dialog.msg,showprompt:!0})})).catch((function(t){}))}else{var d=1==this.ispack?{merchantId:this.merchantId,packageIds:this.goodsId}:{merchantId:this.merchantId,ids:this.goodsId};this.putRequest("post","/app/batchRemoveProductFromCart",d).then((function(t){if("success"==t.data.status){a.productValue=0,document.querySelector(".text"+a.goodsId).innerHTML=a.count;try{var s=1==a.ispack?{proid:a.goodsId,pronum:0,isAdd:1,type:1}:{proid:a.goodsId,pronum:0,isAdd:1};window.webkit.messageHandlers.addPlanNumber.postMessage(s)}catch(t){}try{1==a.ispack?window.hybrid.addPlanNumber(a.goodsId,0,1,1):window.hybrid.addPlanNumber(a.goodsId,0,1)}catch(t){}}}))}}},created:function(){var t=this;this.goodsId=this.dataId,this.productValue=this.productNum?this.productNum:0,this.issplit=this.isSplit,this.mednum=this.medNum,this.ispack=this.isPack,this.count=parseFloat(this.discounts);var a=Math.floor(this.productValue/this.mednum);this.$nextTick((function(){document.querySelector(".text"+t.goodsId).innerHTML=0==a?t.count:(a*t.count).toFixed(2)}))}},o=(0,s(51900).Z)(d,(function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"buybtn",attrs:{"data-id":t.goodsId,"is-slipt":t.issplit,"med-pack-num":t.mednum},on:{click:function(a){return a.stopPropagation(),a.preventDefault(),t.addProductCart.apply(null,arguments)}}},[s("span",{staticClass:"min",attrs:{"edit-cate":"min"}},[t._v("-")]),t._v(" "),s("input",{directives:[{name:"model",rawName:"v-model",value:t.productValue,expression:"productValue"}],staticClass:"input-val",class:"input-val"+t.goodsId,attrs:{"edit-cate":"edit",type:"tel"},domProps:{value:t.productValue},on:{change:t.inputCart,input:function(a){a.target.composing||(t.productValue=a.target.value)}}}),t._v(" "),s("span",{staticClass:"add",attrs:{"edit-cate":"add"}},[t._v("+")])])}),[],!1,null,"44e5d53e",null).exports},78077:function(t,a,s){"use strict";s.r(a),s.d(a,{default:function(){return n}});s(74916),s(15306);var i=s(24388),e=s(59502),d=s(32991),o={data:function(){return{datalist1:[],datalist2:[],datalist3:[],datalist4:[],licenseStatus:0,isdata:!1,isshow:!1}},methods:{getPackData:function(){var t=this;this.putRequest("post","/app/activityPackage/pageList?effectiveStatus=1&limit=100",{merchantId:this.merchantId,mark:"0",offset:"0"}).then((function(a){if("success"==a.data.status&&null!=a.data.resultPage.rows&&a.data.resultPage.rows.length>0){var s=a.data.resultPage.rows;for(var i in t.licenseStatus=a.data.resultPage.licenseStatus,t.isdata=!0,s){if(1199==s[i].id)return void((new Date).getTime()>=new Date(s[i].startSaleTime.replace(/-/g,"/")).getTime()&&(t.isshow=!0))}}e.Z.$emit("changeloading",!1)})).catch((function(t){}))},moveEvent:function(){try{window.webkit.messageHandlers.hideKeyboard.postMessage({hide:"1"})}catch(t){}},getDataList:function(t,a){var s=this;this.putRequest("post","/app/layout/initExhibitionModulePage?sort=1",{merchantId:this.merchantId,limit:1e3,offset:0,exhibitionId:t}).then((function(t){"success"==t.data.status&&(s[a]=t.data.data.rows,s[a].licenseStatus=t.data.data.licenseStatus),s.$nextTick((function(){e.Z.$emit("changeloading",!1)}))})).catch((function(t){}))}},mounted:function(){this.getPackData(),this.getDataList("ZS201809291811545090","datalist1"),this.getDataList("ZS201809291813146570","datalist2"),this.getDataList("ZS201809291814365769","datalist3"),this.getDataList("ZS201809291816018924","datalist4")},activated:function(){this.setAppTitle("搭配用药")},components:{temprow:i.Z,addCartButton:d.Z}},n=(0,s(51900).Z)(o,(function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("div",{staticClass:"collomedician",on:{touchmove:t.moveEvent}},[i("div",{staticClass:"banner"},[i("img",{attrs:{src:s(71428),alt:""}}),t._v(" "),i("router-link",{staticClass:"linka",attrs:{to:"/packagesuper?id=1199"}},[i("img",{attrs:{src:s(13237),alt:""}}),t._v(" "),i("p",{staticClass:"text text1199"},[t._v("已优惠"),i("span",{staticClass:"text1199"},[t._v("20.4")]),t._v("元 ")]),t._v(" "),t.isdata&&!t.isshow?i("p",{staticClass:"hoping"},[t._v("敬请期待...")]):t._e(),t._v(" "),t.isdata&&t.isshow&&1!==t.licenseStatus&&5!==t.licenseStatus?i("addCartButton",{staticClass:"btn",attrs:{"is-pack":!0,"data-id":1199,"is-split":1,"product-num":1,"med-num":1,discounts:20.4}}):t._e()],1)],1),t._v(" "),i("div",{staticClass:"datalist"},[i("temprow",{attrs:{"goods-data":t.datalist1,"license-status":t.datalist1.licenseStatus||0}})],1),t._v(" "),i("div",[i("img",{attrs:{src:s(95477),alt:""}}),t._v(" "),i("router-link",{staticClass:"linka",attrs:{to:"/packagesuper?id=1200"}},[i("img",{attrs:{src:s(169),alt:""}}),t._v(" "),i("p",{staticClass:"text text1200"},[t._v("已优惠"),i("span",{staticClass:"text1200"},[t._v("5")]),t._v("元")]),t._v(" "),t.isdata&&!t.isshow?i("p",{staticClass:"hoping"},[t._v("敬请期待...")]):t._e(),t._v(" "),t.isdata&&t.isshow?i("addCartButton",{staticClass:"btn",attrs:{"is-pack":!0,"data-id":1200,"is-split":1,"product-num":1,"med-num":1,discounts:5}}):t._e()],1)],1),t._v(" "),i("div",{staticClass:"datalist"},[i("temprow",{attrs:{"goods-data":t.datalist2,"license-status":t.datalist2.licenseStatus||0}})],1),t._v(" "),i("div",[i("img",{attrs:{src:s(72787),alt:""}}),t._v(" "),i("router-link",{staticClass:"linka",attrs:{to:"/packagesuper?id=1201"}},[i("img",{attrs:{src:s(23980),alt:""}}),t._v(" "),i("p",{staticClass:"text text1201"},[t._v("已优惠"),i("span",{staticClass:"text1201"},[t._v("18.25")]),t._v("元")]),t._v(" "),t.isdata&&!t.isshow?i("p",{staticClass:"hoping"},[t._v("敬请期待...")]):t._e(),t._v(" "),t.isdata&&t.isshow?i("addCartButton",{staticClass:"btn",attrs:{"is-pack":!0,"data-id":1201,"is-split":1,"product-num":1,"med-num":1,discounts:18.25}}):t._e()],1)],1),t._v(" "),i("div",{staticClass:"datalist"},[i("temprow",{attrs:{"goods-data":t.datalist3,"license-status":t.datalist3.licenseStatus||0}})],1),t._v(" "),i("div",[i("img",{attrs:{src:s(88343),alt:""}}),t._v(" "),i("router-link",{staticClass:"linka",attrs:{to:"/packagesuper?id=1202"}},[i("img",{attrs:{src:s(3877),alt:""}}),t._v(" "),i("p",{staticClass:"text text1202"},[t._v("已优惠"),i("span",{staticClass:"text1202"},[t._v("36.4")]),t._v("元")]),t._v(" "),t.isdata&&!t.isshow?i("p",{staticClass:"hoping"},[t._v("敬请期待...")]):t._e(),t._v(" "),t.isdata&&t.isshow?i("addCartButton",{staticClass:"btn",attrs:{"is-pack":!0,"data-id":1202,"is-split":1,"product-num":1,"med-num":1,discounts:36.4}}):t._e()],1)],1),t._v(" "),i("div",{staticClass:"datalist"},[i("temprow",{attrs:{"goods-data":t.datalist4,"license-status":t.datalist4.licenseStatus||0}})],1),t._v(" "),i("router-link",{staticClass:"router",attrs:{to:"/mainvenue/hotsale"}},[i("img",{attrs:{src:s(20783),alt:""}})]),t._v(" "),i("router-link",{staticClass:"router lastrouter",attrs:{to:"/mainvenue/choosesamepro"}},[i("img",{attrs:{src:s(37894),alt:""}})])],1)}),[],!1,null,"2f049210",null).exports}}]);
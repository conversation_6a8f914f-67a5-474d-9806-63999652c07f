"use strict";(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[94755],{24391:function(t,s,a){a.r(s),a.d(s,{default:function(){return n}});var e=a(59502),i={data:function(){return{skuData:{},skuId:"",plantId:"",name:"",protype:""}},methods:{getGoodsData:function(){var t=this;e.Z.$emit("changeloading",!0),this.putRequest("post","/app/agreement/getSingleSkuDetail",{merchantId:this.merchantId,platformId:this.plantId,skuId:this.skuId}).then((function(s){"success"==s.data.status&&(t.skuData=s.data.data),t.$nextTick((function(){e.Z.$emit("changeloading",!1)}))})).catch((function(t){}))}},activated:function(){this.setAppTitle("协议详情");var t=this.$route.query.id,s=this.$route.query.platformId,a=this.$route.query.settleType;this.skuId!=this.id&&(this.protype=1==a?"月度":2==a?"季度":"年度",this.skuId=t,this.plantId=s,this.skuData={},this.getGoodsData())}},n=(0,a(51900).Z)(i,(function(){var t=this,s=t.$createElement,a=t._self._c||s;return a("div",{attrs:{id:"protocolgoods"}},[a("table",{staticClass:"protocoldetailmsg",attrs:{cellspacing:"0",cellpadding:"0"}},[a("tr",[a("td",{staticClass:"title"},[t._v("商品名称")]),t._v(" "),a("td",{staticClass:"content"},[t._v(t._s(t.skuData.showName))])]),t._v(" "),a("tr",[a("td",{staticClass:"title"},[t._v("规格")]),t._v(" "),a("td",{staticClass:"content"},[t._v(t._s(t.skuData.spec))])]),t._v(" "),a("tr",[a("td",{staticClass:"title"},[t._v("当前返点")]),t._v(" "),a("td",{staticClass:"content"},[t._v("当前"+t._s(t.protype)+"消费"+t._s(t.skuData.thisStageMoney)+"元，返点"+t._s(t.skuData.currentCashback)+"%")])]),t._v(" "),a("tr",[a("td",{staticClass:"title"},[t._v("返点比例")]),t._v(" "),a("td",{staticClass:"content percentlist"},t._l(t.skuData.agreementRuleList,(function(s){return a("p",{key:s.id},[t._v("总金额满"+t._s(s.moneyFull)+"元，返点"+t._s(s.onlinePayCashback)+"%；")])})),0)])])])}),[],!1,null,"761311bc",null).exports}}]);
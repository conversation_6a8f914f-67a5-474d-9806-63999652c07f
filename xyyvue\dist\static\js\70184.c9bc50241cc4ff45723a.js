(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[70184],{86679:function(t,e,a){t.exports=a.p+"static/img/1.6130af9.png"},6555:function(t,e,a){t.exports=a.p+"static/img/2.a0b30a9.png"},83296:function(t,e,a){t.exports=a.p+"static/img/3.1e2ad46.png"},88143:function(t,e,a){t.exports=a.p+"static/img/4.b7409bc.png"},96090:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return c}});var n=a(59502),i={data:function(){return{branchCode:"",autoHeight:{height:"auto"}}},created:function(){this.getDatalist()},methods:{getDatalist:function(){var t=this;n.Z.$emit("changeloading",!0),this.putRequest("post","/app/getBranchCode",{merchantId:this.merchantId}).then((function(e){n.Z.$emit("changeloading",!1),t.isdata=!0,"success"==e.data.status&&(t.branchCode=e.data.data,t.isshow=!0)})).catch((function(t){}))}},activated:function(){this.setAppTitle("国庆节物流配送公告")},mounted:function(){this.autoHeight={height:document.documentElement.clientHeight+"px"}}},c=(0,a(51900).Z)(i,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"wuliutfpage",style:t.autoHeight},["XS500000"==t.branchCode||"XS140001"==t.branchCode||"XS370000"==t.branchCode||"XS330000"==t.branchCode?n("div",[n("img",{attrs:{src:a(86679),alt:""}})]):"XS530000"==t.branchCode||"XS410000"==t.branchCode||"XS350000"==t.branchCode?n("div",[n("img",{attrs:{src:a(83296)}})]):"XS420000"==t.branchCode||"XS430000"==t.branchCode?n("div",[n("img",{attrs:{src:a(88143)}})]):n("div",[n("img",{attrs:{src:a(6555)}})])])}),[],!1,null,"4f551eda",null).exports}}]);
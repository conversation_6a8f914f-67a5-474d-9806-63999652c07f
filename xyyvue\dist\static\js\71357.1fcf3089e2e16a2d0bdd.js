(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[71357],{39920:function(t,e,n){t.exports=n.p+"static/img/tongrentang_02.86d16aa.jpg"},49664:function(t,e,n){t.exports=n.p+"static/img/tongrentang_04.7c22b33.png"},68503:function(t,e,n){t.exports=n.p+"static/img/tongrentang_05.fd3317d.png"},20732:function(t,e,n){"use strict";n.r(e),n.d(e,{default:function(){return c}});var a=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"banner"},[a("img",{attrs:{src:n(39920),alt:""}}),t._v(" "),a("img",{attrs:{src:n(49664),alt:""}})])}],s=n(24388),i=n(59502),o={data:function(){return{licenseStatus:0,presentData:[]}},methods:{getDataList:function(t,e){var n=this;this.putRequest("post","/app/layout/initExhibitionModulePage?sort=1",{merchantId:this.merchantId,limit:1e3,offset:0,exhibitionId:t}).then((function(t){"success"==t.data.status&&(n[e]=t.data.data.rows,n.licenseStatus=t.data.data.licenseStatus),n.$nextTick((function(){i.Z.$emit("changeloading",!1)}))})).catch((function(t){}))},moveEvent:function(){document.querySelector("#kuihuayy").scrollTop>800?i.Z.$emit("showBtn",{isShow:!0,dom:"#kuihuayy"}):i.Z.$emit("showBtn",{isShow:!1,dom:""})}},mounted:function(){this.getDataList("ZS201902151609202714","presentData")},activated:function(){this.setAppTitle("天津同仁堂")},components:{temprow:s.Z}},c=(0,n(51900).Z)(o,(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{attrs:{id:"kuihuayy"},on:{touchmove:t.moveEvent}},[t._m(0),t._v(" "),a("div",{staticClass:"present-content"},[a("img",{attrs:{src:n(68503),alt:""}}),t._v(" "),a("temprow",{attrs:{"goods-data":t.presentData,"license-status":t.licenseStatus||0}})],1)])}),a,!1,null,"cdffbcaa",null).exports}}]);
(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[655],{33190:function(t,a,s){t.exports=s.p+"static/img/coolparty.f934259.jpg"},98354:function(t,a,s){t.exports=s.p+"static/img/gmlh1.9fa6a31.jpg"},3919:function(t,a,s){t.exports=s.p+"static/img/gmlh2.9e8b26d.jpg"},37403:function(t,a,s){t.exports=s.p+"static/img/spbg.dffb814.jpg"},37274:function(t,a,s){t.exports=s.p+"static/img/sptab1.f9ba31f.jpg"},55309:function(t,a,s){t.exports=s.p+"static/img/txttitle.b80b24b.jpg"},34976:function(t,a,s){t.exports=s.p+"static/img/voucher1.f2a0283.jpg"},51719:function(t,a,s){t.exports=s.p+"static/img/voucher2.d55a14a.jpg"},3474:function(t,a,s){"use strict";s.r(a),s.d(a,{default:function(){return r}});var i=[function(){var t=this.$createElement,a=this._self._c||t;return a("div",{staticClass:"banner"},[a("img",{attrs:{src:s(33190),alt:""}})])},function(){var t=this.$createElement,a=this._self._c||t;return a("div",{staticClass:"tabs-title"},[a("img",{attrs:{src:s(55309),alt:""}})])},function(){var t=this.$createElement,a=this._self._c||t;return a("div",[a("img",{attrs:{src:s(37274),alt:""}})])},function(){var t=this.$createElement,a=this._self._c||t;return a("div",[a("img",{attrs:{src:s(98354),alt:""}})])},function(){var t=this.$createElement,a=this._self._c||t;return a("div",[a("img",{attrs:{src:s(3919),alt:""}})])}],e=(s(91058),s(24388)),c=s(59502),n={data:function(){return{isCur:"plan-content",voucher1:!1,voucher2:!1,istabfixed:!1,planData:[],healthData:[],girlData:[],isActive:"0",isCurrent:"0"}},methods:{getVouhcer:function(t){this.putRequest("post","/app/voucher/receiveVoucher",{merchantId:this.merchantId,voucherTemplateId:t}).then((function(t){if("failure"==t.data.status)if(9996==t.data.code)c.Z.$emit("changeprompt",{dialog:"您已领取过此券，用完可继续领取",showprompt:!0});else{var a=t.data.errorMsg?t.data.errorMsg:t.data.msg;c.Z.$emit("changeprompt",{dialog:a,showprompt:!0})}else c.Z.$emit("changeprompt",{dialog:t.data.msg,showprompt:!0})})).catch((function(t){}))},getDataList:function(t,a){var s=this;this.putRequest("post","/app/layout/initExhibitionModulePage?sort=1",{merchantId:this.merchantId,limit:1e3,offset:0,exhibitionId:t}).then((function(t){"success"==t.data.status&&(s[a]=t.data.data.rows,s[a].licenseStatus=t.data.data.licenseStatus),s.$nextTick((function(){c.Z.$emit("changeloading",!1)}))})).catch((function(t){}))},moveEvent:function(){var t=document.querySelector("#coolparty").scrollTop,a=document.querySelector(".tabs-box").offsetTop,s=document.querySelector(".tabs-box").offsetHeight;this.istabfixed=t>=a;var i=this.$refs.plan.offsetTop,e=this.$refs.health.offsetTop,c=this.$refs.girl.offsetTop;t>=i-s&&t<e-s?this.isCur="plan-content":t>=e-s&&t<c-s?this.isCur="health-content":t>=c-s&&(this.isCur="girl-content")},tabitemclick:function(t){this.istabfixed=!0;var a=t.currentTarget.getAttribute("contClass");this.isCur=a;var s=document.querySelector("."+a).offsetTop,i=document.querySelector(".tabs-box").offsetHeight;document.querySelector("#coolparty").scrollTop=s-i;document.querySelector("#coolparty").scrollTop},navitemclick:function(t){var a=parseInt(t.currentTarget.getAttribute("curIdx"));this.isActive=a,0==a?this.getDataList("ZS201809131552277500","planData"):1==a?this.getDataList("ZS201809131552442084","planData"):2==a?this.getDataList("ZS201809131553041624","planData"):3==a&&this.getDataList("ZS201809131553206381","planData")},navlistclick:function(t){var a=parseInt(t.currentTarget.getAttribute("curIdx"));this.isCurrent=a,0==a?this.getDataList("ZS201809131554206940","healthData"):1==a?this.getDataList("ZS201809131553517990","healthData"):2==a?this.getDataList("ZS201809131554063051","healthData"):3==a&&this.getDataList("ZS201809131554206940","healthData")}},mounted:function(){this.getDataList("ZS201809131552277500","planData"),this.getDataList("ZS201809131553364945","healthData"),this.getDataList("ZS201809131554334057","girlData")},components:{temprow:e.Z}},r=(0,s(51900).Z)(n,(function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("div",{attrs:{id:"coolparty"},on:{touchmove:t.moveEvent}},[t._m(0),t._v(" "),i("div",{staticClass:"voucher-list"},[i("div",{staticClass:"voucher-item"},[i("div",{on:{click:function(a){return t.getVouhcer(1106)}}},[i("img",{attrs:{src:s(34976),alt:""}})])]),t._v(" "),i("div",{staticClass:"voucher-item"},[i("div",{on:{click:function(a){return t.getVouhcer(1107)}}},[i("img",{attrs:{src:s(51719),alt:""}})])])]),t._v(" "),t._m(1),t._v(" "),i("div",{staticClass:"tabs-box"},[i("div",{staticClass:"tab-list",class:{tabfiexd:t.istabfixed}},[i("div",{staticClass:"tab-item",class:{tabclickcolor:"plan-content"==t.isCur},attrs:{contClass:"plan-content"},on:{click:t.tabitemclick}},[t._v("热销单品")]),t._v(" "),i("div",{staticClass:"tab-item",class:{tabclickcolor:"health-content"==t.isCur},attrs:{contClass:"health-content"},on:{click:t.tabitemclick}},[t._v("每满100减10")]),t._v(" "),i("div",{staticClass:"tab-item",class:{tabclickcolor:"girl-content"==t.isCur},attrs:{contClass:"girl-content"},on:{click:t.tabitemclick}},[t._v("每满100减20")])])]),t._v(" "),i("div",{ref:"plan",staticClass:"plan-content"},[t._m(2),t._v(" "),i("div",{staticClass:"nav-title"},[i("img",{attrs:{src:s(37403),alt:""}}),t._v(" "),i("div",{staticClass:"nav"},[i("div",{staticClass:"nav-item",class:{actived:"0"==t.isActive},attrs:{curIdx:"0"},on:{click:t.navitemclick}},[t._v("感冒用药")]),t._v(" "),i("div",{staticClass:"nav-item",class:{actived:"1"==t.isActive},attrs:{curIdx:"1"},on:{click:t.navitemclick}},[t._v("呼吸系统")]),t._v(" "),i("div",{staticClass:"nav-item",class:{actived:"2"==t.isActive},attrs:{curIdx:"2"},on:{click:t.navitemclick}},[t._v("抗菌消炎")]),t._v(" "),i("div",{staticClass:"nav-item",class:{actived:"3"==t.isActive},attrs:{curIdx:"3"},on:{click:t.navitemclick}},[t._v("其他用药")])])]),t._v(" "),i("temprow",{attrs:{"goods-data":t.planData,"license-status":t.planData.licenseStatus||0}})],1),t._v(" "),i("div",{ref:"health",staticClass:"health-content"},[t._m(3),t._v(" "),i("div",{staticClass:"nav-title"},[i("img",{attrs:{src:s(37403),alt:""}}),t._v(" "),i("div",{staticClass:"nav"},[i("div",{staticClass:"nav-item",class:{actived:"0"==t.isCurrent},attrs:{curIdx:"0"},on:{click:t.navlistclick}},[t._v("感冒用药")]),t._v(" "),i("div",{staticClass:"nav-item",class:{actived:"1"==t.isCurrent},attrs:{curIdx:"1"},on:{click:t.navlistclick}},[t._v("呼吸系统")]),t._v(" "),i("div",{staticClass:"nav-item",class:{actived:"2"==t.isCurrent},attrs:{curIdx:"2"},on:{click:t.navlistclick}},[t._v("抗菌消炎")]),t._v(" "),i("div",{staticClass:"nav-item",class:{actived:"3"==t.isCurrent},attrs:{curIdx:"3"},on:{click:t.navlistclick}},[t._v("其他用药")])])]),t._v(" "),i("temprow",{attrs:{"goods-data":t.healthData,"license-status":t.healthData.licenseStatus||0}})],1),t._v(" "),i("div",{ref:"girl",staticClass:"girl-content"},[t._m(4),t._v(" "),i("temprow",{attrs:{"goods-data":t.girlData,"license-status":t.girlData.licenseStatus||0}})],1)])}),i,!1,null,"4fd26034",null).exports}}]);
"use strict";(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[8746],{61255:function(t,a,s){s(91058),s(82772),s(9653);var i=s(59502),e=s(67087);a.Z={props:["dataId","productNum","isSplit","medNum","isPack","bgcolor","btncolor"],data:function(){return{goodsId:"",issplit:"",productValue:"",mednum:"",ispack:!1}},watch:{dataId:function(t){this.goodsId=t,this.productValue=this.productNum?this.productNum:0,this.issplit=this.isSplit,this.mednum=this.medNum,this.ispack=this.isPack},isExtend:function(){i.Z.$emit("cart_isExtend",this.isExtend,this.goodsId)}},methods:{addProductCart:function(t){if(this.btn_hidden)this.postCartData(this.mednum);else{var a=t.target.getAttribute("edit-cate"),s=parseInt(t.currentTarget.children[1].value),e=parseInt(this.mednum);if("add"==a)s+=e;else{if("min"!=a)return;s=1==this.issplit?s-1:s-e}s=s>0?s:0,this.productValue=s,i.Z.$emit("listenToChildEvent",{isExtend:this.isExtend,dataId:this.goodsId,productValue:this.productValue}),this.postCartData(s)}},inputCart:function(t){var a=parseInt(t.target.value);a=a>=0?a:0,this.productValue=a,i.Z.$emit("listenToChildEvent",{isExtend:this.isExtend,dataId:this.goodsId,productValue:this.productValue}),this.postCartData(t.target.value)},androidclick:function(t){if(navigator.userAgent.indexOf("Android")>=0){t.target.setAttribute("readonly",!0),t.target.blur();var a=t.target.value;i.Z.$emit("showandorideditcomp",{id:this.goodsId,val:a,showandoridedit:!0,split:this.issplit,medpack:this.mednum,package:this.ispack})}},postCartData:function(t){var a=this;if(t>0){var s=1==this.ispack?{merchantId:this.merchantId,amount:t,packageId:this.goodsId}:{merchantId:this.merchantId,amount:t,skuId:this.goodsId};this.putRequest("post","/app/changeCart",s).then((function(s){if("success"===s.data.status){a.btn_hidden&&i.Z.$emit("changeprompt",{dialog:"已添加到购物车!",showprompt:!0}),Number(s.data.data.qty)&&(0,e.M0)("h5_page_CommodityDetails_o",{commodityId:a.goodsId,real:1}),s.data.data.qty!=t&&(a.productValue=s.data.data.qty),null!=s.data.dialog&&(20==s.data.dialog.style?i.Z.$emit("changesureDialog",{dialogmsg:s.data.dialog.msg,showsureDialog:!0}):i.Z.$emit("changeprompt",{dialog:s.data.dialog.msg,showprompt:!0})),s.errorMsg&&i.Z.$emit("changeprompt",{dialog:s.errorMsg,showprompt:!0});try{var n=1==a.ispack?{proid:a.goodsId,pronum:a.productValue,isAdd:1,type:1}:{proid:a.goodsId,pronum:a.productValue,isAdd:1};window.webkit.messageHandlers.addPlanNumber.postMessage(n)}catch(t){}try{1==a.ispack?window.hybrid.addPlanNumber(a.goodsId,a.productValue,1,1):window.hybrid.addPlanNumber(a.goodsId,a.productValue,1)}catch(t){}}else a.productValue=0,i.Z.$emit("listenToChildEvent",{isExtend:a.isExtend,dataId:a.goodsId,productValue:a.productValue}),s.data.errorMsg?i.Z.$emit("changeprompt",{dialog:s.data.errorMsg,showprompt:!0}):s.data.msg?i.Z.$emit("changesureDialog",{dialogmsg:s.data.msg,showsureDialog:!0}):i.Z.$emit("changeprompt",{dialog:s.data.dialog.msg,showprompt:!0})})).catch((function(t){}))}else{var n=1==this.ispack?{merchantId:this.merchantId,packageIds:this.goodsId}:{merchantId:this.merchantId,ids:this.goodsId};this.putRequest("post","/app/batchRemoveProductFromCart",n).then((function(t){if("success"==t.data.status){a.btn_hidden&&i.Z.$emit("changeprompt",{dialog:"已添从购物车删除!",showprompt:!0}),a.productValue=0;try{var s=1==a.ispack?{proid:a.goodsId,pronum:0,isAdd:1,type:1}:{proid:a.goodsId,pronum:0,isAdd:1};window.webkit.messageHandlers.addPlanNumber.postMessage(s)}catch(t){}try{1==a.ispack?window.hybrid.addPlanNumber(a.goodsId,0,1,1):window.hybrid.addPlanNumber(a.goodsId,0,1)}catch(t){}}}))}}},created:function(){this.goodsId=this.dataId,this.productValue=this.productNum?this.productNum:0,this.issplit=this.isSplit,this.mednum=this.medNum,this.ispack=this.isPack}}},84786:function(t,a,s){s.d(a,{Z:function(){return d}});s(91058),s(47042),s(41539),s(68309),s(91038),s(78783),s(82526),s(41817),s(32165),s(66992),s(33948);var i=s(61255),e=s(59502);function n(t,a){var s="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!s){if(Array.isArray(t)||(s=function(t,a){if(!t)return;if("string"==typeof t)return o(t,a);var s=Object.prototype.toString.call(t).slice(8,-1);"Object"===s&&t.constructor&&(s=t.constructor.name);if("Map"===s||"Set"===s)return Array.from(t);if("Arguments"===s||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(s))return o(t,a)}(t))||a&&t&&"number"==typeof t.length){s&&(t=s);var i=0,e=function(){};return{s:e,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:e}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var n,r=!0,d=!1;return{s:function(){s=s.call(t)},n:function(){var t=s.next();return r=t.done,t},e:function(t){d=!0,n=t},f:function(){try{r||null==s.return||s.return()}finally{if(d)throw n}}}}function o(t,a){(null==a||a>t.length)&&(a=t.length);for(var s=0,i=new Array(a);s<a;s++)i[s]=t[s];return i}var r={props:{btn_hidden:{default:!1},from_tab:{type:String,default:""},goodItem:Object,big:{type:Boolean,default:!1}},computed:{isExtend:function(){return 0!=this.productValue}},mounted:function(){var t=this;e.Z.$on("listenToChildEvent",(function(a){var s=a.isExtend,i=a.dataId;parseInt(i)===parseInt(t.goodsId)&&(s||(t.productValue=0))})),e.Z.$on("update_cart",(function(a){var s,i=a.length,e=0,o=n(a);try{for(o.s();!(s=o.n()).done;){var r=s.value;if(r.item.id===t.goodsId){t.productValue=r.item.amount;break}e++}}catch(t){o.e(t)}finally{o.f()}i===e&&(t.productValue=0)}))},watch:{isExtend:function(){e.Z.$emit("cart_isExtend",this.isExtend,this.goodsId)}},mixins:[i.Z]},d=(0,s(51900).Z)(r,(function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"buybtn",class:{bigStyle:t.big},attrs:{"data-id":t.goodsId,"is-slipt":t.issplit,"med-pack-num":t.mednum,"is-extend":t.isExtend},on:{click:function(a){return a.stopPropagation(),a.preventDefault(),t.addProductCart.apply(null,arguments)}}},[t.btn_hidden?t._e():s("span",{directives:[{name:"show",rawName:"v-show",value:t.isExtend,expression:"isExtend"}],staticClass:"min",attrs:{"edit-cate":"min"}}),t._v(" "),t.btn_hidden?t._e():s("input",{directives:[{name:"show",rawName:"v-show",value:t.isExtend,expression:"isExtend"},{name:"model",rawName:"v-model",value:t.productValue,expression:"productValue"}],staticClass:"input-val",class:"input-val"+t.goodsId,attrs:{"edit-cate":"edit",type:"tel"},domProps:{value:t.productValue},on:{change:t.inputCart,click:function(a){return a.preventDefault(),a.stopPropagation(),t.androidclick.apply(null,arguments)},input:function(a){a.target.composing||(t.productValue=a.target.value)}}}),t._v(" "),s("span",{directives:[{name:"show",rawName:"v-show",value:t.isExtend,expression:"isExtend"}],staticClass:"add addshow",attrs:{"edit-cate":"add"}},[t._v("+")]),t._v(" "),s("span",{directives:[{name:"show",rawName:"v-show",value:!t.isExtend,expression:"!isExtend"}],staticClass:"plus",attrs:{"edit-cate":"add"}})])}),[],!1,null,"3c07d9fa",null).exports},65328:function(t,a,s){s.r(a),s.d(a,{default:function(){return d}});s(9653),s(56977),s(54678);var i=s(84786),e=s(59502),n={data:function(){return{imgUrl:"",isExtend:!0,proNum:0,swiperOption:{slidesPerView:3,slidesPerGroup:3}}},computed:{detailUrl:function(){return e.Z.detailBaseUrl}},props:{dataList:{type:Array,default:function(){return[]}},licenseStatus:Number},filters:{fixedtwo:function(t){return parseFloat(t).toFixed(2)}},components:{cartBtn:i.Z},methods:{showExtend:function(t){this.isExtend=t.isExtend},toshop:function(t){this.$emit("toshop",t)}},mounted:function(){this.imgUrl=this.imgBaseUrl}},o=s(51900),r={name:"flagship",data:function(){return{dataList:[],licenseStatus:0}},components:{swiperListThree:(0,o.Z)(n,(function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"swiperListThree"},t._l(t.dataList,(function(a,i){return s("div",{key:i,staticClass:"data-wrap"},[s("div",{staticClass:"data-header",on:{click:function(s){return t.toshop(a.shopId)}}},[s("img",{attrs:{src:a.logoUrl,alt:""}}),t._v(" "),s("div",{staticClass:"cont"},[s("p",[t._v(t._s(a.shopName))]),t._v(" "),s("span",[t._v(t._s(a.shopTypeName))])]),t._v(" "),s("div",{staticClass:"more"},[t._v("\n        更多\n      ")])]),t._v(" "),s("swipe",{staticClass:"swipe",attrs:{continuous:!1,auto:0}},t._l(Math.ceil(a.csus&&a.csus.length/3),(function(i,e){return s("swipe-item",{key:e},t._l(a.csus&&a.csus.slice(3*e,3*e+3),(function(a,i){return s("div",{key:i,staticClass:"commodity-item"},[s("a",{attrs:{href:t.detailUrl+"product_id="+a.id}},[s("div",{staticClass:"prod-img"},[s("img",{staticClass:"commodity-img",attrs:{src:t.imgUrl+"/ybm/product/min/"+a.imageUrl,alt:""}})]),t._v(" "),a.activityTag&&a.activityTag.tagNoteBackGroupUrl?s("div",{staticClass:"biaoqian"},[s("img",{attrs:{src:t.imgUrl+a.activityTag.tagNoteBackGroupUrl,alt:""}}),t._v(" "),s("div",{staticClass:"pintejia806"},[s("span",{staticClass:"discount"},[t._v("\n                "+t._s(a.activityTag.timeStr)+"\n              ")]),t._v(" "),t._l(a.activityTag.skuTagNotes,(function(a,i){return s("span",{key:i,staticClass:"price806",style:{color:"#"+a.textColor}},[t._v(t._s(a.text))])}))],2)]):t._e(),t._v(" "),s("h2",{staticClass:"textellipsis"},[a.activityTag&&a.activityTag.tagUrl?s("span",{staticClass:"bq-hgj"},[s("img",{attrs:{src:t.imgUrl+a.activityTag.tagUrl,alt:""}})]):t._e(),t._v("\n              "+t._s(a.commonName)+"\n            ")]),t._v(" "),s("h4",{staticClass:"textellipsis"},[t._v(t._s(a.spec))]),t._v(" "),1===t.licenseStatus||5===t.licenseStatus?s("div",{staticClass:"qualifications"},[t._v("\n              价格认证资质可见\n            ")]):!0!==a.isPurchase&&1===a.isControl?s("div",{staticClass:"nobuy"},[t._v("\n              暂无购买权限\n            ")]):s("div",{staticClass:"price-box"},[1!==t.licenseStatus&&5!==t.licenseStatus?s("h3",{staticClass:"fob"},[t._v("\n                ¥"+t._s(t._f("fixedtwo")(a.fob))+"\n              ")]):t._e()]),t._v(" "),1!==t.licenseStatus&&5!==t.licenseStatus?s("div",{staticClass:"btn-box"},[s("cartBtn",{attrs:{"is-pack":!1,"data-id":a.id,"is-split":1,"product-num":t.proNum,"med-num":a.mediumPackageNum},on:{listenToChildEvent:t.showExtend}})],1):t._e()])])})),0)})),1)],1)})),0)}),[],!1,null,"2ed43905",null).exports},methods:{industryList:function(){var t=this;e.Z.$emit("changeloading",!0),this.putRequest("post","/app/industry/list").then((function(a){if(e.Z.$emit("changeloading",!1),"success"==a.data.status){var s=a.data.data;t.dataList=s.dpList,t.licenseStatus=s.licenseStatus}})).catch((function(t){e.Z.$emit("changeloading",!1)}))},toshop:function(t){this.$router.push({path:"/flagshipshop?shopCode=".concat(t)})}},activated:function(){this.dataList=[],this.industryList()}},d=(0,o.Z)(r,(function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"flagship"},[s("swiper-list-three",{attrs:{"data-list":t.dataList,"license-status":t.licenseStatus||0},on:{toshop:t.toshop}})],1)}),[],!1,null,"a1cbeefa",null).exports}}]);
(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[93269],{54217:function(t,a,s){t.exports=s.p+"static/img/gaomao_01.850eedf.png"},98801:function(t,a,s){"use strict";s.r(a),s.d(a,{default:function(){return r}});var i=[function(){var t=this.$createElement,a=this._self._c||t;return a("div",{staticClass:"banner"},[a("img",{attrs:{src:s(54217),alt:""}})])}],e=s(4942),n=(s(91058),s(24388)),o=s(59502),c={data:function(){var t;return t={iscur:0,isShow:!0,isarrow:!0,temprowData:[],licenseStatus:0,isload:!1,loadingmsg:"正在加载···",initData:[{hdid:"ZS201909231554497240",title:"特惠单品"}],tehuiData:[{hdid:"ZS201909231554497240",title:"特惠单品"}],tabData:[{hdid:"ZS201909231553191610",title:"慢病用药"},{hdid:"ZS201909231553307530",title:"清热抗菌"},{hdid:"ZS201909231553451855",title:"消化系统"},{hdid:"ZS201909231553551393",title:"滋补养生"},{hdid:"ZS201909231554072986",title:"感冒呼吸"},{hdid:"ZS201909231554172123",title:"皮肤五官"},{hdid:"ZS201909231554301929",title:"风湿骨痛"},{hdid:"ZS201909231554393482",title:"其他用药"}],isfixed:!1,chooseHdid:"ZS201909231554497240",translateleft:0,tabIndex:0,scrollload:!0},(0,e.Z)(t,"isload",!1),(0,e.Z)(t,"loadingmsg","正在加载···"),(0,e.Z)(t,"pagecur",0),(0,e.Z)(t,"totalpage",0),(0,e.Z)(t,"showBtn",!1),(0,e.Z)(t,"startX",0),(0,e.Z)(t,"endX",0),(0,e.Z)(t,"isCurTab","spc-content"),t},methods:{tabitemclick:function(t){var a=t.currentTarget.getAttribute("contClass");this.isCurTab=a,"spc-content"==this.isCurTab?(this.initData=this.tehuiData,this.iscur=0,this.tabIndex=0,this.translatetabs()):"manjian-content"==this.isCurTab&&(this.initData=this.tabData,this.isarrow=!0,this.iscur=0,this.tabIndex=0,this.translatetabs())},spreadTabs:function(){this.isarrow=!this.isarrow},touchstartTabs:function(t){this.startX=t.touches[0].pageX},touchmoveTabs:function(t){var a=t.touches[0].pageX-this.startX;this.startX=t.touches[0].pageX,this.translateleft+=a,this.initData.length<=4&&(this.translateleft=0),this.translateleft>=0&&(this.translateleft=0),this.translateleft<=-380&&(this.translateleft=-380)},moveEvent:function(){var t=document.querySelector(".checktab").offsetTop,a=document.querySelector("#highmarginzhejiang").scrollTop;this.isfixed=a>=t,this.isarrow=!0;try{window.webkit.messageHandlers.hideKeyboard.postMessage({hide:"1"})}catch(t){}var s=window.screen.height;document.querySelector(".highmarginzhejiangnew-content").scrollHeight-a-400<=s&&this.scrollload&&(this.scrollload=!1,this.pagecur>=this.totalpage-1?(this.isload=!1,this.showBtn=!0):(this.isload=!0,this.loadingmsg="正在加载···",this.pagecur++,this.getDatalist(this.chooseHdid,"temprowData")))},changetabs:function(t){this.tabIndex=parseInt(t.target.getAttribute("tabitem")),(this.tabIndex||0==this.tabIndex)&&(this.translatetabs(),this.isarrow=!0)},translatetabs:function(){this.iscur=this.tabIndex,this.chooseHdid=this.initData[this.tabIndex].hdid,this.tabIndex>=2?this.translateleft=-70*(this.tabIndex-1):this.translateleft=0,this.pagecur=0,this.showBtn=!1,this.getDatalist(this.chooseHdid,"temprowData")},getDatalist:function(t,a){var s=this;this.putRequest("post","/app/layout/initExhibitionModulePage?sort=1",{merchantId:this.merchantId,limit:10,offset:this.pagecur,exhibitionId:t}).then((function(t){if("success"==t.data.status){0==s.pagecur&&(s.totalpage=t.data.data.pageCount,s[a]=[]),s.isload=!1,s.scrollload=!0;var i=t.data.data.rows;s[a].push.apply(s[a],i),s.licenseStatus=t.data.data.licenseStatus}s.$nextTick((function(){o.Z.$emit("changeloading",!1)}))})).catch((function(t){}))}},mounted:function(){this.getDatalist(this.chooseHdid,"temprowData")},components:{temprow:n.Z},activated:function(){this.setAppTitle("高毛专区")}},r=(0,s(51900).Z)(c,(function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{attrs:{id:"highmarginzhejiang"}},[s("div",{staticClass:"highmarginzhejiangnew-content",on:{touchmove:t.moveEvent}},[t._m(0),t._v(" "),s("div",{staticClass:"checktab"},[s("div",{staticClass:"tab-list",class:{tabfixed:t.isfixed}},[s("div",{staticClass:"tab-item spc-content",class:{tabclickcolor:"spc-content"==t.isCurTab},attrs:{contClass:"spc-content"},on:{click:t.tabitemclick}},[t._v("特惠单品")]),t._v(" "),s("div",{staticClass:"tab-item manjian-content",class:{tabclickcolor:"manjian-content"==t.isCurTab},attrs:{contClass:"manjian-content"},on:{click:t.tabitemclick}},[t._v("最高满799减30")])]),t._v(" "),t.isShow?s("div",{staticClass:"single-list"},[s("div",{staticClass:"tab-title",class:{tabfixed:t.isfixed},on:{click:t.changetabs}},[t.isarrow&&"spc-content"!=t.isCurTab?s("div",{staticClass:"tab-section"},[s("ul",{staticClass:"tab-scroll",style:{transform:["translateX("+t.translateleft+"px)","-webkit-translateX("+t.translateleft+"px)"]},on:{touchstart:t.touchstartTabs,touchmove:t.touchmoveTabs}},t._l(t.initData,(function(a,i){return s("li",{key:i,staticClass:"tab-title-item",class:{cur:t.iscur==i},attrs:{tabitem:i}},[t._v(t._s(a.title))])})),0)]):t._e(),t._v(" "),t.isarrow?t._e():s("div",{staticClass:"tab-more-section"},[s("div",{staticClass:"title"},[s("span",[t._v("精选分类")]),t._v(" "),s("em",{on:{click:function(a){return a.stopPropagation(),t.spreadTabs.apply(null,arguments)}}},[t._v("X")])]),t._v(" "),s("ul",{staticClass:"tab-more"},t._l(t.initData,(function(a,i){return s("li",{key:i,staticClass:"tab-more-item",class:{curcur:t.iscur==i},attrs:{tabitem:i}},[t._v(t._s(a.title))])})),0)]),t._v(" "),t.isarrow&&"spc-content"!=t.isCurTab?s("div",{staticClass:"tab-arrow",class:{arrow:!t.isarrow},on:{click:function(a){return a.stopPropagation(),t.spreadTabs.apply(null,arguments)}}},[s("span",[t._v("V")])]):t._e()]),t._v(" "),s("div",{staticClass:"temprow-box"},[s("temprow",{attrs:{"goods-data":t.temprowData,"license-status":t.licenseStatus||0}}),t._v(" "),s("div",{directives:[{name:"show",rawName:"v-show",value:t.isload,expression:"isload"}],staticClass:"loaing"},[t._v(t._s(t.loadingmsg))])],1)]):t._e()])])])}),i,!1,null,"720b2852",null).exports}}]);
(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[82884],{37324:function(t,a,s){t.exports=s.p+"static/img/banner.bb7c157.png"},94302:function(t,a,s){t.exports=s.p+"static/img/prod.7471837.png"},66651:function(t,a,s){t.exports=s.p+"static/img/prod1.0faeefb.png"},46762:function(t,a,s){t.exports=s.p+"static/img/prod2.cff1eb1.png"},57001:function(t,a,s){t.exports=s.p+"static/img/prod3.8813025.png"},24227:function(t,a,s){t.exports=s.p+"static/img/prod4.aaadae3.png"},6551:function(t,a,s){t.exports=s.p+"static/img/tab.b3b7def.png"},36468:function(t,a,s){"use strict";s.d(a,{Z:function(){return i}});var e={data:function(){return{isExtend:!1}},mounted:function(){},mixins:[s(61255).Z]},i=(0,s(51900).Z)(e,(function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"buybtn",style:{borderColor:t.bgcolor},attrs:{bgcolr:t.bgcolor,"data-id":t.goodsId,"is-slipt":t.issplit,"med-pack-num":t.mednum,"is-extend":t.isExtend},on:{click:function(a){return a.stopPropagation(),a.preventDefault(),t.addProductCart.apply(null,arguments)}}},[s("span",{staticClass:"min",style:{backgroundColor:t.bgcolor},attrs:{"edit-cate":"min"}}),t._v(" "),s("input",{directives:[{name:"model",rawName:"v-model",value:t.productValue,expression:"productValue"}],staticClass:"input-val",class:"input-val"+t.goodsId,attrs:{"edit-cate":"edit",type:"tel"},domProps:{value:t.productValue},on:{change:t.inputCart,click:function(a){return a.preventDefault(),a.stopPropagation(),t.androidclick.apply(null,arguments)},input:function(a){a.target.composing||(t.productValue=a.target.value)}}}),t._v(" "),s("span",{staticClass:"add",style:{backgroundColor:t.bgcolor},attrs:{"edit-cate":"add"}})])}),[],!1,null,"2f0de2f5",null).exports},77187:function(t,a,s){"use strict";s.r(a),s.d(a,{default:function(){return c}});var e=[function(){var t=this.$createElement,a=this._self._c||t;return a("div",{staticClass:"banner"},[a("img",{attrs:{src:s(37324),alt:""}})])},function(){var t=this.$createElement,a=this._self._c||t;return a("div",[a("img",{attrs:{src:s(6551),alt:""}})])}],i=s(24388),n=s(36468),o=s(59502),r={data:function(){return{hotData:[],licenseStatus:0,proNum:0,adressCode:""}},methods:{getDataList:function(t,a){var s=this;this.putRequest("post","/app/layout/initExhibitionModulePage?sort=1",{merchantId:this.merchantId,limit:1e3,offset:0,exhibitionId:t}).then((function(t){"success"==t.data.status&&(s[a]=t.data.data.rows,s.licenseStatus=t.data.data.licenseStatus),s.$nextTick((function(){o.Z.$emit("changeloading",!1)}))})).catch((function(t){}))},getAdress:function(){var t=this;this.putRequest("post","/app/getCityCode",{merchantId:this.merchantId}).then((function(a){"success"==a.data.status&&(t.adressCode=a.data.data.cityCode),t.$nextTick((function(){o.Z.$emit("changeloading",!1)}))})).catch((function(t){}))}},mounted:function(){this.getDataList("ZS201902270945393370","hotData"),this.getAdress()},components:{temprow:i.Z,cartBtn:n.Z},activated:function(){this.setAppTitle("华润三九")}},c=(0,s(51900).Z)(r,(function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{attrs:{id:"sanjiupagenewnew"}},[t._m(0),t._v(" "),e("div",{staticClass:"good-pills"},[e("img",{attrs:{src:s(94302),alt:""}}),t._v(" "),e("img",{attrs:{src:s(66651),alt:""}}),t._v(" "),"420100"==t.adressCode?e("div",{staticClass:"btn-box"},[e("cartBtn",{ref:"autotext",attrs:{"is-pack":!0,"data-id":1361,"is-split":1,"product-num":t.proNum,"med-num":1}})],1):t._e()]),t._v(" "),e("div",{staticClass:"good-pills"},[e("img",{attrs:{src:s(46762),alt:""}}),t._v(" "),"420100"==t.adressCode?e("div",{staticClass:"btn-box"},[e("cartBtn",{ref:"autotext",attrs:{"is-pack":!0,"data-id":1362,"is-split":1,"product-num":t.proNum,"med-num":1}})],1):t._e()]),t._v(" "),e("div",{staticClass:"good-pills"},[e("img",{attrs:{src:s(57001),alt:""}}),t._v(" "),"420100"==t.adressCode?e("div",{staticClass:"btn-box"},[e("cartBtn",{ref:"autotext",attrs:{"is-pack":!0,"data-id":1367,"is-split":1,"product-num":t.proNum,"med-num":1}})],1):t._e()]),t._v(" "),e("div",{staticClass:"good-pills"},[e("img",{attrs:{src:s(24227),alt:""}}),t._v(" "),"420100"==t.adressCode?e("div",{staticClass:"btn-box1"},[e("cartBtn",{ref:"autotext",attrs:{"is-pack":!0,"data-id":1366,"is-split":1,"product-num":t.proNum,"med-num":1}})],1):t._e()]),t._v(" "),e("div",{staticClass:"hot-content"},[t._m(1),t._v(" "),e("temprow",{attrs:{"goods-data":t.hotData,"license-status":t.licenseStatus||0}})],1)])}),e,!1,null,"7968d353",null).exports}}]);
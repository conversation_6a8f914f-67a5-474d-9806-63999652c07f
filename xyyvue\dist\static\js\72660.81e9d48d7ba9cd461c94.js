(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[72660],{88142:function(t,s,e){t.exports=e.p+"static/img/openappicon.9563827.png"},6469:function(t,s,e){t.exports=e.p+"static/img/ybm.ccfe8cd.png"},16183:function(t,s,e){"use strict";e.r(s),e.d(s,{default:function(){return h}});e(92881);var a=e(36689),i=e(15861),n=e(15785),r=(e(47601),e(44951)),o=(e(72975),e(98655)),c=(e(11155),e(91391)),u=e(87757),p=e.n(u),l=(e(26833),e(74916),e(4723),e(91058),e(82772),e(70538)),d=e(67087),m=(e(17761),e(81614)),v=navigator.userAgent.toLowerCase(),g=/wxwork/i.test(v);l.Z.use(c.Z),l.Z.use(o.Z),l.Z.use(r.Z);var _={name:"index",data:function(){return{isComWxOrWx:!1,merchantId:"",userId:"",isHide:!0,imgUrl:"",current:0,skuId:"",actId:"",actPt:{assembleStatus:0,assemblePrice:"",assembleStartTime:"",surplusTime:0,subTitle:"已拼团客户",assembleOrderList:[{merchantName:"用户名称",productAmount:"购买数量",createTime:"购买时间"}],percentage:0},actPgby:{},isActPt:!0,productDto:{markerUrl:"",showName:"",spec:"",availableQty:"",approvalNumber:"",farEffect:"",nearEffect:"",mediumPackageNum:"",manufacturer:"",shelfLife:"",imagesList:[],productUnit:"",retailPrice:0,drugClassification:"",drugClassificationImage:"",isThirdCompany:""},countDownInterval:null,countDownTimeAry:["00","00","00"],appLink:"",wxConfig:{id:"wxopenLanchAppId",appId:"wx5766ec723a326dff",enable:!1,extinfoStr:""},shareSign:"",isComWx:g,showBk:!1}},created:function(){var t=this.$route.query;Object.values(t).length>0&&(this.skuId=t.skuId,this.actId=t.actId,this.userId=t.user_id||"",this.merchantId=t.merchantId||"",this.shareSign=(t||{}).shareSign||""),this.imgUrl=this.imgBaseUrl,this.isComWx&&(this.showBk=!0),this.getWxConfig(),this.getShareDetail(),this.jumpurlFc();var s={skuId:this.skuId,actId:this.actId,userId:this.userId,merchantId:this.merchantId};(0,d.jy)("share_product_details",s),navigator.userAgent.match(/MicroMessenger\/([\d\.]+)/i)&&(this.isComWxOrWx=!0),this.isComWx&&(this.isComWxOrWx=!0)},methods:{openYYB:function(){location.href="//a.app.qq.com/o/simple.jsp?pkgname=com.ybmmarket20"},toLoad:function(t){},hidenBackgroundPage:function(){this.showBk=!1},browserToApp:function(){this.isComWx?this.showBk=!this.showBk:this.jumpurlFc()},stringToBase64:function(t){var s=(new TextEncoder).encode(t);return btoa(String.fromCharCode.apply(String,(0,n.Z)(s)))},jumpurlFc:function(){if(!navigator.userAgent.match(/MicroMessenger\/([\d\.]+)/i)){var t="product_id="+this.skuId;window.location.href="ybm100://ybmmarket20.com/productdetail?".concat(this.stringToBase64(t))}},onChange:function(t){this.current=t},getShareDetail:function(){var t=this;return(0,i.Z)(p().mark((function s(){var i,n,r,o,c,u,l;return p().wrap((function(s){for(;;)switch(s.prev=s.next){case 0:return s.prev=0,i={skuId:t.skuId},t.actId&&(i.actId=t.actId),t.shareSign&&(n=e(1504),"q47uyo8zkyfsu87o",r={key:"q47uyo8zkyfsu87o",mode:"ecb",cipherType:"base64"},o=new n.sm4(r),c=o.encrypt(t.shareSign),i.shareSign=c),s.next=6,t.putRequest("POST","/app/share/getShareDetail",i);case 6:u=s.sent,"success"===(l=u.data).status?(t.productDto=l.data.productDto,1==l.data.isAssemble?(t.isActPt=!0,t.actPt=l.data.actPt):1==l.data.isWholesale&&(t.isActPt=!1,t.actPgby=l.data.actPgby),t.isHide=!0):(0,a.Z)(l.msg||"请求出错！"),s.next=14;break;case 11:s.prev=11,s.t0=s.catch(0);case 14:1===t.actPt.assembleStatus&&t.actPt.surplusTime>0&&(clearInterval(t.countDownInterval),t.countDownInterval=setInterval((function(){t.actPt.surplusTime<=0&&(t.actPt.assembleStatus=2,clearInterval(t.countDownInterval)),t.getSeconds(t.actPt.surplusTime--)}),1e3));case 15:case"end":return s.stop()}}),s,null,[[0,11]])})))()},getSeconds:function(t){var s=parseInt(t),e=0,a=0;s>60&&(e=parseInt(s/60),s=parseInt(s%60),e>60&&(a=parseInt(e/60),e=parseInt(e%60)));var i=[];a>=0&&a<10?i.push("0"+parseInt(a)):i.push(""+parseInt(a)),e>=0&&e<10?i.push("0"+parseInt(e)):i.push(""+parseInt(e)),s>=0&&s<10?i.push("0"+parseInt(s)):i.push(""+parseInt(s)),this.countDownTimeAry=i},openYBM:function(t){this.browserToApp()},getWxConfig:function(){var t=this;navigator.userAgent.indexOf("iPhone");this.putRequest("post","/app/wechat/jsSdkConfig/query",{url:location.href}).then((function(s){if("success"===s.data.status){var e=s.data.data,a=e.appId,i=e.timestamp,n=e.nonceStr,r=e.signature,o="productdetail?product_id="+t.skuId;t.wxConfig.extinfoStr="ybmpage://".concat(decodeURIComponent(o)),m&&m.config({debug:!1,appId:a,timestamp:i,nonceStr:n,signature:r,jsApiList:["updateAppMessageShareData","updateTimelineShareData"],openTagList:["wx-open-launch-app"]}),m&&m.ready((function(){t.wxConfig.enable=!0,m.updateAppMessageShareData({title:"打开药帮忙",desc:"买好药",link:location.href,imgUrl:"",success:function(t){},cancel:function(){},fail:function(t){}}),m.updateTimelineShareData({title:"打开药帮忙",link:location.href,imgUrl:"",success:function(t){},cancel:function(){},fail:function(t){}})})),m&&m.error((function(t){}))}}))},handleLaunchFn:function(){(0,d.M0)("launchYbmApp-productDetail-success",{pageName:"唤起药帮忙",status:"唤起成功",skuId:this.skuId,actId:this.actId,userId:this.userId,merchantId:this.merchantId})},handleErrorFn:function(){navigator.userAgent.match(/MicroMessenger\/([\d\.]+)/i)?this.showBk=!0:location.href="//a.app.qq.com/o/simple.jsp?pkgname=com.ybmmarket20"}}},h=(0,e(51900).Z)(_,(function(){var t=this,s=t.$createElement,a=t._self._c||s;return t.isHide?a("div",{staticClass:"shareContent"},[t.isComWxOrWx?t._e():a("div",{staticClass:"toYYB"},[a("img",{staticStyle:{width:"0.8rem",height:"0.8rem","border-radius":"0.2rem"},attrs:{src:e(6469),alt:""}}),t._v(" "),t._m(0),t._v(" "),a("button",{staticClass:"openApp",staticStyle:{"z-index":"111"},on:{click:function(s){return s.stopPropagation(),t.openYYB.apply(null,arguments)}}},[t._v("下载APP")])]),t._v(" "),a("van-swipe",{staticClass:"my-swipe",attrs:{autoplay:3e3,"indicator-color":"white"},on:{change:t.onChange},scopedSlots:t._u([t.productDto.imagesList&&t.productDto.imagesList.length>0?{key:"indicator",fn:function(){return[a("div",{staticClass:"custom-indicator"},[t._v(t._s(t.current+1)+"/"+t._s(t.productDto.imagesList.length))])]},proxy:!0}:null],null,!0)},t._l(t.productDto.imagesList,(function(s,e){return a("van-swipe-item",{key:e,staticClass:"swipeItem",attrs:{data:t.imgUrl+"/ybm/product/min/"+s}},[0===e?[t.productDto.markerUrl?[a("van-image",{staticClass:"markerImg",attrs:{width:"100%",height:"4.5rem",fit:"contain",src:t.imgUrl+t.productDto.markerUrl,data:t.imgUrl+t.productDto.markerUrl}})]:t._e(),t._v(" "),a("van-image",{attrs:{width:"100%",height:"4.5rem",fit:"cover",src:t.imgUrl+"/ybm/product/min/"+s}})]:[a("van-image",{attrs:{width:"100%",height:"4.5rem",fit:"cover",src:t.imgUrl+"/ybm/product/min/"+s}})]],2)})),1),t._v(" "),t.isActPt?a("div",{staticClass:"groupTitle"},[a("div",{staticClass:"groupPrice"},[a("div",{staticClass:"groupPriceContent"},[a("span",[t._v("拼团价\n          "),t._v(" "),2==t.actPt.stepPriceStatus?a("span",{staticStyle:{"font-size":"0.32rem"}},[a("i",{staticStyle:{"font-size":"0.5rem"}},[t._v("\n              "+t._s(String(t.actPt.assemblePrice).split(".")[0]||"0")+"\n            ")]),t._v("\n            ."+t._s(String(t.actPt.assemblePrice).split(".")[1]||"00")+"元/"+t._s(t.productDto.productUnit)+"\n          ")]):t._e(),t._v(" "),1==t.actPt.stepPriceStatus?a("span",{staticStyle:{"font-size":"0.32rem"}},[a("i",{staticStyle:{"font-size":"0.5rem"}},[t._v("\n              "+t._s(String(t.actPt.minSkuPrice).split(".")[0]||"0")+"\n            ")]),t._v("\n            ."+t._s(String(t.actPt.minSkuPrice).split(".")[1]||"00")+"\n\n            "),t._v("\n            元起/"+t._s(t.productDto.productUnit)+"\n          ")]):t._e(),t._v(" "),a("span",{staticClass:"usedPrice"},[t._v(" ¥"+t._s(t.productDto.retailPrice)+" ")])])]),t._v(" "),a("i",{staticClass:"groupPrice-bg"})]),t._v(" "),a("div",{staticClass:"countDown"},[a("div",[t._v(t._s(1===t.actPt.assembleStatus?"距结束仅剩":0===t.actPt.assembleStatus?"未开始":"已结束"))]),t._v(" "),a("div",{staticClass:"downTimeBox"},[a("span",{staticClass:"downTime"},[a("i",[t._v(t._s(t.countDownTimeAry[0]))]),t._v(":"),a("i",[t._v(t._s(t.countDownTimeAry[1]))]),t._v(":"),a("i",[t._v(t._s(t.countDownTimeAry[2]))])])])])]):a("div",{staticClass:"groupTitle"},[a("div",{staticClass:"groupPrice groupPrice-pgby"},[a("div",{staticClass:"groupPriceContent"},[a("span",[t._v("包邮价\n          "),t._v(" "),a("span",{staticStyle:{"font-size":"0.32rem"}},[a("i",{staticStyle:{"font-size":"0.5rem"}},[t._v("\n              "+t._s(String(t.actPgby.assemblePrice).split(".")[0]||"0")+"\n            ")]),t._v("\n            ."+t._s(String(t.actPgby.assemblePrice).split(".")[1]||"00")+"元/"+t._s(t.productDto.productUnit)+"\n          ")]),t._v(" "),a("span",{staticClass:"usedPrice"},[t._v(" ¥"+t._s(t.productDto.retailPrice)+" ")])])]),t._v(" "),a("i",{staticClass:"groupPrice-bg"})])]),t._v(" "),a("div",{staticClass:"productInfo"},[t.productDto.actPurchaseTip?a("div",{staticStyle:{"font-size":"0.26rem",margin:"0.1rem 0.1rem",padding:"0.1rem","border-radius":"0.1rem","background-color":"#ffd5a0"}},[t._v("\n      "+t._s(t.productDto.actPurchaseTip)+"\n    ")]):t._e(),t._v(" "),0===t.productDto.isThirdCompany?a("i",{staticClass:"self-support"}):t._e(),t._v(" "),3!==t.productDto.drugClassification?[a("van-image",{staticStyle:{"vertical-align":"middle"},attrs:{width:"0.64rem",height:"0.3rem",fit:"contain",src:t.imgUrl+t.productDto.drugClassificationImage}})]:t._e(),t._v("\n    "+t._s(t.productDto.showName)+"\n    "),a("div",{staticClass:"specs"},[a("span",[t._v("规格："+t._s(t.productDto.spec))]),t._v(" "),a("span",{staticStyle:{"margin-left":"0.4rem"}},[t._v("库存："+t._s(Number(t.productDto.availableQty)>100?"大于100":t.productDto.availableQty))])])],2),t._v(" "),t.isActPt?a("div",{staticClass:"groupInfo"},[a("div",{staticClass:"grouped"},[a("i",{staticClass:"in-group"}),t._v(" "),a("span",[t._v(t._s(t.actPt.subTitle))]),t._v(" "),a("div",{staticClass:"groupProcess"},[a("span",{style:{width:t.actPt.percentage+"%"}})]),t._v(" "),a("span",{staticClass:"groupProcessNum"},[t._v(t._s(t.actPt.percentage)+"%")])]),t._v(" "),t._l(t.actPt.assembleOrderList,(function(s,e){return a("div",{key:e,staticClass:"groupUser"},[a("div",[t._v(t._s(s.merchantName)+" "),a("span",{staticStyle:{"margin-left":"0.4rem"}},[t._v("已购"+t._s(s.productAmount)+t._s(t.productDto.productUnit))])]),t._v(" "),a("span",{staticStyle:{float:"right"}},[t._v(t._s(s.createTime))])])}))],2):t._e(),t._v(" "),(t.productDto.sellingProposition1||t.productDto.sellingProposition2||t.productDto.sellingProposition3)&&t.isActPt?a("div",{staticClass:"pachyderm"},[a("div",{staticStyle:{"font-size":"0.26rem","padding-left":"0"}},[t._v("推荐理由：")]),t._v(" "),t.productDto.sellingProposition1?a("div",{staticClass:"sellingProposition1"},[t._v(t._s(t.productDto.sellingProposition1))]):t._e(),t._v(" "),t.productDto.sellingProposition2?a("div",{staticClass:"sellingProposition2"},[t._v(t._s(t.productDto.sellingProposition2))]):t._e(),t._v(" "),t.productDto.sellingProposition3?a("div",{staticClass:"sellingProposition3"},[t._v(t._s(t.productDto.sellingProposition3))]):t._e()]):t._e(),t._v(" "),a("div",{staticClass:"productDto"},[a("div",{staticClass:"productDtoRow"},[a("span",[t._v("近/远效期：")]),t._v(" "),a("span",[t._v(t._s(t.productDto.nearEffect)+"/"+t._s(t.productDto.farEffect))])]),t._v(" "),a("div",{staticClass:"productDtoRow"},[a("span",[t._v("中包装：")]),t._v(" "),a("span",[t._v(t._s(t.productDto.mediumPackageNum+t.productDto.productUnit))])]),t._v(" "),a("div",{staticClass:"productDtoRow"},[a("span",[t._v("生产厂家：")]),t._v(" "),a("span",[t._v(t._s(t.productDto.manufacturer))])]),t._v(" "),a("div",{staticClass:"productDtoRow"},[a("span",[t._v("批准文号：")]),t._v(" "),a("span",[t._v(t._s(t.productDto.approvalNumber))])]),t._v(" "),a("div",{staticClass:"productDtoRow"},[a("span",[t._v("保质期：")]),t._v(" "),a("span",[t._v(t._s(t.productDto.shelfLife))])])]),t._v(" "),a("div",{staticClass:"fixedButton"},[t.isActPt?a("div",{staticClass:"buttonBox"},[a("div",{staticClass:"joinGroup",class:0===t.actPt.assembleStatus||2===t.actPt.assembleStatus?"notStart":"",staticStyle:{"z-index":"111"},on:{click:function(s){return t.openYBM(1)}}},[t._v("\n        "+t._s(0===t.actPt.assembleStatus?"未开始":2===t.actPt.assembleStatus?"已结束":"立即参团")+"\n      ")]),t._v(" "),a("div",{staticClass:"openYBM",class:0===t.actPt.assembleStatus||2===t.actPt.assembleStatus?"notStart":"",on:{click:function(s){return t.openYBM(2)}}},[t._v("打开药帮忙APP\n      ")])]):a("div",{staticClass:"buttonBox"},[a("div",{staticClass:"joinGroup",staticStyle:{"z-index":"111"},on:{click:function(s){return t.openYBM(1)}}},[t._v("\n        立即抢购\n      ")]),t._v(" "),a("div",{staticClass:"openYBM",staticStyle:{"z-index":"111"},on:{click:function(s){return t.openYBM(2)}}},[t._v("打开药帮忙APP\n      ")])])]),t._v(" "),t.wxConfig.enable&&!t.isComWx?a("div",{staticClass:"content-share"},[a("div",{staticClass:"content-share-bottom"},[a("wx-open-launch-app",{staticClass:"launch-btn",attrs:{id:t.wxConfig.id,appid:t.wxConfig.appId,extinfo:t.wxConfig.extinfoStr},on:{error:t.handleErrorFn,launch:t.handleLaunchFn}},[a("script",{attrs:{type:"text/wxtag-template"}},[t._v('\n            <div id="launchBtn" class="btn" style="display: block;width: 2000px;height: 2000px;margin:auto"></div>\n        ')])])],1)]):t._e(),t._v(" "),t.showBk?a("div",{staticClass:"share_background-page",on:{click:t.hidenBackgroundPage}},[a("img",{attrs:{src:e(88142),alt:""}}),t._v(" "),t._m(1)]):t._e()],1):t._e()}),[function(){var t=this,s=t.$createElement,e=t._self._c||s;return e("div",{staticStyle:{flex:"1","margin-left":"0.2rem"}},[e("div",{staticStyle:{"font-size":"0.28rem","font-weight":"600"}},[t._v("\n        药帮忙\n      ")]),t._v(" "),e("div",{staticStyle:{"font-size":"0.2rem","margin-top":"0.1rem",color:"rgb(167,167,167)"}},[t._v("\n        便宜好药，当然药帮忙\n      ")])])},function(){var t=this,s=t.$createElement,e=t._self._c||s;return e("div",{staticClass:"share_background-page-row"},[e("div",{staticClass:"share_background-page-button"},[t._v("点击关闭")])])}],!1,null,"74edede9",null).exports},50511:function(){}}]);
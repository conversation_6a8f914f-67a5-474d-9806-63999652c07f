(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[75126],{35564:function(t,e,i){t.exports=i.p+"static/img/ganmao_02.1bb260d.png"},72730:function(t,e,i){"use strict";i.r(e),i.d(e,{default:function(){return l}});var s=[function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"banner"},[e("img",{attrs:{src:i(35564),alt:""}})])}],a=i(4942),n=(i(91058),i(24388)),o=i(59502),c={data:function(){var t;return t={iscur:0,temprowData:[],licenseStatus:0,isload:!1,loadingmsg:"正在加载···",tabData:[{hdid:"ZS201908231056426216",title:"感冒发烧"},{hdid:"ZS201908231056541403",title:"化痰止咳"},{hdid:"ZS201908231057084433",title:"清热解毒"}],isfixed:!1,chooseHdid:"ZS201908231056426216",tabIndex:0,scrollload:!0},(0,a.Z)(t,"isload",!1),(0,a.Z)(t,"loadingmsg","正在加载···"),(0,a.Z)(t,"pagecur",0),(0,a.Z)(t,"totalpage",0),(0,a.Z)(t,"skiptext","化痰止咳"),(0,a.Z)(t,"showBtn",!1),t},methods:{tabitemclick:function(t){document.querySelector("#pinleiqixie").scrollTop=document.querySelector(".checktab").offsetTop,this.translatetabs()},skipNexTab:function(){this.tabIndex++,this.tabIndex=this.tabIndex==this.tabData.length?0:this.tabIndex,this.translatetabs()},moveEvent:function(){var t=document.querySelector(".checktab").offsetTop,e=document.querySelector("#pinleiqixie").scrollTop;this.isfixed=e>=t,e>800&&o.Z.$emit("showBtn",{isShow:!0,dom:"#pinleiqixie"});var i=window.screen.height;document.querySelector("#pinleiqixie").scrollHeight-e<=i&&this.scrollload&&(this.scrollload=!1,this.pagecur>=this.totalpage-1?(this.isload=!1,this.showBtn=!0):(this.isload=!0,this.loadingmsg="正在加载···",this.pagecur++,this.getDatalist(this.chooseHdid,"temprowData")))},changetabs:function(t){this.tabIndex=parseInt(t.target.getAttribute("tabitem")),this.translatetabs()},translatetabs:function(){this.iscur=this.tabIndex,this.chooseHdid=this.tabData[this.tabIndex].hdid,document.querySelector("#pinleiqixie").scrollTop=document.querySelector(".checktab").offsetTop;var t=this.tabIndex+1;t=t==this.tabData.length?0:t,this.skiptext=this.tabData[t].title,this.pagecur=0,this.showBtn=!1,this.getDatalist(this.chooseHdid,"temprowData")},getDatalist:function(t,e){var i=this;this.putRequest("post","/app/layout/initExhibitionModulePage?sort=1",{merchantId:this.merchantId,limit:10,offset:this.pagecur,exhibitionId:t}).then((function(t){if("success"==t.data.status){0==i.pagecur&&(i.totalpage=t.data.data.pageCount,i[e]=[]),i.isload=!1,i.scrollload=!0;var s=t.data.data.rows;i[e].push.apply(i[e],s),i.licenseStatus=t.data.data.licenseStatus}i.$nextTick((function(){o.Z.$emit("changeloading",!1)}))})).catch((function(t){}))}},mounted:function(){document.querySelector("#pinleiqixie").addEventListener("scroll",this.moveEvent),this.getDatalist("ZS201908231056426216","temprowData")},components:{temprow:n.Z},activated:function(){this.setAppTitle("感冒用药")}},l=(0,i(51900).Z)(c,(function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{attrs:{id:"pinleiqixie"}},[t._m(0),t._v(" "),i("div",{staticClass:"checktab"},[i("div",{staticClass:"tabs-box",class:{tabfixed:t.isfixed},on:{click:t.changetabs}},[i("ul",{staticClass:"tab-scroll"},t._l(t.tabData,(function(e,s){return i("li",{key:s,staticClass:"tab-item",class:{cur:t.iscur==s},attrs:{tabitem:s}},[t._v(t._s(e.title)),i("i")])})),0)])]),t._v(" "),i("div",{staticClass:"temprow-box"},[i("temprow",{attrs:{"goods-data":t.temprowData,"license-status":t.licenseStatus||0}}),t._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:t.isload,expression:"isload"}],staticClass:"loaing"},[t._v(t._s(t.loadingmsg))])],1),t._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:t.showBtn,expression:"showBtn"}],staticClass:"highmargin-btn-box"},[i("div",{staticClass:"highmargin-btn",on:{click:t.skipNexTab}},[t._v("点击跳转至“"+t._s(t.skiptext)+"”")])])])}),s,!1,null,"4748bc5e",null).exports}}]);
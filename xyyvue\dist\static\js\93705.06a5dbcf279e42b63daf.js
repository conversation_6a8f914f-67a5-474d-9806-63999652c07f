(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[93705],{20828:function(t,s,a){t.exports=a.p+"static/img/app_cq_01.1731138.png"},54333:function(t,s,a){"use strict";a.d(s,{Z:function(){return e}});var i={data:function(){return{isShow:!0,progressNumber:"",progressNumber1:"",progressNumber2:""}},methods:{progress:function(){var t=this;this.putRequest("post","/app/activityPackage/showSchedule",{merchantId:this.merchantId}).then((function(s){"success"==s.data.status?(t.isShow=!0,t.progressNumber1=s.data.result.completeMoney,t.progressNumber2=s.data.result.totalMoney,t.progressNumber=Math.round(t.progressNumber1/t.progressNumber2*1e4)/100+"%"):t.isShow=!1})).catch((function(s){t.isShow=!1}))}},mounted:function(){this.progress()},created:function(){}},e=(0,a(51900).Z)(i,(function(){var t=this,s=t.$createElement,a=t._self._c||s;return t.isShow?a("div",{staticClass:"progress-bar"},[a("span",[t._v("协议完成进度")]),t._v(" "),a("div",{staticClass:"progress"},[a("div",{staticClass:"bar",style:{width:t.progressNumber}}),t._v(" "),a("span",[t._v("已完成"+t._s(t.progressNumber1)+"/"+t._s(t.progressNumber2)+"元")])])]):t._e()}),[],!1,null,"52d75148",null).exports},95712:function(t,s,a){"use strict";a.r(s),a.d(s,{default:function(){return c}});var i=a(4942),e=(a(91058),a(24388)),n=a(59502),r=a(54333),o={data:function(){var t;return t={iscur:0,licenseStatus:0,isShow:!0,isarrow:!0,temprowData:[],isload:!1,loadingmsg:"正在加载···",initData:[{hdid:"ZS201810261522524744",title:"慢病用药"},{hdid:"ZS201810261525071345",title:"感冒用药"},{hdid:"ZS201810261523221244",title:"风湿骨痛"},{hdid:"ZS201901311822508030",title:"皮肤外用"},{hdid:"ZS201810261524338470",title:"呼吸系统"},{hdid:"ZS201810261524066636",title:"消化系统"},{hdid:"ZS201810261521397730",title:"抗菌消炎"},{hdid:"ZS201810261525345990",title:"其他药品"}],tehuiData:[{hdid:"ZS201810261522524744",title:"慢病用药"},{hdid:"ZS201810261525071345",title:"感冒用药"},{hdid:"ZS201810261523221244",title:"风湿骨痛"},{hdid:"ZS201901311822508030",title:"皮肤外用"},{hdid:"ZS201810261524338470",title:"呼吸系统"},{hdid:"ZS201810261524066636",title:"消化系统"},{hdid:"ZS201810261521397730",title:"抗菌消炎"},{hdid:"ZS201810261525345990",title:"其他药品"}],tabData:[{hdid:"ZS201903271357338640",title:"优惠<10%"},{hdid:"ZS201903271357464813",title:"优惠10%~30%"}],isfixed:!1,chooseHdid:"ZS201810261522524744",translateleft:0,tabIndex:0,scrollload:!0},(0,i.Z)(t,"isload",!1),(0,i.Z)(t,"loadingmsg","正在加载···"),(0,i.Z)(t,"pagecur",0),(0,i.Z)(t,"totalpage",0),(0,i.Z)(t,"showBtn",!1),(0,i.Z)(t,"startX",0),(0,i.Z)(t,"endX",0),(0,i.Z)(t,"isCurTab","spc-content"),t},methods:{tabitemclick:function(t){var s=t.currentTarget.getAttribute("contClass");this.isCurTab=s,"spc-content"==this.isCurTab?(this.initData=this.tehuiData,this.iscur=0,this.tabIndex=0,this.translatetabs()):"manjian-content"==this.isCurTab&&(this.initData=this.tabData,this.isarrow=!0,this.iscur=0,this.tabIndex=0,this.translatetabs())},spreadTabs:function(){this.isarrow=!this.isarrow},touchstartTabs:function(t){this.startX=t.touches[0].pageX},touchmoveTabs:function(t){var s=t.touches[0].pageX-this.startX;this.startX=t.touches[0].pageX,this.translateleft+=s,this.initData.length<=4&&(this.translateleft=0),this.translateleft>=0&&(this.translateleft=0),this.translateleft<=-300&&(this.translateleft=-300)},moveEvent:function(){var t=document.querySelector(".checktab").offsetTop,s=document.querySelector("#highmarginchongqing").scrollTop;this.isfixed=s>=t,this.isarrow=!0;try{window.webkit.messageHandlers.hideKeyboard.postMessage({hide:"1"})}catch(t){}var a=window.screen.height;document.querySelector(".highmarginchongqing-content").scrollHeight-s-400<=a&&this.scrollload&&(this.scrollload=!1,this.pagecur>=this.totalpage-1?(this.isload=!1,this.showBtn=!0):(this.isload=!0,this.loadingmsg="正在加载···",this.pagecur++,this.getDatalist(this.chooseHdid,"temprowData")))},changetabs:function(t){this.tabIndex=parseInt(t.target.getAttribute("tabitem")),(this.tabIndex||0==this.tabIndex)&&(this.translatetabs(),this.isarrow=!0)},translatetabs:function(){this.iscur=this.tabIndex,this.chooseHdid=this.initData[this.tabIndex].hdid,this.tabIndex>=2&&"spc-content"==this.isCurTab?this.translateleft=-65*(this.tabIndex-1):this.translateleft=0,this.pagecur=0,this.showBtn=!1,this.getDatalist(this.chooseHdid,"temprowData")},getDatalist:function(t,s){var a=this;this.putRequest("post","/app/layout/initExhibitionModulePage?sort=1",{merchantId:this.merchantId,limit:10,offset:this.pagecur,exhibitionId:t}).then((function(t){if("success"==t.data.status){0==a.pagecur&&(a.totalpage=t.data.data.pageCount,a[s]=[]),a.isload=!1,a.scrollload=!0;var i=t.data.data.rows;a[s].push.apply(a[s],i),a.licenseStatus=t.data.data.licenseStatus}a.$nextTick((function(){n.Z.$emit("changeloading",!1)}))})).catch((function(t){}))}},mounted:function(){this.getDatalist(this.chooseHdid,"temprowData")},components:{temprow:e.Z,progRess:r.Z},activated:function(){this.setAppTitle("高毛专区")}},c=(0,a(51900).Z)(o,(function(){var t=this,s=t.$createElement,i=t._self._c||s;return i("div",{attrs:{id:"highmarginchongqing"}},[i("div",{staticClass:"highmarginchongqing-content",on:{touchmove:t.moveEvent}},[i("div",{staticClass:"banner"},[i("img",{attrs:{src:a(20828),alt:""}}),t._v(" "),i("progRess")],1),t._v(" "),i("div",{staticClass:"checktab"},[i("div",{staticClass:"tab-list",class:{tabfixed:t.isfixed}},[i("div",{staticClass:"tab-item spc-content",class:{tabclickcolor:"spc-content"==t.isCurTab},attrs:{contClass:"spc-content"},on:{click:t.tabitemclick}},[t._v("最高满699减35")]),t._v(" "),i("div",{staticClass:"tab-item manjian-content",class:{tabclickcolor:"manjian-content"==t.isCurTab},attrs:{contClass:"manjian-content"},on:{click:t.tabitemclick}},[t._v("单品满减专区")])]),t._v(" "),t.isShow?i("div",{staticClass:"single-list"},[i("div",{staticClass:"tab-title",class:{tabfixed:t.isfixed},on:{click:t.changetabs}},[t.isarrow?i("div",{staticClass:"tab-section"},[i("ul",{staticClass:"tab-scroll",style:{transform:["translateX("+t.translateleft+"px)","-webkit-translateX("+t.translateleft+"px)"]},on:{touchstart:t.touchstartTabs,touchmove:t.touchmoveTabs}},t._l(t.initData,(function(s,a){return i("li",{key:a,staticClass:"tab-title-item",class:{cur:t.iscur==a},attrs:{tabitem:a}},[t._v(t._s(s.title))])})),0)]):t._e(),t._v(" "),t.isarrow?t._e():i("div",{staticClass:"tab-more-section"},[i("div",{staticClass:"title"},[i("span",[t._v("精选分类")]),t._v(" "),i("em",{on:{click:function(s){return s.stopPropagation(),t.spreadTabs.apply(null,arguments)}}},[t._v("X")])]),t._v(" "),i("ul",{staticClass:"tab-more"},t._l(t.initData,(function(s,a){return i("li",{key:a,staticClass:"tab-more-item",class:{curcur:t.iscur==a},attrs:{tabitem:a}},[t._v(t._s(s.title))])})),0)]),t._v(" "),t.isarrow&&"spc-content"==t.isCurTab?i("div",{staticClass:"tab-arrow",class:{arrow:!t.isarrow},on:{click:function(s){return s.stopPropagation(),t.spreadTabs.apply(null,arguments)}}},[i("span",[t._v("∨")])]):t._e()]),t._v(" "),i("div",{staticClass:"temprow-box"},[i("temprow",{attrs:{"goods-data":t.temprowData,"license-status":t.licenseStatus||0}}),t._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:t.isload,expression:"isload"}],staticClass:"loaing"},[t._v(t._s(t.loadingmsg))])],1)]):t._e()])])])}),[],!1,null,"5787c9b4",null).exports}}]);
"use strict";(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[76209],{18745:function(t,s,a){a.r(s),a.d(s,{default:function(){return o}});a(82772),a(91058),a(47042);var e=a(59502),i={data:function(){return{istelTip:!1,bankName:"",cardNum:"",totalmoney:"",inputfocus:!1,depositMoney:"",telphoneNo:"",showToast:!1,toastContent:""}},methods:{moneyFoucs:function(){this.inputfocus=!0},moneyBlur:function(){this.inputfocus=!1,this.depositMoney<100?e.Z.$emit("changeprompt",{dialog:"提现金须大于100",showprompt:!0}):this.depositMoney>this.totalmoney?e.Z.$emit("changeprompt",{dialog:"提现金额不能超过可用余额",showprompt:!0}):this.depositMoney.indexOf(".")>-1&&(e.Z.$emit("changeprompt",{dialog:"提现金额须为整数",showprompt:!0}),this.depositMoney=parseInt(this.depositMoney))},telphoneFocus:function(){this.istelTip=!0},telphoneBlur:function(){""!=this.telphoneNo&&(this.istelTip=!1)},closeToast:function(){this.showToast=!1},amendAptitude:function(){this.$router.push({path:"/qualification",query:{checkstate:3}})},getDatalist:function(){var t=this;this.putRequest("post","/app/bank/query",{merchantId:this.merchantId}).then((function(s){"success"==s.data.status&&(t.bankName=s.data.data.bankName,t.cardNum=s.data.data.cardNo.slice(-4),t.totalmoney=s.data.data.balance)})).catch((function(t){}))},submitData:function(){var t=this;this.depositMoney<100?e.Z.$emit("changeprompt",{dialog:"提现金须大于100",showprompt:!0}):this.depositMoney>this.totalmoney?e.Z.$emit("changeprompt",{dialog:"提现金额不能超过可用余额",showprompt:!0}):this.putRequest("post","/app/bank/submit",{merchantId:this.merchantId,balanceJournal:this.depositMoney,phone:this.telphoneNo}).then((function(s){"success"==s.data.status&&("FAIL"==s.data.data.RESULT?(t.showToast=!0,t.toastContent=s.data.data.MSG):t.$router.push({path:"/withdrawdetail",query:{id:s.data.data.merchantBalanceJournalId}}))})).catch((function(t){}))}},activated:function(){this.getDatalist(),this.depositMoney="",this.setAppTitle("余额提现")}},o=(0,a(51900).Z)(i,(function(){var t=this,s=t.$createElement,a=t._self._c||s;return a("div",{attrs:{id:"withdrawdeposit"}},[a("div",{staticClass:"padding"}),t._v(" "),a("div",{staticClass:"bank-card-msg"},[a("div",{staticClass:"text"},[t._v("提现到")]),t._v(" "),a("div",{staticClass:"bank-detail"},[a("div",{staticClass:"bank-name"},[t._v(t._s(t.bankName))]),t._v(" "),a("div",{staticClass:"cardnum"},[t._v("尾号"+t._s(t.cardNum)+"储蓄卡")])])]),t._v(" "),a("div",{staticClass:"withdrawal"},[a("div",{staticClass:"title"},[t._v("提现金额")]),t._v(" "),a("div",{staticClass:"edit-count clearfixed"},[a("div",{staticClass:"token"},[t._v("￥")]),t._v(" "),a("input",{directives:[{name:"model",rawName:"v-model",value:t.depositMoney,expression:"depositMoney"}],staticClass:"editipt",class:{oninput:t.inputfocus||""!=this.depositMoney},attrs:{type:"tel",placeholder:"请输入金额"},domProps:{value:t.depositMoney},on:{focus:t.moneyFoucs,blur:t.moneyBlur,input:function(s){s.target.composing||(t.depositMoney=s.target.value)}}})]),t._v(" "),a("div",{staticClass:"bottom-box clearfixed"},[a("div",{staticClass:"totalmoney"},[t._v("可用余额"),a("span",{staticClass:"balance"},[t._v(t._s(t.totalmoney))]),t._v("元")]),t._v(" "),a("div",{staticClass:"getall"},[t._v("最低提现金额为100元")])])]),t._v(" "),a("div",{staticClass:"eidt-phone"},[a("div",{staticClass:"text",class:{oninputtext:t.istelTip||""!=this.telphoneNo}},[t._v("联系电话")]),t._v(" "),a("input",{directives:[{name:"model",rawName:"v-model",value:t.telphoneNo,expression:"telphoneNo"}],staticClass:"telphone",attrs:{type:"tel",placeholder:"请输入联系电话"},domProps:{value:t.telphoneNo},on:{focus:t.telphoneFocus,blur:t.telphoneBlur,input:function(s){s.target.composing||(t.telphoneNo=s.target.value)}}})]),t._v(" "),a("div",{staticClass:"submit",on:{click:t.submitData}},[t._v("确认提现")]),t._v(" "),t.showToast?a("div",{staticClass:"toast-box",on:{click:function(s){return s.stopPropagation(),t.closeToast.apply(null,arguments)}}},[a("div",{staticClass:"toast"},[a("div",{staticClass:"title"},[t._v("提示")]),t._v(" "),a("div",{staticClass:"content"},[t._v(t._s(t.toastContent))]),t._v(" "),a("div",{staticClass:"button-box"},[a("div",{staticClass:"cancel"},[t._v("暂不修改")]),t._v(" "),a("div",{staticClass:"editCard",on:{click:t.amendAptitude}},[t._v("修改")])])])]):t._e()])}),[],!1,null,"7c288461",null).exports}}]);
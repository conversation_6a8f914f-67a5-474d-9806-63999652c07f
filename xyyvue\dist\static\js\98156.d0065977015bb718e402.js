(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[98156],{51447:function(t,e,a){t.exports=a.p+"static/img/yiyuan_02.6c353e0.png"},81917:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return l}});var i=[function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"banner"},[e("img",{attrs:{src:a(51447),alt:""}})])}],s=a(4942),n=(a(91058),a(24388)),o=a(59502),c={data:function(){var t;return t={iscur:0,temprowData:[],licenseStatus:0,isload:!1,loadingmsg:"正在加载···",tabData:[{hdid:"ZS201908231059101829",title:"慢病用药"},{hdid:"ZS201908231059262844",title:"抗菌消炎"},{hdid:"ZS201908231059405380",title:"其他热销"}],isfixed:!1,chooseHdid:"ZS201908231059101829",tabIndex:0,scrollload:!0},(0,s.Z)(t,"isload",!1),(0,s.Z)(t,"loadingmsg","正在加载···"),(0,s.Z)(t,"pagecur",0),(0,s.Z)(t,"totalpage",0),(0,s.Z)(t,"skiptext","抗菌消炎"),(0,s.Z)(t,"showBtn",!1),t},methods:{tabitemclick:function(t){document.querySelector("#pinleiyiyuannew").scrollTop=document.querySelector(".checktab").offsetTop,this.translatetabs()},skipNexTab:function(){this.tabIndex++,this.tabIndex=this.tabIndex==this.tabData.length?0:this.tabIndex,this.translatetabs()},moveEvent:function(){var t=document.querySelector(".checktab").offsetTop,e=document.querySelector("#pinleiyiyuannew").scrollTop;this.isfixed=e>=t,e>800&&o.Z.$emit("showBtn",{isShow:!0,dom:"#pinleiyiyuannew"});var a=window.screen.height;document.querySelector("#pinleiyiyuannew").scrollHeight-e<=a&&this.scrollload&&(this.scrollload=!1,this.pagecur>=this.totalpage-1?(this.isload=!1,this.showBtn=!0):(this.isload=!0,this.loadingmsg="正在加载···",this.pagecur++,this.getDatalist(this.chooseHdid,"temprowData")))},changetabs:function(t){this.tabIndex=parseInt(t.target.getAttribute("tabitem")),this.translatetabs()},translatetabs:function(){this.iscur=this.tabIndex,this.chooseHdid=this.tabData[this.tabIndex].hdid,document.querySelector("#pinleiyiyuannew").scrollTop=document.querySelector(".checktab").offsetTop;var t=this.tabIndex+1;t=t==this.tabData.length?0:t,this.skiptext=this.tabData[t].title,this.pagecur=0,this.showBtn=!1,this.getDatalist(this.chooseHdid,"temprowData")},getDatalist:function(t,e){var a=this;this.putRequest("post","/app/layout/initExhibitionModulePage?sort=1",{merchantId:this.merchantId,limit:10,offset:this.pagecur,exhibitionId:t}).then((function(t){if("success"==t.data.status){0==a.pagecur&&(a.totalpage=t.data.data.pageCount,a[e]=[]),a.isload=!1,a.scrollload=!0;var i=t.data.data.rows;a[e].push.apply(a[e],i),a.licenseStatus=t.data.data.licenseStatus}a.$nextTick((function(){o.Z.$emit("changeloading",!1)}))})).catch((function(t){}))}},mounted:function(){document.querySelector("#pinleiyiyuannew").addEventListener("scroll",this.moveEvent),this.getDatalist("ZS201908231059101829","temprowData")},components:{temprow:n.Z},activated:function(){this.setAppTitle("医院品种专区")}},l=(0,a(51900).Z)(c,(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{attrs:{id:"pinleiyiyuannew"}},[t._m(0),t._v(" "),a("div",{staticClass:"checktab"},[a("div",{staticClass:"tabs-box",class:{tabfixed:t.isfixed},on:{click:t.changetabs}},[a("ul",{staticClass:"tab-scroll"},t._l(t.tabData,(function(e,i){return a("li",{key:i,staticClass:"tab-item",class:{cur:t.iscur==i},attrs:{tabitem:i}},[t._v(t._s(e.title)),a("i")])})),0)])]),t._v(" "),a("div",{staticClass:"temprow-box"},[a("temprow",{attrs:{"goods-data":t.temprowData,"license-status":t.licenseStatus||0}}),t._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:t.isload,expression:"isload"}],staticClass:"loaing"},[t._v(t._s(t.loadingmsg))])],1),t._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:t.showBtn,expression:"showBtn"}],staticClass:"highmargin-btn-box"},[a("div",{staticClass:"highmargin-btn",on:{click:t.skipNexTab}},[t._v("点击跳转至“"+t._s(t.skiptext)+"”")])])])}),i,!1,null,"23226e22",null).exports}}]);
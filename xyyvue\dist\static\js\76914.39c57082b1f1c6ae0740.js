(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[76914],{84964:function(t,e,n){var a=n(5112)("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(n){try{return e[a]=!1,"/./"[t](e)}catch(t){}}return!1}},3929:function(t,e,n){var a=n(47850);t.exports=function(t){if(a(t))throw TypeError("The method doesn't accept regular expressions");return t}},32023:function(t,e,n){"use strict";var a=n(82109),i=n(3929),r=n(84488),o=n(41340);a({target:"String",proto:!0,forced:!n(84964)("includes")},{includes:function(t){return!!~o(r(this)).indexOf(o(i(t)),arguments.length>1?arguments[1]:void 0)}})},81611:function(t,e,n){t.exports=n.p+"static/img/launch-open.1884a9f.png"},88142:function(t,e,n){t.exports=n.p+"static/img/openappicon.9563827.png"},6469:function(t,e,n){t.exports=n.p+"static/img/ybm.ccfe8cd.png"},76914:function(t,e,n){"use strict";n.r(e),n.d(e,{default:function(){return u}});var a=n(15785),i=(n(26699),n(32023),n(74916),n(4723),n(23123),n(82772),n(92222),n(67087)),r=navigator.userAgent.toLowerCase(),o=/wxwork/i.test(r),s=n(81614),c={name:"launchYbmApp",data:function(){return{isComWxOrWx:!1,id:"wxopenLanchAppId",appId:"wx5766ec723a326dff",enable:!1,extinfoStr:"",showBk:!1,isBrowser:!1,isComWx:o}},created:function(){var t=this.$route.query,e=t.jumpurl,n=t.appurl,a={sptype:4,spid:"唤起药帮忙app",userId:t.user_id||"",merchantId:t.merchantId||""};if(e&&(a.jumpurl=e),n&&(a.appurl=n,n.includes("productdetail"))){var r=n.match(/\d+/g)[0];r&&(a.skuId=r)}if((0,i.jy)("launchYbmApp-query-pv",a),this.isComWx)this.showBk=!0;else{this.getWxConfig();var o=navigator.userAgent.match(/MicroMessenger\/([\d\.]+)/i);if(o){var s=o[1];if(s){var c=s.split(".");+c[0]>=7&&+c[1]>=0?7==+c[0]&&0==+c[1]&&(+c[2]>=12||(location.href="//a.app.qq.com/o/simple.jsp?pkgname=com.ybmmarket20")):location.href="//a.app.qq.com/o/simple.jsp?pkgname=com.ybmmarket20"}}}navigator.userAgent.match(/MicroMessenger\/([\d\.]+)/i)&&(this.isComWxOrWx=!0),this.isComWx&&(this.isComWxOrWx=!0)},mounted:function(){this.jumpurlFc()},methods:{openYYB:function(){location.href="//a.app.qq.com/o/simple.jsp?pkgname=com.ybmmarket20"},browserToApp:function(){this.isComWx?this.showBk=!this.showBk:this.jumpurlFc()},stringToBase64:function(t){var e=(new TextEncoder).encode(t);return btoa(String.fromCharCode.apply(String,(0,a.Z)(e)))},toLoad:function(){},jumpurlFc:function(){if(!navigator.userAgent.match(/MicroMessenger\/([\d\.]+)/i)){this.isBrowser=!0;var t=navigator.userAgent.indexOf("iPhone")>=0,e=this.$route.query,n=e.jumpurl,a=e.appurl;if(n)if(t){var i="url="+decodeURIComponent(n);window.location.href="ybm100://ybmmarket20.com/commonh5activity?".concat(this.stringToBase64(i))}else window.location.href="ybm100://ybmmarket20.com/commonh5activity?cache=0&url=".concat(n);else if(a){var r=decodeURIComponent(this.checkUrlParams(this.$route.query)),o=[];if(o=r&&r.split("?"),t){var s="";s=o&&o[1].indexOf("orgId")>-1?"ybm100://ybmmarket20.com/shopactivity?".concat(o.length>1&&this.stringToBase64(o[1])):o&&o[1].indexOf("shopCode")>-1?"ybm100://ybmmarket20.com/searchresult?".concat(o.length>1&&this.stringToBase64(o[1])):"ybm100://ybmmarket20.com/productdetail?".concat(o.length>1&&this.stringToBase64(o[1])),window.location.href=s}else o&&o[0].indexOf("productdetail")>-1?window.location.href="ybm100://ybmmarket20.com/productdetail?".concat(o.length>1&&this.stringToBase64(o[1])):window.location.href="ybm100://ybmmarket20.com/searchresult?".concat(o.length>1&&this.stringToBase64(o[1]))}}},hidenBackgroundPage:function(){this.showBk=!1},getOtherPage:function(){this.putRequest("get","https://app-v4.ybm100.com/public/2011/11/9999999999.html")},checkUrlParams:function(t){if(t){var e="";for(var n in t)e+="appurl"!=n&&"jumpurl"!=n?"&"+n+"="+t[n]:t[n];return e}},getWxConfig:function(){var t=this,e=navigator.userAgent.indexOf("iPhone")>=0,n=location.href;e&&(n=location.href.split("#")[0]),this.putRequest("post","/app/wechat/jsSdkConfig/query",{url:n}).then((function(e){if("success"===e.data.status){var n=e.data.data,a=n.appId,i=n.timestamp,r=n.nonceStr,o=n.signature,c=t.$route.query,u=c.jumpurl,p=c.appurl;u?(-1===(u=decodeURIComponent(u)).indexOf("18681")&&-1===u.indexOf("18691")||(t.getOtherPage(),u="".concat(u).concat(-1!==u.indexOf("?")?"&fromLaunchApp=1":"?fromLaunchApp=1")),t.extinfoStr="ybmpage://commonh5activity?cache=0&url=".concat(u)):p&&(t.extinfoStr="ybmpage://".concat(decodeURIComponent(t.checkUrlParams(t.$route.query)))),s&&s.config({debug:!1,appId:a,timestamp:i,nonceStr:r,signature:o,jsApiList:["updateAppMessageShareData","updateTimelineShareData"],openTagList:["wx-open-launch-app"]}),s&&s.ready((function(e){t.enable=!0,s.updateAppMessageShareData({title:"打开药帮忙",desc:"买好药",link:location.href,imgUrl:"",success:function(t){},cancel:function(){},fail:function(t){}}),s.updateTimelineShareData({title:"打开药帮忙",link:location.href,imgUrl:"",success:function(t){},cancel:function(){},fail:function(t){}})})),s&&s.error((function(t){}))}}))},handleLaunchFn:function(){var t=this.$route.query,e=t.jumpurl,n=t.appurl,a={pageName:"唤起药帮忙",status:"唤起成功",userId:t.user_id||"",merchantId:t.merchantId||""};if(e&&(a.jumpurl=e),n&&(a.appurl=n,n.includes("productdetail"))){var r=n.match(/\d+/g)[0];r&&(a.skuId=r)}(0,i.M0)("launchYbmApp-success",a)},handleErrorFn:function(t){navigator.userAgent.match(/MicroMessenger\/([\d\.]+)/i)?this.showBk=!0:location.href="//a.app.qq.com/o/simple.jsp?pkgname=com.ybmmarket20"}}},u=(0,n(51900).Z)(c,(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"launch-container"},[t.isComWxOrWx?t._e():a("div",{staticClass:"toYYB"},[a("img",{staticStyle:{width:"0.8rem",height:"0.8rem","border-radius":"0.2rem"},attrs:{src:n(6469),alt:""}}),t._v(" "),t._m(0),t._v(" "),a("button",{staticClass:"openApp",staticStyle:{"z-index":"111"},on:{click:t.openYYB}},[t._v("下载APP")])]),t._v(" "),t.enable?a("div",{staticClass:"content"},[a("div",{staticClass:"content-button"}),t._v(" "),a("div",{staticClass:"content-bottom"},[a("wx-open-launch-app",{staticClass:"launch-btn",attrs:{id:t.id,appid:t.appId,extinfo:t.extinfoStr},on:{error:t.handleErrorFn,launch:t.handleLaunchFn}},[a("script",{attrs:{type:"text/wxtag-template"}},[t._v('\n          <div id="launchBtn" class="btn" style="display: block;width: 2000px;height: 2000px;margin:auto"></div>\n        ')])])],1)]):t._e(),t._v(" "),t.showBk?a("div",{staticClass:"share_background-page",on:{click:t.hidenBackgroundPage}},[a("img",{attrs:{src:n(88142),alt:""}}),t._v(" "),t._m(1)]):t._e(),t._v(" "),t.isBrowser||t.isComWx?a("div",{staticClass:"browser-btn",on:{click:t.browserToApp}},[a("div",{staticClass:"browser-btn-img"},[a("img",{directives:[{name:"show",rawName:"v-show",value:t.isBrowser,expression:"isBrowser"}],attrs:{src:n(81611),alt:""}})])]):t._e()])}),[function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticStyle:{flex:"1","margin-left":"0.2rem"}},[n("div",{staticStyle:{"font-size":"0.28rem","font-weight":"600"}},[t._v("\n        药帮忙\n      ")]),t._v(" "),n("div",{staticStyle:{"font-size":"0.2rem","margin-top":"0.1rem",color:"rgb(167,167,167)"}},[t._v("\n        便宜好药，当然药帮忙\n      ")])])},function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"share_background-page-row"},[n("div",{staticClass:"share_background-page-button"},[t._v("点击关闭")])])}],!1,null,"02a584e1",null).exports}}]);
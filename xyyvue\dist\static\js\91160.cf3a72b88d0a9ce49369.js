(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[91160],{34261:function(t,s,a){t.exports=a.p+"static/img/tianshil_01.42b2b65.png"},8273:function(t,s,a){t.exports=a.p+"static/img/tianshil_02.42f389f.png"},12924:function(t,s,a){"use strict";a.r(s),a.d(s,{default:function(){return o}});var e=[function(){var t=this.$createElement,s=this._self._c||t;return s("div",{staticClass:"banner"},[s("img",{attrs:{src:a(34261),alt:""}})])}],i=a(24388),n=a(59502),c={data:function(){return{licenseStatus:0,hotData:[]}},methods:{getDataList:function(t,s){var a=this;this.putRequest("post","/app/layout/initExhibitionModulePage?sort=1",{merchantId:this.merchantId,limit:1e3,offset:0,exhibitionId:t}).then((function(t){"success"==t.data.status&&(a[s]=t.data.data.rows,a.licenseStatus=t.data.data.licenseStatus),a.$nextTick((function(){n.Z.$emit("changeloading",!1)}))})).catch((function(t){}))}},mounted:function(){this.getDataList("ZS201904031347492624","hotData")},activated:function(){this.setAppTitle("华润三九")},components:{temprow:i.Z}},o=(0,a(51900).Z)(c,(function(){var t=this,s=t.$createElement,e=t._self._c||s;return e("div",{attrs:{id:"sanjiu"}},[t._m(0),t._v(" "),e("div",{staticClass:"good-pills"},[e("img",{attrs:{src:a(8273),alt:""}}),t._v(" "),e("router-link",{staticClass:"quan",attrs:{to:"/voucherscenter?ybm_title=领券中心"}})],1),t._v(" "),e("div",{staticClass:"hot-content"},[e("temprow",{attrs:{"goods-data":t.hotData,"license-status":t.licenseStatus||0}})],1)])}),e,!1,null,"203fb786",null).exports}}]);
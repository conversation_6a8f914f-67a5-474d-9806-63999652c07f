(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[99669],{4494:function(t,a,s){t.exports=s.p+"static/img/danpinmanjian.91f7292.png"},43674:function(t,a,s){"use strict";s.r(a),s.d(a,{default:function(){return o}});var i=[function(){var t=this.$createElement,a=this._self._c||t;return a("div",{staticClass:"banner"},[a("img",{attrs:{src:s(4494),alt:""}})])}],e=(s(91058),s(24388)),n=s(59502),c={data:function(){return{isCurTab:"recom-content",isfixed:!1,isarrow:!0,zjData:[],recomData:[],tabData:[],iscur:0,translateleft:0,licenseStatus:0,chooseHdid:"ZS201903121011415060"}},methods:{getDataList:function(t,a){var s=this;this.putRequest("post","/app/layout/initExhibitionModulePage?sort=1",{merchantId:this.merchantId,limit:1e3,offset:0,exhibitionId:t}).then((function(t){"success"==t.data.status&&(s[a]=t.data.data.rows,s.licenseStatus=t.data.data.licenseStatus),s.$nextTick((function(){n.Z.$emit("changeloading",!1)}))})).catch((function(t){}))},moveEvent:function(){var t=document.querySelector("#danpinmanjian").scrollTop,a=document.querySelector(".tabs-box").offsetTop;document.querySelector(".tabs-box").offsetHeight;this.isfixed=t>=a,t>800?n.Z.$emit("showBtn",{isShow:!0,dom:"#danpinmanjian"}):n.Z.$emit("showBtn",{isShow:!1,dom:""})},spreadTabs:function(){this.isarrow=!this.isarrow},touchstartTabs:function(t){this.startX=t.touches[0].pageX},touchmoveTabs:function(t){var a=t.touches[0].pageX-this.startX;this.startX=t.touches[0].pageX,this.translateleft+=a,this.translateleft>=0&&(this.translateleft=0),this.translateleft<=-50&&(this.translateleft=-50)},tabitemclick:function(t){var a=t.currentTarget.getAttribute("contClass");this.isCurTab=a,"zj-content"==this.isCurTab?(this.tabData=[{hdid:"ZS201903121011536406",title:"呼吸系统"},{hdid:"ZS201903121012032974",title:"抗菌消炎"},{hdid:"ZS201903121012141662",title:"清热解毒"},{hdid:"ZS201903121012255528",title:"皮肤用药"},{hdid:"ZS201903121012497535",title:"更多优惠"}],this.chooseHdid="ZS201903121011536406",this.getDataList("ZS201903121011536406","recomData")):(this.tabData=[],this.getDataList("ZS201903121011415060","recomData"))},changetabs:function(t){this.tabIndex=parseInt(t.target.getAttribute("tabitem")),(this.tabIndex||0==this.tabIndex)&&(this.translatetabs(),this.isarrow=!0)},translatetabs:function(){this.iscur=this.tabIndex,this.chooseHdid=this.tabData[this.tabIndex].hdid,this.tabIndex>=2?this.translateleft=-65*(this.tabIndex-1):this.translateleft=0,this.pagecur=0,this.getDataList(this.chooseHdid,"recomData")}},mounted:function(){this.getDataList("ZS201903121011415060","recomData")},components:{temprow:e.Z},activated:function(){this.setAppTitle("八折起专区")}},o=(0,s(51900).Z)(c,(function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{attrs:{id:"danpinmanjian"},on:{touchmove:t.moveEvent}},[t._m(0),t._v(" "),s("div",{staticClass:"tabs-box"},[s("div",{staticClass:"tab-list",class:{tabfixed:t.isfixed}},[s("div",{staticClass:"tab-item",class:{tabclickcolor:"recom-content"==t.isCurTab},attrs:{contClass:"recom-content"},on:{click:t.tabitemclick}},[s("span",[t._v("5-7折")])]),t._v(" "),s("div",{staticClass:"tab-item",class:{tabclickcolor:"zj-content"==t.isCurTab},attrs:{contClass:"zj-content"},on:{click:t.tabitemclick}},[s("span",[t._v("8折")])])]),t._v(" "),s("div",{staticClass:"tab-title",class:{tabfixed:t.isfixed},on:{click:t.changetabs}},[t.isarrow?s("div",{staticClass:"tab-section"},[s("ul",{staticClass:"tab-scroll",style:{transform:["translateX("+t.translateleft+"px)","-webkit-translateX("+t.translateleft+"px)"]},on:{touchstart:t.touchstartTabs,touchmove:t.touchmoveTabs}},t._l(t.tabData,(function(a,i){return s("li",{key:i,staticClass:"tab-title-item",class:{cur:t.iscur==i},attrs:{tabitem:i}},[t._v(t._s(a.title))])})),0)]):t._e(),t._v(" "),t.isarrow?t._e():s("div",{staticClass:"tab-more-section"},[s("div",{staticClass:"title",on:{click:function(a){return a.stopPropagation(),t.spreadTabs.apply(null,arguments)}}},[s("span",[t._v("精选分类")]),t._v(" X")]),t._v(" "),s("ul",{staticClass:"tab-more"},t._l(t.tabData,(function(a,i){return s("li",{key:i,staticClass:"tab-more-item",class:{curcur:t.iscur==i},attrs:{tabitem:i}},[t._v(t._s(a.title))])})),0)]),t._v(" "),t.isarrow?s("div",{staticClass:"tab-arrow",class:{arrow:!t.isarrow},on:{click:function(a){return a.stopPropagation(),t.spreadTabs.apply(null,arguments)}}}):t._e()])]),t._v(" "),s("div",{ref:"recom",staticClass:"recom-content"},[s("temprow",{attrs:{"goods-data":t.recomData,"license-status":t.licenseStatus||0}})],1)])}),i,!1,null,"2c5a0ef0",null).exports}}]);
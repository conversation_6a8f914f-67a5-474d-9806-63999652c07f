(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[98346],{28785:function(t,e,a){t.exports=a.p+"static/img/app_01.f72c36c.png"},94461:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return c}});var s=[function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"banner"},[e("img",{attrs:{src:a(28785),alt:""}})])}],i=(a(91058),a(89095)),o=a(59502),n={data:function(){return{iscur:0,licenseStatus:0,temprowData:[],tabData:[{hdid:"ZS201908021800364895",title:"热卖单品榜"},{hdid:"ZS201909171517016550",title:"销量飙升榜"}],isfixed:!1,chooseHdid:"ZS201908021800364895",tabIndex:0,scrollload:!0,isload:!1,loadingmsg:"正在加载···",pagecur:0,totalpage:0,skiptext:"销量飙升榜",showBtn:!1,imgNumber:0,imgSrc:[a(42845),a(31280)],cur_tab:"热卖单品榜"}},methods:{tabitemclick:function(t){document.querySelector("#explosionlist").scrollTop=document.querySelector(".checktab").offsetTop,this.translatetabs()},skipNexTab:function(){this.tabIndex++,this.tabIndex=this.tabIndex==this.tabData.length?0:this.tabIndex,this.translatetabs()},moveEvent:function(){var t=document.querySelector(".checktab").offsetTop,e=document.querySelector("#explosionlist").scrollTop;this.isfixed=e>=t,e>800&&o.Z.$emit("showBtn",{isShow:!0,dom:"#explosionlist"});var a=window.screen.height;document.querySelector("#explosionlist").scrollHeight-e<=a&&this.scrollload&&(this.scrollload=!1,this.pagecur>=this.totalpage-1?(this.isload=!1,this.showBtn=!0):(this.isload=!0,this.loadingmsg="正在加载···",this.pagecur++,this.getDatalist(this.chooseHdid,"temprowData")))},changetabs:function(t){this.tabIndex=parseInt(t.target.getAttribute("tabitem")),this.translatetabs(),this.imgNumber=this.tabIndex-0,this.cur_tab=t.target.innerText},translatetabs:function(){this.iscur=this.tabIndex,this.chooseHdid=this.tabData[this.tabIndex].hdid,document.querySelector("#explosionlist").scrollTop=document.querySelector(".checktab").offsetTop;var t=this.tabIndex+1;t=t==this.tabData.length?0:t,this.skiptext=this.tabData[t].title,this.pagecur=0,this.showBtn=!1,this.getDatalist(this.chooseHdid,"temprowData"),this.imgNumber=this.tabIndex-0},getDatalist:function(t,e){var a=this;this.putRequest("post","/app/layout/initExhibitionModulePage?sort=1",{merchantId:this.merchantId,limit:10,offset:this.pagecur,exhibitionId:t}).then((function(t){if("success"==t.data.status){t.data.data.rows.length<3&&(a.showBtn=!0),0==a.pagecur&&(a.totalpage=t.data.data.pageCount,a[e]=[]),a.isload=!1,a.scrollload=!0;var s=t.data.data.rows;a[e].push.apply(a[e],s),a.licenseStatus=t.data.data.licenseStatus}a.$nextTick((function(){o.Z.$emit("changeloading",!1)}))})).catch((function(t){}))}},mounted:function(){document.querySelector("#explosionlist").addEventListener("scroll",this.moveEvent),this.getDatalist("ZS201908021800364895","temprowData")},components:{temprow:i.Z},activated:function(){this.setAppTitle("爆款榜单")}},c=(0,a(51900).Z)(n,(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{attrs:{id:"explosionlist"}},[t._m(0),t._v(" "),a("div",{staticClass:"checktab"},[a("div",{staticClass:"tabs-box",class:{tabfixed:t.isfixed},on:{click:t.changetabs}},[a("ul",{staticClass:"tab-scroll"},t._l(t.tabData,(function(e,s){return a("li",{directives:[{name:"tracking",rawName:"v-tracking",value:{eventName:"h5_check_tab",params:{tabPage:"explosion_tab",tabName:e.title}},expression:"{eventName:'h5_check_tab',params:{tabPage: 'explosion_tab',tabName:item.title}}"}],key:s,staticClass:"tab-item",class:{cur:t.iscur==s},attrs:{tabitem:s}},[t._v("\n          "+t._s(e.title)+"\n          "),a("i")])})),0)])]),t._v(" "),a("div",{staticClass:"temprow-box"},[a("img",{attrs:{src:t.imgSrc[t.imgNumber],alt:""}}),t._v(" "),a("temprow",{attrs:{"goods-data":t.temprowData,from_tab:t.cur_tab,"license-status":t.licenseStatus||0}}),t._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:t.isload,expression:"isload"}],staticClass:"loaing"},[t._v(t._s(t.loadingmsg))])],1),t._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:t.showBtn,expression:"showBtn"}],staticClass:"highmargin-btn-box"},[a("div",{staticClass:"highmargin-btn",on:{click:t.skipNexTab}},[t._v("点击跳转至“"+t._s(t.skiptext)+"”")])])])}),s,!1,null,"3220f16d",null).exports}}]);
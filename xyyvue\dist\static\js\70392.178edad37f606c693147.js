(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[70392],{47868:function(t,e,i){t.exports=i.p+"static/img/rule_01_09.efa8375.gif"},5289:function(t,e,i){t.exports=i.p+"static/img/rule_02_09.f26a2e3.gif"},86168:function(t,e,i){t.exports=i.p+"static/img/rule_03_09.077d074.gif"},58463:function(t,e,i){t.exports=i.p+"static/img/rule_04_09.fd95185.gif"},42875:function(t,e,i){t.exports=i.p+"static/img/rule_05_09.5e7d6f3.jpg"},60644:function(t,e,i){"use strict";i.r(e),i.d(e,{default:function(){return n}});var r=[function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{attrs:{id:"highmarginrulenew"}},[r("img",{attrs:{src:i(47868),alt:""}}),t._v(" "),r("img",{attrs:{src:i(5289),alt:""}}),t._v(" "),r("img",{attrs:{src:i(86168),alt:""}}),t._v(" "),r("img",{attrs:{src:i(58463),alt:""}}),t._v(" "),r("img",{attrs:{src:i(42875),alt:""}})])}],s={activated:function(){this.setAppTitle("高毛规则")}},n=(0,i(51900).Z)(s,(function(){var t=this,e=t.$createElement;t._self._c;return t._m(0)}),r,!1,null,null,null).exports}}]);
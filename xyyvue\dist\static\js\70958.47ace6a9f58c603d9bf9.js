(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[70958],{66072:function(t,e,a){t.exports=a.p+"static/img/title_01.192c1fd.png"},43173:function(t,e,a){t.exports=a.p+"static/img/title_02.f672f8c.png"},8535:function(t,e,a){t.exports=a.p+"static/img/title_03.f02138e.png"},81170:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return c}});var s=[function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"banner"},[e("img",{attrs:{src:a(66072),alt:""}})])}],i=a(24388),o=a(59502),n={data:function(){return{text:"点击领取",isGet:!1,licenseStatus:0,temprowData:[],isload:!1,scrollload:!0,loadingmsg:"正在加载···",pagecur:0,totalpage:0}},methods:{getDatalist:function(t,e){var a=this;this.putRequest("post","/app/layout/initExhibitionModulePage?sort=1",{merchantId:this.merchantId,limit:10,offset:this.pagecur,exhibitionId:"ZS201910141032402991"}).then((function(t){if("success"==t.data.status){a.isload=!1;var e=t.data.data.rows;a.temprowData.push.apply(a.temprowData,e),a.totalpage=t.data.data.pageCount,a.scrollload=!0,a.licenseStatus=t.data.data.licenseStatus}a.$nextTick((function(){o.Z.$emit("changeloading",!1)}))})).catch((function(t){}))},changeVoucher:function(){this.getVoucher("11785")},getVoucher:function(t){var e=this;this.putRequest("post","/app/voucher/receiveVoucher",{merchantId:this.merchantId,voucherTemplateId:t}).then((function(t){"success"==t.data.status?e.$nextTick((function(){o.Z.$emit("changeprompt",{dialog:t.data.msg,showprompt:!0}),setTimeout((function(){window.location.reload()}),1500)})):o.Z.$emit("changeprompt",{dialog:t.data.errorMsg,showprompt:!0})})).catch((function(t){}))},getInitVoucher:function(t){var e=this;this.putRequest("post","app/voucher/getVoucherTemplateList",{merchantId:this.merchantId,templateIds:t}).then((function(t){"success"==t.data.status&&("0"!=t.data.data[0].isLq?(e.text="已领取",e.isGet=!0):(e.text="点击领取",e.isGet=!1));e.$nextTick((function(){o.Z.$emit("changeloading",!1)}))})).catch((function(t){}))},moveEvent:function(){var t=window.screen.height,e=document.querySelector("#seasonalmedicine").scrollTop,a=document.querySelector("#seasonalmedicine").scrollHeight;e>800?o.Z.$emit("showBtn",{isShow:!0,dom:"#seasonalmedicine"}):o.Z.$emit("showBtn",{isShow:!1,dom:""}),a-e-400<=t&&this.scrollload&&(this.scrollload=!1,this.pagecur>=this.totalpage-1?(this.isload=!0,this.loadingmsg="无更多数据"):(this.isload=!0,this.loadingmsg="正在加载···",this.pagecur++,this.getDatalist()))}},mounted:function(){document.querySelector("#seasonalmedicine"),this.getDatalist("ZS201910141032402991","temprowData"),this.getInitVoucher("11785")},activated:function(){this.setAppTitle("高毛热卖")},components:{temprow:i.Z}},c=(0,a(51900).Z)(n,(function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{attrs:{id:"seasonalmedicine"},on:{touchmove:t.moveEvent}},[t._m(0),t._v(" "),s("div",{staticClass:"voucher",on:{click:t.changeVoucher}},[s("img",{attrs:{src:a(43173),alt:""}}),t._v(" "),s("div",{staticClass:"stage",class:{getbg:t.isGet}},[t._v(" "+t._s(t.text))])]),t._v(" "),s("div",{staticClass:"goodsdata"},[s("img",{attrs:{src:a(8535),alt:""}}),t._v(" "),s("temprow",{attrs:{"goods-data":t.temprowData,"license-status":t.licenseStatus||0}}),t._v(" "),s("div",{directives:[{name:"show",rawName:"v-show",value:t.isload,expression:"isload"}],staticClass:"loaing"},[t._v(t._s(t.loadingmsg))])],1)])}),s,!1,null,"f30f4b98",null).exports}}]);
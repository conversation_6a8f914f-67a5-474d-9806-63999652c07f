"use strict";
function calcPageHeight(doc) {
  var cHeight = Math.max(
    doc.body.clientHeight,
    doc.documentElement.clientHeight
  );
  var sHeight = Math.max(
    doc.body.scrollHeight,
    doc.documentElement.scrollHeight
  );
  var height = Math.max(cHeight, sHeight);
  return height;
}
function _asyncToGenerator(fn) {
  return function() {
    var gen = fn.apply(this, arguments);
    return new Promise(function(resolve, reject) {
      function step(key, arg) {
        try {
          var info = gen[key](arg);
          var value = info.value;
        } catch (error) {
          reject(error);
          return;
        }
        if (info.done) {
          resolve(value);
        } else {
          return Promise.resolve(value).then(
            function(value) {
              step("next", value);
            },
            function(err) {
              step("throw", err);
            }
          );
        }
      }
      return step("next");
    });
  };
}

function crossAccess() {
  var paramsArr = location.search.substr(1).split("&");
  var params = {};
  for (var i = 0; i < paramsArr.length; i++) {
    params[paramsArr[i].split("=")[0]] = paramsArr[i].split("=")[1];
  }
  if (params.from) {
    console.log("init-cross");
    var origin = params.from;
    var height = calcPageHeight(document);
    console.log(232323232323, height);
    window.parent.postMessage({
      message: 'getCrossHeight',
      height,
    }, '*');
    // var url = origin + "/public/static/cross.html?height=" + height;
    // $("#crossFrame").attr("src", url);
  }
  if (params.title) {
    var arrs = params.title.split("-");
    var html = "";
    if (arrs.length > 1) {
      html = decodeURI(arrs[0]) + " <span> " + decodeURI(arrs[1]) + "</span>";
    } else {
      html = "<span>" + decodeURI(arrs[0]) + "</span>";
    }
    $(".truntable-title").html(html);
  } else {
    $(".truntable-title").html("<span>幸运大转盘</span>");
  }

  if (params.subTitle) {
    var arrs = params.subTitle.split("-");
    var html = "";
    for (var i = 0; i < arrs.length; i++) {
      html += decodeURI(arrs[i]) + " ";
    }
    $(".truntable-sub-title").html(html);
  } else {
    $(".truntable-sub-title").html("做任务抽奖 赢大额红包");
  }
}

localStorage.removeItem("merchantId");
localStorage.removeItem("branchCode");
lucky.getMerchantId(); //获取会员ID
api.getBranchCode(); //获取BranchCode
window._self_fn_turnPlate = {};
window._self_fn_turnPlate.isShareSuccess = function() {
  var _this = this;

  //分享成功后 调用分享次数接口
  api.addLuckCount().then(
    (function() {
      var _ref = _asyncToGenerator(
        /*#__PURE__*/ regeneratorRuntime.mark(function _callee(res) {
          var c;
          return regeneratorRuntime.wrap(
            function _callee$(_context) {
              while (1) {
                switch ((_context.prev = _context.next)) {
                  case 0:
                    if (!res.success) {
                      _context.next = 14;
                      break;
                    }

                    _context.next = 3;
                    return api.getLuckCount();

                  case 3:
                    c = _context.sent;

                    if (c.success) {
                      _context.next = 6;
                      break;
                    }

                    return _context.abrupt("return");

                  case 6:
                    lucky.countNum = c.data.lastCount;
                    if ($(".notice").length > 0) {
                      $(".notice").remove();
                    }

                    lucky.$pointer
                      .find("em")
                      .html("立即抽奖")
                      .removeClass("no-count")
                      .siblings("img")
                      .attr("src", "../static/img/point_start.png");
                    lucky.isClick = true;
                    $(".notice").hide();
                    $(".count-num").length !== 0
                      ? $(".count-num").html("\xD7" + lucky.countNum)
                      : lucky.$pointer.append(
                          "<span class='animated fadeIn count-num'>\xD7" +
                            lucky.countNum +
                            "</span>"
                        );
                    _context.next = 15;
                    break;

                  case 14:
                    // if ($('.countNum').length > 0) {
                    // 	$('.countNum').remove()
                    // }
                    // $('.notice').length !== 0
                    // 	? $('.notice').html('今日次数已用完,明天再来转好运')
                    // 	: lucky.$pointer.append(`<span class='animated fadeInUp notice'>今日次数已用完,明天再来转好运</span>`);
                    console.log(res.msg);

                  case 15:
                  case "end":
                    return _context.stop();
                }
              }
            },
            _callee,
            _this
          );
        })
      );

      return function(_x) {
        return _ref.apply(this, arguments);
      };
    })()
  );
};
window._self_fn_turnPlate.isTimeCalibration = _asyncToGenerator(
  /*#__PURE__*/ regeneratorRuntime.mark(function _callee2() {
    var a, _a$data, startTime, endTime;

    return regeneratorRuntime.wrap(
      function _callee2$(_context2) {
        while (1) {
          switch ((_context2.prev = _context2.next)) {
            case 0:
              _context2.next = 2;
              return api.getTurnplate();

            case 2:
              a = _context2.sent;

              if (!(!a.success || !a.data)) {
                _context2.next = 6;
                break;
              }

              console.log(a.msg);
              return _context2.abrupt("return");

            case 6:
              if (!(a.data.luckDrawPrizeList.length === 0)) {
                _context2.next = 9;
                break;
              }

              lucky.isClick = false;
              return _context2.abrupt("return");

            case 9:
              lucky.isClick = true;
              (_a$data = a.data),
                (startTime = _a$data.startTime),
                (endTime = _a$data.endTime);

              if (lucky.timer) clearInterval(lucky.timer);
              lucky.timeStamp(startTime, endTime);

            case 13:
            case "end":
              return _context2.stop();
          }
        }
      },
      _callee2,
      this
    );
  })
);

function bindClick() {
  lucky.$activityRule.click(function() {
    lucky.$pop_black_bg.show();
    lucky.$activityRule_pop.show();
    var top = $(document).scrollTop();
    $(document).on('scroll.unable',function (e) {
      $(document).scrollTop(top);
    })
  });
  lucky.$closeRulePop.click(function() {
    lucky.$pop_black_bg.hide();
    lucky.$activityRule_pop.hide();
    $(document).unbind("scroll.unable");
  });
  lucky.$getmore.click(function() {
    lucky.$pop_black_bg.show();
    lucky.$opportunity_pop.show();
    var top = $(document).scrollTop();
    $(document).on('scroll.unable',function (e) {
      $(document).scrollTop(top);
    })
  });
  lucky.$closeOppoPop.click(function() {
    lucky.$pop_black_bg.hide();
    lucky.$opportunity_pop.hide();
    $(document).unbind("scroll.unable");
  });
}

// 监听加购达到条件后，重新调用抽奖次数接口
window.addEventListener('message', async function(e){
  if (e.data.message === 'meetConditions') {
    if (e.data.value) {
      const c = await api.getLuckCount();
      if (!c.success) {
        return;
      }
      if (c.data.lastCount > 0) {
        lucky.countNum = c.data.lastCount;
        if ($(".notice").length > 0) {
          $(".notice").remove();
        }
        lucky.$pointer
          .find("em")
          .html("立即抽奖")
          .removeClass("no-count")
          .siblings("img")
          .attr("src", "../static/img/point_start.png");
        lucky.isClick = true;
        $(".notice").hide();
        $(".count-num").length !== 0
          ? $(".count-num").html("\xD7" + lucky.countNum)
          : lucky.$pointer.append(
              "<span class='animated fadeIn count-num'>\xD7" +
                lucky.countNum +
                "</span>"
            );
      }
    }
  }
}, false);

$(document).ready(
  _asyncToGenerator(
    /*#__PURE__*/ regeneratorRuntime.mark(function _callee4() {
      var a,
        c,
        _a$data2,
        startTime,
        endTime,
        showDrawCount,
        showDrawCountHeight,
        containerHeight,
        showDraw,
        l,
        mhtml,
        i,
        _l$i,
        mobile,
        titile;
      bindClick();
      return regeneratorRuntime.wrap(
        function _callee4$(_context4) {
          while (1) {
            switch ((_context4.prev = _context4.next)) {
              case 0:
                _context4.prev = 0;
                _context4.next = 3;
                return api.getTurnplate();

              case 3:
                a = _context4.sent;
                _context4.next = 6;
                return api.getLuckCount();

              case 6:
                c = _context4.sent;

                if (c.success) {
                  lucky.alreadyDrawCount = c.data.alreadyDrawCount;
                  lucky.countNum = c.data.lastCount;
                } else {
                  lucky.isClick = false;
                  lucky.hasEndActivity = false;
                  $(".notice").length !== 0
                    ? $(".notice").html("" + c.msg)
                    : lucky.$pointer.append(
                        "<span class='animated fadeInUp notice'>" +
                          c.msg +
                          "</span>"
                      );
                }

                if (!(!a.success || !a.data)) {
                  _context4.next = 11;
                  break;
                }

                console.log(a.msg);
                return _context4.abrupt("return");

              case 11:
                $("title").text(a.data.title);
                //分享内容
                lucky.shareContent = {
                  title: a.data.title,
                  url:
                    "https://android.myapp.com/myapp/detail.htm?apkName=com.ybmmarket20&ADTAG=mobile",
                  // url: window.location.href,
                  desc: a.data.friendShare,
                  desc_pyq: a.data.friendCircleShare
                };
                console.log(a);

                if (!(a.data.luckDrawPrizeList.length === 0)) {
                  _context4.next = 17;
                  break;
                } else {
                  crossAccess();
                }

                lucky.isClick = false;
                return _context4.abrupt("return");

              case 17:
                lucky.turnplate = a.data;
                lucky.init(crossAccess);
                (_a$data2 = a.data),
                  (startTime = _a$data2.startTime),
                  (endTime = _a$data2.endTime);

                lucky.timeStamp(startTime, endTime);

                //获取中奖信息
                showDrawCount = 3;
                showDrawCountHeight = showDrawCount * 0.21;
                containerHeight = showDrawCountHeight + 0.5;
                showDraw = a.data.showDraw;

                l = a.data.luckDrawRecordRespList;
                if (showDraw === 0) {
                  lucky.$mainBox.find(".inner.text-center").hide();
                  lucky.$mainBox.find(".inner.container").hide();
                } else {
                  if (l && l.length > 0) {
                    lucky.$mainBox.show();
                  }
                }
                // lucky.$mainBox
                //   .find(".container")
                //   .css("height", containerHeight + "rem");
                // lucky.$mainBox
                //   .find(".container")
                //   .find(".wraper")
                //   .css("height", showDrawCountHeight + "rem");
                if (l && l.length > 0) {
                  mhtml = "";

                  for (i = 0; i < l.length; i++) {
                    (_l$i = l[i]),
                      (mobile = _l$i.mobile),
                      (titile = _l$i.titile);

                    mhtml +=
                      "<li>\u606D\u559C &nbsp;" +
                      mobile +
                      " &nbsp;\u83B7\u5F97 &nbsp;" +
                      titile +
                      "</li>";
                  }
                  $(".list").html(mhtml);
                  //无缝滚动
                  lucky.Marquee("wraper", 20);
                } else {
                  $(".price-box").hide();
                  $(".btn_decoration").first().hide();
                }
                if (lucky.hasEndActivity) {
                  if (lucky.countNum === 0) {
                    lucky.isClick = false;
                    lucky.$pointer
                      .find("em")
                      .html("次数用完")
                      .addClass("no-count")
                      .siblings("img")
                      .attr("src", "../static/img/point_complete.png");
                    // $(".notice").length !== 0
                    //   ? ''
                    //   : lucky.$pointer.append(
                    //       "<span class='animated fadeInUp notice'>\u5206\u4EAB/\u4E0B\u5355\u6EE1\u989D\u53EF\u83B7\u5F97\u62BD\u5956\u673A\u4F1A</span>"
                    //     );
                  }
                  if (lucky.turnplate.drawMaxCount <= lucky.alreadyDrawCount) {
                    lucky.$pointer
                      .find("em")
                      .html("明天再来")
                      .addClass("no-count")
                      .siblings("img")
                      .attr("src", "../static/img/point_complete.png");
                    $(".notice").length !== 0
                      ? $(".notice").html("今日次数已用完,明天再来转好运")
                      : lucky.$pointer.append(
                          "<span class='animated fadeInUp notice'>\u4ECA\u65E5\u6B21\u6570\u5DF2\u7528\u5B8C,\u660E\u5929\u518D\u6765\u8F6C\u597D\u8FD0</span>"
                        );
                  }
                }
                //点击抽奖
                lucky.$pointer.click(
                  _asyncToGenerator(
                    /*#__PURE__*/ regeneratorRuntime.mark(function _callee3() {
                      var r, _c, location;
                      if (lucky.loading) {
                        return false;
                      }
                      lucky.loading = true;
                      return regeneratorRuntime.wrap(
                        function _callee3$(_context3) {
                          while (1) {
                            switch ((_context3.prev = _context3.next)) {
                              case 0:
                                if (lucky.isClick) {
                                  _context3.next = 2;
                                  break;
                                }

                                return _context3.abrupt("return");

                              case 2:
                                if (lucky.hasEndActivity) {
                                  _context3.next = 4;
                                  break;
                                }

                                return _context3.abrupt("return");

                              case 4:
                                if (!lucky.hasclick) {
                                  _context3.next = 28;
                                  break;
                                }

                                lucky.hasclick = false;
                                $(this)
                                  .find("em")
                                  .html("抽奖中");
                                _context3.next = 9;
                                return api.getLuck();

                              case 9:
                                r = _context3.sent;
                                _context3.next = 12;
                                return api.getLuckCount();

                              case 12:
                                _c = _context3.sent;

                                if (_c.success) {
                                  _context3.next = 15;
                                  break;
                                }

                                return _context3.abrupt("return");

                              case 15:
                                lucky.alreadyDrawCount =
                                  _c.data.alreadyDrawCount;
                                lucky.countNum = _c.data.lastCount;
                                lucky.hasclick = true;

                                if (!(r.code == 1001)) {
                                  _context3.next = 27;
                                  break;
                                }

                                lucky.isClick = false;
                                lucky.$pointer
                                  .find("em")
                                  .html("次数用完")
                                  .addClass("no-count")
                                  .siblings("img")
                                  .attr(
                                    "src",
                                    "../static/img/point_complete.png"
                                  );
                                if ($(".count-num").length > 0) {
                                  $(".count-num").remove();
                                }
                                // $(".notice").length !== 0
                                //   ? ''
                                //   : lucky.$pointer.append(
                                //       "<span class='animated fadeInUp notice'>\u5206\u4EAB/\u4E0B\u5355\u6EE1\u989D\u53EF\u83B7\u5F97\u62BD\u5956\u673A\u4F1A</span>"
                                //     );
                                if (
                                  lucky.turnplate.drawMaxCount <=
                                  lucky.alreadyDrawCount
                                ) {
                                  lucky.$pointer
                                    .find("em")
                                    .html("明天再来")
                                    .addClass("no-count")
                                    .siblings("img")
                                    .attr(
                                      "src",
                                      "../static/img/point_complete.png"
                                    );
                                  $(".notice").length !== 0
                                    ? $(".notice").html(
                                        "今日次数已用完,明天再来转好运"
                                      )
                                    : lucky.$pointer.append(
                                        "<span class='animated fadeInUp notice'>\u4ECA\u65E5\u6B21\u6570\u5DF2\u7528\u5B8C,\u660E\u5929\u518D\u6765\u8F6C\u597D\u8FD0</span>"
                                      );
                                }
                                return _context3.abrupt("return");

                              case 27:
                                if (
                                  lucky.turnplate.luckDrawPrizeList &&
                                  lucky.turnplate.luckDrawPrizeList.length > 0
                                ) {
                                  location = 1;

                                  lucky.turnplate.luckDrawPrizeList.forEach(
                                    function(item, index) {
                                      if (item.id == r.data.id) {
                                        location = index + 1;
                                      }
                                    }
                                  );
                                  //指针落在对应奖品区域的中心角度
                                  lucky.rotateFn(location, r.data);
                                }

                              case 28:
                              case "end":
                                return _context3.stop();
                            }
                          }
                        },
                        _callee3,
                        this
                      );
                    })
                  )
                );
                _context4.next = 37;
                break;

              case 34:
                _context4.prev = 34;
                _context4.t0 = _context4["catch"](0);

                // console.log(_context4.t0);

              case 37:
                lucky.$knowBtn.click(function() {
                  lucky.$cover.hide();
                  lucky.$pop.hide();
                });

              case 38:
              case "end":
                return _context4.stop();
            }
          }
        },
        _callee4,
        this,
        [[0, 34]]
      );
    })
  )()
);

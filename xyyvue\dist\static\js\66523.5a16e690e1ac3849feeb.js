(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[66523],{61255:function(t,s,e){"use strict";e(91058),e(82772),e(9653);var a=e(59502),i=e(67087);s.Z={props:["dataId","productNum","isSplit","medNum","isPack","bgcolor","btncolor"],data:function(){return{goodsId:"",issplit:"",productValue:"",mednum:"",ispack:!1}},watch:{dataId:function(t){this.goodsId=t,this.productValue=this.productNum?this.productNum:0,this.issplit=this.isSplit,this.mednum=this.medNum,this.ispack=this.isPack},isExtend:function(){a.Z.$emit("cart_isExtend",this.isExtend,this.goodsId)}},methods:{addProductCart:function(t){if(this.btn_hidden)this.postCartData(this.mednum);else{var s=t.target.getAttribute("edit-cate"),e=parseInt(t.currentTarget.children[1].value),i=parseInt(this.mednum);if("add"==s)e+=i;else{if("min"!=s)return;e=1==this.issplit?e-1:e-i}e=e>0?e:0,this.productValue=e,a.Z.$emit("listenToChildEvent",{isExtend:this.isExtend,dataId:this.goodsId,productValue:this.productValue}),this.postCartData(e)}},inputCart:function(t){var s=parseInt(t.target.value);s=s>=0?s:0,this.productValue=s,a.Z.$emit("listenToChildEvent",{isExtend:this.isExtend,dataId:this.goodsId,productValue:this.productValue}),this.postCartData(t.target.value)},androidclick:function(t){if(navigator.userAgent.indexOf("Android")>=0){t.target.setAttribute("readonly",!0),t.target.blur();var s=t.target.value;a.Z.$emit("showandorideditcomp",{id:this.goodsId,val:s,showandoridedit:!0,split:this.issplit,medpack:this.mednum,package:this.ispack})}},postCartData:function(t){var s=this;if(t>0){var e=1==this.ispack?{merchantId:this.merchantId,amount:t,packageId:this.goodsId}:{merchantId:this.merchantId,amount:t,skuId:this.goodsId};this.putRequest("post","/app/changeCart",e).then((function(e){if("success"===e.data.status){s.btn_hidden&&a.Z.$emit("changeprompt",{dialog:"已添加到购物车!",showprompt:!0}),Number(e.data.data.qty)&&(0,i.M0)("h5_page_CommodityDetails_o",{commodityId:s.goodsId,real:1}),e.data.data.qty!=t&&(s.productValue=e.data.data.qty),null!=e.data.dialog&&(20==e.data.dialog.style?a.Z.$emit("changesureDialog",{dialogmsg:e.data.dialog.msg,showsureDialog:!0}):a.Z.$emit("changeprompt",{dialog:e.data.dialog.msg,showprompt:!0})),e.errorMsg&&a.Z.$emit("changeprompt",{dialog:e.errorMsg,showprompt:!0});try{var o=1==s.ispack?{proid:s.goodsId,pronum:s.productValue,isAdd:1,type:1}:{proid:s.goodsId,pronum:s.productValue,isAdd:1};window.webkit.messageHandlers.addPlanNumber.postMessage(o)}catch(t){}try{1==s.ispack?window.hybrid.addPlanNumber(s.goodsId,s.productValue,1,1):window.hybrid.addPlanNumber(s.goodsId,s.productValue,1)}catch(t){}}else s.productValue=0,a.Z.$emit("listenToChildEvent",{isExtend:s.isExtend,dataId:s.goodsId,productValue:s.productValue}),e.data.errorMsg?a.Z.$emit("changeprompt",{dialog:e.data.errorMsg,showprompt:!0}):e.data.msg?a.Z.$emit("changesureDialog",{dialogmsg:e.data.msg,showsureDialog:!0}):a.Z.$emit("changeprompt",{dialog:e.data.dialog.msg,showprompt:!0})})).catch((function(t){}))}else{var o=1==this.ispack?{merchantId:this.merchantId,packageIds:this.goodsId}:{merchantId:this.merchantId,ids:this.goodsId};this.putRequest("post","/app/batchRemoveProductFromCart",o).then((function(t){if("success"==t.data.status){s.btn_hidden&&a.Z.$emit("changeprompt",{dialog:"已添从购物车删除!",showprompt:!0}),s.productValue=0;try{var e=1==s.ispack?{proid:s.goodsId,pronum:0,isAdd:1,type:1}:{proid:s.goodsId,pronum:0,isAdd:1};window.webkit.messageHandlers.addPlanNumber.postMessage(e)}catch(t){}try{1==s.ispack?window.hybrid.addPlanNumber(s.goodsId,0,1,1):window.hybrid.addPlanNumber(s.goodsId,0,1)}catch(t){}}}))}}},created:function(){this.goodsId=this.dataId,this.productValue=this.productNum?this.productNum:0,this.issplit=this.isSplit,this.mednum=this.medNum,this.ispack=this.isPack}}},87809:function(t,s,e){"use strict";e.d(s,{p:function(){return a}});e(74916),e(15306),e(24603),e(39714);function a(t,s){if(!t)return"";t="string"==typeof t?t.replace(/-/g,"/"):t,t=new Date(t),/(y+)/.test(s)&&(s=s.replace(RegExp.$1,(t.getFullYear()+"").substr(4-RegExp.$1.length)));var e={"M+":t.getMonth()+1,"d+":t.getDate(),"h+":t.getHours(),"m+":t.getMinutes(),"s+":t.getSeconds()};for(var a in e)if(new RegExp("(".concat(a,")")).test(s)){var o=e[a]+"";s=s.replace(RegExp.$1,1===RegExp.$1.length?o:i(o))}return s}function i(t){return("00"+t).substr(t.length)}},34553:function(t,s,e){"use strict";var a=e(82109),i=e(42092).findIndex,o=e(51223),n="findIndex",c=!0;n in[]&&Array(1).findIndex((function(){c=!1})),a({target:"Array",proto:!0,forced:c},{findIndex:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}}),o(n)},62119:function(t,s,e){t.exports=e.p+"static/img/icon_empty.fff9bd9.png"},71476:function(t,s,e){t.exports=e.p+"static/img/nodata.ddcad20.jpg"},52619:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAYAAACohjseAAAEc0lEQVRoQ+2ae8jeYxjHP1/n85mQHCY5JMQoh4ahDfGHlkxOf1FiMktJhiaH0NQoTSiapJDMH1NrM1MUTZNTEiuhkJYz46tr7ufteZ/9Ts/2u5/3efVc9fS+dV/3dV+f33267uu+xf9cNMx8tk8BFgLHAWuBOZLe68fnQkDbewHTgAP6MVaj+xewpqmDtqPtD4E9u+z+CkyX9E5Tv8YB2j4EeBI4G9iqqZE+9RZLuq6uju3QebxA7zvgVEmf19mI8jFA22cBLwN7NKm4hTozJS2rslEBGNU+A06T9H2dHxsBbe+ShsPBdRVaKp8vaUEN4H4JZLcSvbfTcP2tyk4H8F7gtpacb2JmtqTn6xRtXwy8BGxdohsjbpakf8psdQA/Bo6qa7Cl8lXpy//dxJ7t64HHKnQXSZpTB/g7sH2B0mKgkSMNnA07scQvkRQramOx/QBwa0WFeZIeLirv9KALC6Wh2Cdthx9LgNklkOH/ZZJe6C2fFIBpIdwOeB04swTyD+A8SW92l08awAQZW9hbwDElkD+m7eOTTvmkALS9M3BsCtlmApdUzMcvUyDwbegMJaDtvYEL0m8qcHh3UNJgdVou6dyhAkyx51XARdEDLYSKh0paN+E9aPtk4CbgUmDbBr3TVGWKpC8mDNB2nFbui0Whqcd96C2TFHN18HPQ9oHAg8DlfTjcVPXPtJVcIWn9QAHTZn0zcDcQwX0/Ehv5OuCj9Itz4lfAT+n3c/q7vjcuHcgQtb078AwQwXNT+QZYCrwKrJAUEH1LdkDbsX/FieCIBt79kg7cz0bcKqkwhGxgZ0wlK6DtGcCLQGzUVfIDsAh4VFL835pkA7R9ThpiO1R4uyGtpPdLinxL65IFMKU/XgN2qvA4smTXSFrTOlWXwdYB08a9omZYRgbhrn7PhZvzIVoFTOnG6JGy3E4ceq+V9NTmOFtWx/Y2QCTN9gFWSfq6o9saYNrnYlieX+JIZA3iUPpKy3CRlFoORFAeEitx5Hxie2kvkrF9O3BPBVykCt9oEy5slaQzYiXeX9KGVnrQ9gnAuxXZr6slxUbfutiOE/wZBYaPl7S2LcDVwOkl3j8iKUK0LGI7PuxJBcanxjXBFgPavjKFYUUAMTdmSGorM7dJG1kBbccE/zTGewFdBMJHSoqYMpvkBrwFeKjE+7mS4uorq+QGfK4kV/kBcGKsYlnp/ltF881B23dGRNIDESeAaZJi4ckuuQHjcjLylEd3kSyQND87WWogK2C0YXvXiFCAg4CVkiIOHZhkBxwYSUlDI8A0zIb6dqlqlIx6cNSDE72K1LQ/GqKjIToaohP7BUZzMM3BONYUvSa6ocV3Mrm6+g4gruR6ZVxOpuxMlcupQdjdNx7rdXIycSE5bxCtDqiNuJnamCftAB4GvA+UvewbkF+tNBNxdTyaXTkGmOZhPJOKFMRkl4WS5nYgel/8TgeeAKZMQspI2d8o6elu3zd5bGd7R+DCSBr1vJceVuZ4bBDZ7bh0iVTlOBmK14Q5v9y/kUFAVzaFdNwAAAAASUVORK5CYII="},47897:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAABYCAYAAAB1YOAJAAAEB0lEQVR4Xu3cMYsTQRQH8Dd7CYJ6aTz1E1hpZaFXiFxhcQqKYiOW4u0s0RUL/RSeKMaEndXOVhQPtLC0UE4sbO4ar7URLPS4IpDbJ3ts4DguyW5m9s0bmTQhJDP/N788ZsPuEgH+QSIgSFJ8CHhooibw0B6aSIAoxne0hyYSIIrxHe2hiQSIYnxHe2giAaIY39EemkiAKMZ3tIcmEiCKMdrRcRwfyOvudDp9ovpri4njuNXpdP6aCjAC3W63Dw8Gg+cAcLMo7FWr1bq3vLy8ZapQqnmiKLqRZdlTADgOAGtBENxKkuSrbr42NCIKKeVHALiwuxghxKfZ2dlLLmFLKduI2N2DutVsNs92u901HWxtaCnlZURc2a8Il7BHIO8sSwjxTil11Sp0GIb3AeDJqCJcwB6HXKxrLU3TU1ahoyg6k2XZ6rgiOGOXQM6XptI0jaxC5+FSykeI+MA17JLIG81mc77b7f62Dl1gP0PE2BXssshBECwkSfJTB3lnn9edYPd4KaUT2NTIxqFd6GwbyLVAc8a2hVwbNEdsm8i1QnPCto1cOzQHbA7IJNA2sbkgk0HbwOaETApNic0NmRyaApsjshXoOrG5IluDrgObM7JVaJPY3JGtQ5vAdgGZBbQOtivIbKCnwXYJmRV0FewgCN5ub2+PvE6ZzwUAG6ZO2hfzaT0ZPfGvVUkxuMzFgxI5rJDZdfQQUBObHTJb6LLbyD6dzRKZNfQU2GyR2UNXwGaN7AR0XmQYhq8B4PqIg+CfIAhOmrgloMRBduqPsPvVsXclUsrziPgBAA6NWqUQopckyV0hBE4tUfNA1tBlkIc+3LHZQldBdgGbJfQ0yNyx2UHrIHPGZgVdEnlTCPEDEU+PO35x27PZQJdFbjQai4PB4LsQ4j0iLriCzQK6CnKv1/tc/LY+6BK2dehpkIddHIahM9hWoXWQXcO2Bm0C2SVsK9AmkV3BJoeuA9kFbFLoOpG5Y5NBUyBzxiaBpkTmil07tA1kjti1QttE5oZdGzQHZE7YtUBzQuaCbRyaIzIHbKPQnJFtYxuDdgHZJrYRaJeQp8FWSt3RvRtBGzqKomOIuI6IR8YUs5lfGRmetNct2tT4CuezpVIq1ck1Ab2UZdm4IlgiV+zs1TRN561Ch2F4GwBejCiCNXIFbPvQcRwf7ff76wAwtwfbCeSS2Etpmr602tHFhdJzAPAGAI7mr4UQv2ZmZq5x25MnQeV7NgDkN1ReLD6LQojHSqmHk8ZOel97j97VEXNBEFxBRGw0Giu6/6o1qfA635dSLgLACQD4opT6ZiLLGLSJYv7nOTw00bfroT00kQBRjO9oD00kQBTjO9pDEwkQxfiO9tBEAkQxvqM9NJEAUcw/ity9hqd1FWcAAAAASUVORK5CYII="},45836:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFgAAABYCAYAAABxlTA0AAAC10lEQVR4Xu3asa4xQRwF8L/mkki0GonwAl6ARCORKFUSHkCl1fEMKChUXsUTCIVQiFJLgcaX2UQi936uvbt7dv6TnGlIxJnZ356MzdrE4/F4CAdMIEFgmK0XTGCsL4HBvgQmMFoAnM89mMBgAXA8G0xgsAA4ng0mMFgAHM8GExgsAI5ngwkMFgDHs8EEBguA49lgAoMFwPFsMIHBAuB4NpjAYAFwPBtMYLAAOJ4NJjBYABwfusGXy0VWq5W3zFKpJOl0GrxkXPxms5HT6STFYlHy+XwkE4UC3u/3Mp1O5Xw+e4vJZDLS7Xa9Bbo07ve7zGYzWa/X3rITiYTUajVpNpuhDyMwsGnuYDAQ8/o6UqmU9Ho9Z5AN7mQyke12+wOz0+lIuVwOhRwYeLlcymKx+O/kriD/hmsOrFAoSL/f1wdsVqQd+ROudWCz7w6Hwx9bhAvbhR9ccxztdlsqlYqdBptZd7udjMdjud1ubxehrcl+cavVqrRarVC43g9m2CfcXUKOGzcSYFeabAM3MmDtyLZwIwXWimwTN3Jgbci2cSHAWpA14MKAbSNrwYUC20LWhAsHjhtZG24swHEha8SNDRiNrBU3VmAUsmbc2IGjRtaOawU4KmQXcK0Bh0V2BdcqcFBkl3CtA/8VOZfLvf2D0mQ9R1Q3y18zg74PfcM96MSv3/Nz0z6ZTEo2m5Xj8fjrlJpwVTT4qeUH+dPJ1IarCtjvdvEOWSOuOuCgyFpxVQL/FVkzrlpgv8jacVUDP5FHo5GYa9/vwwVc9cBmgYfDQebzufdYqRlfX1/SaDSkXq9/uqhQ8bmK62A/Etfr1Xus1ACbV1eGM8CugH5fJ4HBZ47ABAYLgOPZYAKDBcDxbDCBwQLgeDaYwGABcDwbTGCwADieDSYwWAAczwYTGCwAjmeDCQwWAMezwQQGC4Dj2WACgwXA8WwwgcEC4Hg2mMBgAXA8GwwG/gcf+U4m6oQYJQAAAABJRU5ErkJggg=="},35904:function(t){t.exports="data:image/png;base64,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"},49994:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFgAAABYCAYAAABxlTA0AAACG0lEQVR4Xu3c0YkCQRCE4RF8FMzGDIxAYzEIY9EIzMBsFnwU9uCej+2uLvpY2N/nqrnZj2YUR243z/M8eLUJ7ABus/1dGOBeX4CbfQEGuFugeX3OYICbBZqXZ4IBbhZoXp4JBrhZoHl5JhjgZoHm5ZlggJsFmpdnggFuFmhe3p7g7/c7ns/neL/f4/P5NG/3f5Y/HA7jdDqNy+Uy9vu99Udt4MfjMV6vl7WJtZbP5/O4Xq/W9mzg2+02pmmyNrHW8vF4HPf73doewAt8qwDmiFgecHuCeZNrBrYOqA2U7QnegJH1iABbfHEZ4NjISgBs8cVlgGMjKwGwxReXAY6NrATAFl9cBjg2shIAW3xxGeDYyEoAbPHFZYBjIysBsMUXlwGOjayEDcwX7sv+NjBXRs3A3CoDXD5DuVUu0+WKq/jhCW9yzUdEbha2m7I/RWyXLvfkAOecyimAy3S5IsA5p3IK4DJdrghwzqmcArhMlysCnHMqpwAu0+WKAOecyimAy3S5IsA5p3IK4DJdrghwzqmcArhMlyvawHzhvgxtA3Or3AzMrTLAucPyjxS3ymW6XJFb5ZyTnFrVP+SQd7+xgv0pYmNe8uMCLJNpBYA1LzkNsEymFQDWvOQ0wDKZVgBY85LTAMtkWgFgzUtOAyyTaQWANS85DbBMphUA1rzkNMAymVYAWPOS0wDLZFoBYM1LTgMsk2kFgDUvOf0DmpnvCLQauh0AAAAASUVORK5CYII="},46957:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAC2CAYAAACxri/QAAAEOElEQVR4Xu1d227bMAyV2qS2B/RH+6976a/kocjF1kCWh6VVu1Iflm7AKRBkWRSbJg8vpkg6p+qvlJJTStPpdPr1/Pw8ns/nMefsr3mex5SSfB6WZZmWZZHPQyllKqXod/JeShnkd/JvWSdr5F1+Z9/rd7J+WZZjSuk15/wiJ1/9lVIOsii+LpeLEpBz1gPP8ywHlgM5MUaUnhzEyDo5KYiwk4NoXRcuQNb9/pKgt7e3cZqm4XK5KAHyMg7hap0gIc64JlfunLD1ziEjQLlXc26e52OTQxDZ09PTcLvdIoeEKD+oESGfXXw74olihchcInsECSvl5MqBDQ4pt4wgcEn/T4g28Qo3XXwb+PkkMsXmHoZOp9MooJZFhiEViZwkgHrFIQMpOKRiMbypiA0zymUAG7iCAjQx9PDwsNIygNowA5YrEdA8nAwii1iR30Nh5BimnQ7uFoaG8/k8DcMwXq/XCGpXe1NnB7KdxNdG8EKjQGCl/sq5FkFqh4ZhGK7Xq2parfaBUxCngzqqfLRHRtj3MCQ/MrWHBjS1LGII4oLNgs0x2wXj2a1lrobRUt9ut1EwFYEYjWNthwBocKc2A4IfiBxa2xIZ1F9FJgQdj0fRnJVhFFV/fHx0DRRuAKzRSNqFRFPg7gNupEVQrfbAkKo7MIUrlXcDtfuxqPaBg7Bjn0xAk6AK1M6F4CpwUKg9VFmvPoppS8u67ZAYxsPhIL4sGkb4MrXSxqGVuwie37kEDG052sDd9yjiK28PLYuWWn4UxQACYH3hOoCdCOw9DkV71HQdd4qHms71R+KhXpHdLR5SXLYiRsZDjIcYD4V7NMZD9c0i46EQUzMeGhkPvadw2rfSjIc6QljGQ1spPeaHmB9CoiqmZZgfEg4wPyTYYH4oJD2ZH0pxN4j5oVYGDVsA99ovY36o2lGMW5zNlB7jIcZDjIdCIYFvdzIeQnqG8RDjoXgvzXjovX6o6Vy5X4YcN/fLQoLhe7UfrB+y6r4Whlg/1FXZgOK4VpUe64dYPyRev66FZf1Qx+bLqtKT9UM/WU/N/BDzQ6ynDk0CrKfW+zLWU3fUMbKeunXXUTcJcL+M+2XR4TbDD+6XddghbwlkfxnuPhBTMz/E/FDod2V/2VYbzlYXMPvLwhan90nHpsq6ebLLlzE/xPzQdr8998u6+u3ZX1Z1AnO/zJtu2W8f+/DjDBD2l7G/rLqVRve4pGTYX8b+Mh9e0mpNZn9ZRwjL/jL2l3H+kIUdOrSE84dCwgCD2VhPzflDq1l6zA91OFfWD/0z8xibSc97z2Nkfoj5Ic5j5DzGveJczITlPMYtDlVjWDmP0ct1OH+I84c+hmPvzqdmPMR6atZTs566dq6sp+6oY2Q9Neup6wdBcD515/M6WD/0f9YPlVJ2HyDyI/OH9h6xsnoITXWj+Lee13HMOb+mlF7+AJOrG4x6Z1YKAAAAAElFTkSuQmCC"},79157:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFgAAABYCAYAAABxlTA0AAAC00lEQVR4Xu3asYoTURjF8e8mUynYuJs3EHsLsRCxXBvBd3BnBnRK+1haWCbkjlbWYiHoIyiChdU2voFgtSAEkvmWgQ3Igkkmc8/c78rZLmTzn8lvD5MliRP+QAUctM64EBg8AgITGCwAznPBBAYLgPNcMIHBAuA8F0xgsAA4zwUTGCwAznPBBAYLgPNcMIHBAuA8F0xgsAA4zwUTGCwAznPBBAYLgPPJLLiqqhvr9bqZTCZ/ptNpA3YJljcPXJblXVV9p6q3RUSdc0tVfem9f+Wc02ASoJBp4KIoHqjqZxG5fvX5O+fmi8XiuXVks8DbcDfYKSCbBN4HNxVkc8BdcFNANgV8CK51ZDPAfXAtI5sA3hP33Dn3U1XvbPuPytoLX3TgfXGzLDtZrVY/nHOfVPVhKshRgbvgzufzLy1qnufXUkKOBnwI7ma1KSFHAe6Dmxry4MAhcFNCHhQ4JG4qyIMBI3BTQB4EGIlrHRkOPASuZWQo8JC4VpFhwDFwLSJDgGPiWkMODmwB1xJyUGBLuFaQgwFbxLWAHATYMm5s5N7AKeDGRO4FnBLuIcje+2d9v49yMHBZlhNVPVPVm1tO4rz9JGLzZnnfkw31+A7vJxfe+7rPcfsAnzZNs+3gJnE7LvlbXdf3ogDnef5URN784+CmcTsgxwOuqup4uVyeicjRFeQkcPdEPq3r+m2UBV9+AHlfRD6IyHF72zn3azweP7F2zd0F1F6TReS9iDy6/N32W5yvvfcvdj121/0HX4P/WsDRaDR6rKqaZdnH2Wz2e9dBrd5fFMWJiNwSka/e++8hzrM3cIiT+J8bBAb/dQlMYLAAOM8FExgsAM5zwQQGC4DzXDCBwQLgPBdMYLAAOM8FExgsAM5zwQQGC4DzXDCBwQLgPBdMYLAAOM8FExgsAM5zwQQGC4DzXDCBwQLgPBdMYLAAOM8FExgsAM5zwQQGC4DzF61s/2gG7LupAAAAAElFTkSuQmCC"},2477:function(t,s,e){"use strict";e.d(s,{Z:function(){return r}});e(9653),e(56977),e(54678),e(74916),e(15306),e(54747),e(47042),e(69600);var a=e(55880),i=e(99194),o=e(59502),n=e(67087),c={props:{goodsData:Array,licenseStatus:Number,trackingStatus:{type:Boolean,default:!0}},data:function(){return{datalist:[],imgUrl:""}},filters:{fixedtwo:function(t){return parseFloat(t).toFixed(2)},replace:function(t){return t.replace(/-/g,".")}},components:{cartBtn:a.Z,priceBox:i.Z},methods:{getIdList:function(t){var s=[];t&&t.length>0?(t.forEach((function(t,e){s.push(t.id)})),this.getPrice(t,s)):this.datalist=t},splitData:function(t){for(var s=[],e=0,a=t.length;e<a;e+=20)s.push(t.slice(e,e+20));return s},getPrice:function(t,s){var e=this,a=this.splitData(s),i=0;a.forEach((function(s,n){e.putRequest("post","/app/marketing/discount/satisfactoryInHandPrice",{merchantId:e.merchantId,skuIds:s.join(",")}).then((function(s){if(e.$nextTick((function(){o.Z.$emit("changeloading",!1)})),"success"==s.data.status){var n=s.data.data;n&&n.length>0&&n.forEach((function(s,e){t.forEach((function(t,e){s.skuId==t.id&&(t.zheHouPrice=s.price)}))}))}++i===a.length&&t&&t.length>0&&(e.datalist=[],t.forEach((function(t,s){e.datalist.push(t)})))})).catch((function(s){e.datalist=t}))}))}},mounted:function(){var t=this.goodsData;this.getIdList(t),this.imgUrl=this.imgBaseUrl,(0,n.dA)("h5_page_ListPage_ExpStatic",{list:t},"",this.trackingStatus),o.Z.$on("cart_isExtend",(function(t,s){for(var e=document.querySelectorAll("i[data-original-id]"),a=0;a<e.length;a++)e[a].getAttribute("data-original-id")==s&&(e[a].style.display=t?"none":"inline-block");for(var i=document.querySelectorAll("div[data-pageage-id]"),o=0;o<i.length;o++)i[o].getAttribute("data-pageage-id")==s&&(t?(i[o].style.display="block",i[o].previousElementSibling.style.width="40%"):(i[o].previousElementSibling.style.width="80%",i[o].style.display="none"))}))},computed:{detailUrl:function(){return o.Z.detailBaseUrl}},watch:{goodsData:function(t,s){this.getIdList(t),(0,n.dA)("h5_page_ListPage_ExpStatic",{list:t},"",this.trackingStatus)}}},r=(0,e(51900).Z)(c,(function(){var t=this,s=t.$createElement,a=t._self._c||s;return a("div",{staticClass:"temprow-row"},t._l(t.datalist,(function(s,i){return a("div",{key:i,ref:"temp"+s.id,refInFor:!0,staticClass:"temprow-item"},[a("a",{attrs:{href:t.detailUrl+"product_id="+s.id}},[a("div",{staticClass:"img-box"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.imgUrl+"/ybm/product/min/"+s.imageUrl,expression:"imgUrl + '/ybm/product/min/' + item.imageUrl"}],attrs:{alt:""}}),t._v(" "),2==s.status?a("div",{staticClass:"sold-out posmiddle"},[t._v("售罄")]):t._e(),t._v(" "),s.markerUrl&&!s.reducePrice?a("img",{staticClass:"activity-token",attrs:{src:t.imgUrl+s.markerUrl,alt:""}}):t._e(),t._v(" "),s.markerUrl&&s.reducePrice&&(1!=s.isControl||1==s.isPurchase&&1==s.isControl)?a("div",{staticClass:"mark-text"},[a("img",{attrs:{src:t.imgUrl+s.markerUrl,alt:""}}),t._v(" "),a("h4",[t._v("药采节价："+t._s(t._f("fixedtwo")(s.reducePrice)))])]):t._e(),t._v(" "),s.promotionSkuImageUrl&&(1!=s.isControl||1==s.isPurchase&&1==s.isControl)?a("div",{staticClass:"promotionSkuPrice"},[a("img",{attrs:{src:t.imgUrl+s.promotionSkuImageUrl,alt:""}}),t._v(" "),a("span",[t._v("¥"+t._s(t._f("fixedtwo")(s.promotionSkuPrice)))])]):t._e(),t._v(" "),s.activityTag&&s.activityTag.tagNoteBackGroupUrl?a("div",{staticClass:"biaoqian"},[a("img",{attrs:{src:t.imgUrl+s.activityTag.tagNoteBackGroupUrl,alt:""}}),t._v(" "),a("div",{staticClass:"temtejia806"},[a("span",{staticClass:"discount"},[t._v("\n              "+t._s(s.activityTag.timeStr)+"\n            ")]),t._v(" "),t._l(s.activityTag.skuTagNotes,(function(s,e){return a("span",{key:e,staticClass:"price806",style:{color:"#"+s.textColor}},[t._v(t._s(s.text))])}))],2)]):t._e(),t._v(" "),s.activityTag&&s.activityTag.tagNoteBackGroupUrl&&2==+s.activityTag.sourceType?a("div",{staticClass:"shop-biaoqian"},[a("img",{attrs:{src:s.activityTag.tagNoteBackGroupUrl,alt:""}}),t._v(" "),s.activityTag.customTopNote?a("div",{staticClass:"customTopNote"},[t._v(t._s(s.activityTag.customTopNote))]):t._e(),t._v(" "),a("div",[a("span",{staticClass:"shop-discount"},[t._v(" "+t._s(s.activityTag.timeStr)+" ")]),t._v(" "),t._l(s.activityTag.skuTagNotes,(function(s,e){return a("span",{key:e,staticClass:"shop-text"},[t._v(t._s(s.text))])}))],2),t._v(" "),s.activityTag.customBottomNote?a("div",{staticClass:"customBottomNote"},[t._v(t._s(s.activityTag.customBottomNote))]):t._e()]):t._e()]),t._v(" "),a("div",{staticClass:"commonName"},[a("p",{staticClass:"textellipsis"},[s.activityTag&&s.activityTag.tagUrl?a("span",{staticClass:"bq-hgj"},[a("img",{attrs:{src:t.imgUrl+s.activityTag.tagUrl,alt:""}})]):t._e(),t._v(" "),1===s.agent?a("span",{staticClass:"dujia"},[t._v("独家")]):t._e(),t._v(" "),a("span",{staticClass:"name"},[t._v(t._s(s.commonName))]),t._v(" "),a("span",{staticClass:"spec"},[t._v("/")]),t._v(" "),a("span",{staticClass:"spec"},[t._v(t._s(s.spec))])])]),t._v(" "),a("div",{staticClass:"factory"},[a("div",{staticClass:"chang"}),t._v(" "),a("div",{staticClass:"name"},[t._v(t._s(s.manufacturer))])]),t._v(" "),a("div",{staticClass:"effect"},[a("div",{staticClass:"xiao"}),t._v(" "),s.nearEffect?a("div",{staticClass:"name"},[t._v("\n          "+t._s(t._f("replace")(s.nearEffect))+"/"+t._s(t._f("replace")(s.farEffect))+"\n        ")]):t._e(),t._v(" "),a("div",{staticClass:"pageage-info",attrs:{"data-pageage-id":s.id}},[t._v("\n          "+t._s(s.mediumPackageTitle)+"\n        ")])])]),t._v(" "),a("div",{staticClass:"price-add-wrap"},[a("div",{staticClass:"price-wrap"},[a("div",{staticClass:"price-container"},[1===t.licenseStatus||5===t.licenseStatus?a("div",{staticClass:"qualifications"},[t._v("\n            价格认证资质可见\n          ")]):!0!==s.isPurchase&&1===s.isControl?a("div",{staticClass:"nobuy"},[t._v("\n            暂无购买权限\n          ")]):a("div",{staticClass:"price-numer"},["true"==s.isOEM&&0==s.signStatus||0==s.showAgree?a("span",{staticClass:"price-permission"},[t._v("价格签署协议可见")]):2==s.priceType&&s.skuPriceRangeList?a("i",[t._v("\n              ￥"+t._s(t._f("fixedtwo")(s.skuPriceRangeList[0].price))+"~"+t._s(t._f("fixedtwo")(s.skuPriceRangeList[s.skuPriceRangeList.length-1].price))+"\n            ")]):a("div",{staticClass:"pricewapper"},[a("div",{staticClass:"price-box clearfixed"},[a("div",{staticClass:"price-two"},[a("p",{staticClass:"ellipsis-box"},[a("span",[t._v("￥"+t._s(t._f("fixedtwo")(s.fob)))]),t._v(" "),s.zheHouPrice?a("span",{staticClass:"zhekou"},[t._v(t._s(s.zheHouPrice))]):t._e()])])])])])]),t._v(" "),a("div",{staticClass:"control-hid"},[1!==t.licenseStatus&&5!==t.licenseStatus?a("div",{staticClass:"control-box"},["true"!=s.isOEM&&(1!=s.isControl||1==s.isPurchase&&1==s.isControl)||"true"==s.isOEM&&1==s.signStatus?a("priceBox",{attrs:{uniformPrice:s.uniformPrice,suggestPrice:s.suggestPrice,grossMargin:s.grossMargin}}):t._e()],1):t._e()])]),t._v(" "),a("div",{staticClass:"inputNumber"},[2===s.status?a("div",{staticClass:"no-goods"},[a("img",{attrs:{src:e(33292),alt:""}})]):a("div",["true"==s.isOEM&&0==s.signStatus||0==s.showAgree?a("div"):a("div",[!0===s.isPurchase||1!==s.isControl||1!==t.licenseStatus&&5!==t.licenseStatus?a("div",[1!=s.isControl||1==s.isPurchase&&1==s.isControl?a("cartBtn",{attrs:{"is-pack":!1,"data-id":s.id,"is-split":s.isSplit,"product-num":s.cartProductNum,"med-num":s.mediumPackageNum}}):t._e()],1):t._e()])])])]),t._v(" "),a("div",{staticClass:"label-box",class:{"label-box-hide":s.tagList.length<=0&&!(1==s.isUsableMedicalStr)}},[a("div",{staticClass:"labels"},t._l(s.tagList.slice(0,3),(function(s,e){return a("span",{key:e,class:"span"+s.uiType},[t._v(t._s(s.name))])})),0)]),t._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:0!==s.isThirdCompany,expression:"item.isThirdCompany !== 0"}],staticClass:"enter-shop"},[a("i",{staticClass:"icon"}),t._v(" "),a("span",[a("span",{staticClass:"companyName"},[t._v(t._s(s.companyName))])])])])})),0)}),[],!1,null,"15017132",null).exports},88264:function(t,s,e){"use strict";e.d(s,{Z:function(){return i}});var a={mixins:[e(61255).Z]},i=(0,e(51900).Z)(a,(function(){var t=this,s=t.$createElement,e=t._self._c||s;return e("div",{staticClass:"green-btn",attrs:{"data-id":t.goodsId,"is-slipt":t.issplit,"med-pack-num":t.mednum},on:{click:function(s){return s.preventDefault(),t.addProductCart.apply(null,arguments)}}},[e("span",{staticClass:"min",attrs:{"edit-cate":"min"}},[t._v("-")]),t._v(" "),e("input",{directives:[{name:"model",rawName:"v-model",value:t.productValue,expression:"productValue"}],staticClass:"input-val",class:"input-val"+t.goodsId,attrs:{"edit-cate":"edit",type:"tel"},domProps:{value:t.productValue},on:{change:t.inputCart,click:function(s){return s.preventDefault(),t.androidclick.apply(null,arguments)},input:function(s){s.target.composing||(t.productValue=s.target.value)}}}),t._v(" "),e("span",{staticClass:"add",attrs:{"edit-cate":"add"}},[t._v("+")])])}),[],!1,null,"5d221bb3",null).exports},33449:function(t,s,e){"use strict";e.r(s),e.d(s,{default:function(){return w}});e(40561),e(82772),e(74916),e(23123),e(57327),e(4723),e(15306),e(92222);var a=e(59502),i=e(70372),o=e(77626),n=e(87809),c={name:"shopHomepage",data:function(){return{goodsData:[],totalpage:0,isload:!1,licenseStatus:0,loadingmsg:"正在加载···",pagecur:1,shopCode:"",tabWidth:162}},computed:{detailUrl:function(){return a.Z.detailBaseUrl}},mounted:function(){var t=this,s=this.$route.query;this.shopCode=s.shopCode,this.goodsData=[],this.pagecur=1,this.getDataList(),this.$nextTick((function(){t._initMenu()}))},filters:{formatDate:function(t){var s=new Date(t);return(0,n.p)(s,"yyyy/MM/dd")}},methods:{turnActivity:function(t){if(t.actId)this.$emit("turnActivity",t.actId);else if(t.productId){var s=document.createElement("a");s.href="".concat(this.detailUrl,"product_id=").concat(t.productId),s.click()}else if(t.jumpurl){var e=document.createElement("a");e.href="ybmpage://commonh5activity?cache=0&url="+t.jumpurl,e.click()}},moveEvent:function(){var t=window.screen.height-document.querySelector(".store-title-wrap").clientHeight,s=document.querySelector(".shop-homepage").scrollTop,e=document.querySelector(".shop-homepage").scrollHeight;s>800?a.Z.$emit("showBtn",{isShow:!0,dom:"#shop"}):a.Z.$emit("showBtn",{isShow:!1,dom:""}),e-s<=t&&!this.isload&&(this.isload=!0,this.pagecur>=this.totalpage?this.loadingmsg="无更多数据":(this.loadingmsg="正在加载···",this.pagecur++,this.getDataList()));try{window.webkit.messageHandlers.hideKeyboard.postMessage({hide:"1"})}catch(t){}},getDataList:function(){var t=this;a.Z.$emit("changeloading",!0),this.putRequest("post","/shop/listProducts",{shopCode:this.shopCode,pageSize:20,pageNum:this.pagecur}).then((function(s){if("success"==s.data.status){var e=s.data.data.products;t.goodsData.push.apply(t.goodsData,e),t.totalpage=s.data.data.totalPage,t.licenseStatus=s.data.data.licenseStatus}t.isload=!1,a.Z.$emit("changeloading",!1)})).catch((function(s){t.isload=!1,a.Z.$emit("changeloading",!1)}))},_initMenu:function(){var t=this,s=this.tabWidth;this.categorys.length;this.$nextTick((function(){t.$refs.tab&&(t.scroll?t.scroll.refresh():t.scroll=new o.Z(t.$refs.tabsWrapper,{scrollX:!0,eventPassthrough:"vertical"}))}))},selectItem:function(t,s){this.$emit("allProducts",{index:t,categoryCode:s})}},components:{templist:i.Z},props:{shopImagesBanner:{type:Array,default:[]},shopImagesBar:{type:Array,default:[]},categorys:{type:Array,default:[]},couponList:{type:Array,default:[]},hasTree:{type:Boolean,default:!1}}},r=e(51900),l=(0,r.Z)(c,(function(){var t=this,s=t.$createElement,a=t._self._c||s;return a("div",{staticClass:"shop-homepage",on:{touchmove:function(s){return s.stopPropagation(),t.moveEvent.apply(null,arguments)}}},[t.shopImagesBanner.length?a("div",{staticClass:"position"},[t._m(0),t._v(" "),a("div",{staticClass:"banner-swiper"},[a("swipe",{staticClass:"swipe",attrs:{auto:3e3}},t._l(t.shopImagesBanner,(function(s,e){return a("swipe-item",{key:e},[a("div",{staticClass:"img-box",on:{click:function(e){return t.turnActivity(s)}}},[a("img",{staticClass:"item-img",attrs:{src:s.url,width:"100%"}})])])})),1)],1)]):t._e(),t._v(" "),t._l(t.shopImagesBar,(function(s,e){return a("div",{key:e,staticClass:"barChart",class:{shopBarTop:0===e&&!t.shopImagesBanner.length},on:{click:function(e){return t.turnActivity(s)}}},[a("img",{staticClass:"item-img",attrs:{src:s.url}})])})),t._v(" "),t.couponList.length?a("div",{staticClass:"coupon-wrap"},[a("ul",{staticClass:"ul-tab"},t._l(t.couponList,(function(s,e){return a("li",{key:e},[a("a",{staticClass:"img-box",attrs:{href:"ybmpage://couponavailableactivity?coupon_id="+s.id}},[a("div",{staticClass:"coupon-left"},[a("div",[a("p",{staticClass:"coupon-cen"},[a("span",[t._v("￥")]),t._v(t._s(s.moneyInVoucher)+"\n              ")]),t._v(" "),0==s.reduceType?[a("p",{staticClass:"coupon-bot"},[t._v("满"+t._s(s.minMoneyToEnable)+"元")]),t._v(" "),a("p",{staticClass:"coupon-bot"},[t._v("使用")])]:[a("p",{staticClass:"coupon-bot"},[t._v("每满"+t._s(s.minMoneyToEnable))]),t._v(" "),a("p",{staticClass:"coupon-bot"},[t._v("可用")]),t._v(" "),a("p",{staticClass:"coupon-bot"},[t._v("最高减"+t._s(s.discount))])]],2)]),t._v(" "),a("div",{staticClass:"coupon-right"},[a("p",{staticClass:"coupon-top"},[t._v(t._s(s.name))]),t._v(" "),a("p",{staticClass:"coupon-cen"},[a("span",{staticClass:"coupon-des"},[t._v(t._s(s.description))]),t._v(" "),a("span",{staticClass:"coupon-purchase"},[t._v("去抢购")])]),t._v(" "),a("p",{staticClass:"coupon-bot"},[t._v("\n              "+t._s(t._f("formatDate")(s.startTime))+"-"+t._s(t._f("formatDate")(s.endTime))+"\n            ")])])])])})),0)]):t._e(),t._v(" "),t.hasTree&&t.categorys.length&&t.categorys.some((function(t){return t.url}))?a("div",{ref:"tabsWrapper",staticClass:"tabs"},[a("ul",{ref:"tab",staticClass:"ul-tab"},t._l(t.categorys,(function(s,e){return a("li",{key:e,class:{"empty-img":!s.url},on:{click:function(a){return t.selectItem(e,s.categoryCode)}}},[a("img",{staticClass:"item-img",attrs:{src:s.url,width:"100%"}}),t._v("\n        "+t._s(s.categoryName)+"\n      ")])})),0)]):t._e(),t._v(" "),0!==t.totalpage?a("div",{staticClass:"shop"},[t.shopImagesBar.length||t.shopImagesBanner.length?a("p",{staticClass:"title"},[t._v("\n      所有品种\n    ")]):t._e(),t._v(" "),t.goodsData.length?a("div",[a("templist",{attrs:{"goods-data":t.goodsData,"license-status":t.licenseStatus||0}}),t._v(" "),t.isload?a("div",{staticClass:"loadingtips"},[t._v(t._s(t.loadingmsg))]):t._e()],1):a("div",{staticClass:"nodata"},[a("img",{attrs:{src:e(62119),alt:""}}),t._v(" "),a("p",[t._v("暂无商品")])])]):t._e()],2)}),[function(){var t=this.$createElement,s=this._self._c||t;return s("div",{staticClass:"topic-image"},[s("div",{staticClass:"bg_content1"})])}],!1,null,"399c74aa",null).exports,d=(e(21249),e(9653),e(2477)),u={name:"allProducts",data:function(){return{isDown:!0,isOpen:!0,isload:!1,totalpage:0,oneLevel:0,licenseStatus:0,twoLevel:-1,pagecur:1,shopCode:"",productsCode:"",goodsData:[],oneLevelList:[],twoLevelList:[]}},mounted:function(){var t=this,s=this.$route.query;this.shopCode=s.shopCode,this.oneLevel=this.turnProductsIndex,this.categorys.length&&(this.oneLevelList=this.categorys.map((function(t,s){return t})),this.twoLevelList=this.categorys[this.oneLevel]&&this.categorys[this.oneLevel].subCategorys),this.goodsData=[],this.pagecur=1,this.productsCode=this.categoryCode?this.categoryCode:this.categorys[this.oneLevel]&&this.categorys[this.oneLevel].categoryCode,this.queryProducts(this.productsCode),this.$nextTick((function(){t._initMenu(),t.twoLevelList&&t.twoLevelList.length&&t._initTwoMenu()}))},methods:{nextLevel:function(){var t=this.twoLevelList.length-1;this.twoLevelList&&this.twoLevelList.length&&(t>this.twoLevel?this.twoLevel+=1:this.twoLevel=0,this.productsCode=this.twoLevelList[this.twoLevel].categoryCode,this.twoLeveldownselectItem(this.twoLevel))},initHeight:function(){var t=document.querySelector(".store-title-wrap").clientHeight,s=document.querySelector(".level-one").clientHeight,e=document.querySelector(".level-two").clientHeight,a=window.screen.height-t-s-e;document.querySelector(".shop")&&(document.querySelector(".shop").style.height=a+"px")},moveEvent:function(){var t=document.querySelector(".store-title-wrap").clientHeight,s=document.querySelector(".level-one").clientHeight,e=document.querySelector(".level-two").clientHeight,i=window.screen.height-t-s-e,o=document.querySelector(".shop").scrollTop,n=document.querySelector(".shop").scrollHeight;o>800?a.Z.$emit("showBtn",{isShow:!0,dom:"#shop"}):a.Z.$emit("showBtn",{isShow:!1,dom:""}),n-o<=i&&!this.isload&&(this.isload=!0,this.pagecur<this.totalpage&&(this.pagecur++,this.queryProducts(this.productsCode)));try{window.webkit.messageHandlers.hideKeyboard.postMessage({hide:"1"})}catch(t){}},handleOneLevel:function(){this.isDown=!this.isDown,this.isOpen=!0},handleTwoLevel:function(){this.isOpen=!this.isOpen},queryProducts:function(t){var s=this;a.Z.$emit("changeloading",!0),this.putRequest("post","/shop/queryProducts",{shopCode:this.shopCode,pageSize:20,pageNum:this.pagecur,categoryCode:t}).then((function(t){if(s.isload=!1,"success"==t.data.status){var e=t.data.data;s.goodsData.push.apply(s.goodsData,e.products),s.totalpage=e.totalPage,s.licenseStatus=e.licenseStatus,e.pageNo===e.totalPage&&(s.isload=!0),s.$nextTick((function(){s.initHeight()}))}a.Z.$emit("changeloading",!1)})).catch((function(t){s.isload=!1,a.Z.$emit("changeloading",!1)}))},countWidth:function(t){var s=this.$refs.tab.offsetWidth,e=Math.floor(this.$refs.tabsWrapper.clientWidth),a=this.$refs.tab.children[t].offsetLeft,i=this.$refs.tab.children[t].offsetWidth;a+i/2<=e/2?this.scroll.scrollTo(0,0):s-a-i<=e/2||a+i>s-20?this.scroll.scrollTo(Number("-".concat(s-e)),0):this.scroll.scrollTo(Number("-".concat(a-e/2+i/2)),0)},downselectItem:function(t){var s=this;this.isDown=!0,this.oneLevel=t,this.goodsData=[],this.pagecur=1,this.twoLevelList=this.categorys[this.oneLevel]&&this.categorys[this.oneLevel].subCategorys,this.categorys[this.oneLevel]&&(this.productsCode=this.categorys[this.oneLevel].categoryCode,this.queryProducts(this.productsCode)),this.twoLevelList&&this.twoLevelList.length&&this._initTwoMenu(),this.$nextTick((function(){s.countWidth(t),s.twoscroll.scrollTo(0,0),s.twoLevel=-1}))},twoLeveldownselectItem:function(t){var s=this;this.isOpen=!0,this.twoLevel=t,this.goodsData=[],this.pagecur=1,this.twoLevelList[this.twoLevel]&&(this.productsCode=this.twoLevelList[this.twoLevel].categoryCode,this.queryProducts(this.productsCode)),this.$nextTick((function(){var e=s.$refs.twoTab.offsetWidth,a=Math.floor(s.$refs.twoTabsWrapper.clientWidth),i=s.$refs.twoTab.children[t].offsetLeft,o=s.$refs.twoTab.children[t].offsetWidth;i+o/2<=a/2?s.twoscroll.scrollTo(0,0):e-i-o<=a/2||i+o>e-20?s.twoscroll.scrollTo(Number("-".concat(e-a)),0):s.twoscroll.scrollTo(Number("-".concat(i-a/2+o/2)),0)}))},selectItem:function(t,s){var e=this;this.oneLevel=t,this.goodsData=[],this.pagecur=1,this.twoLevelList=this.categorys[this.oneLevel]&&this.categorys[this.oneLevel].subCategorys,this.categorys[this.oneLevel]&&(this.productsCode=this.categorys[this.oneLevel].categoryCode,this.queryProducts(this.productsCode)),this.twoLevelList&&this.twoLevelList.length&&this._initTwoMenu(),this.$nextTick((function(){var t=Math.floor(e.$refs.tabsWrapper.clientWidth/2),a=s.target.offsetLeft,i=e.$refs.tab.offsetWidth,o=s.target.offsetWidth;a+o/2<=t?e.scroll.scrollTo(0,0):i-a-o<=t||a+o>i-20?e.scroll.scrollTo(Number("-".concat(i-2*t)),0):e.scroll.scrollTo(Number("-".concat(a-t+o/2)),0),e.twoscroll&&(e.twoscroll.scrollTo(0,0),e.twoLevel=-1)}))},twoLevelSelectItem:function(t,s){this.twoLevel=t,this.goodsData=[],this.pagecur=1;var e=Math.floor(this.$refs.twoTabsWrapper.clientWidth/2),a=s.target.offsetLeft,i=this.$refs.twoTab.offsetWidth,o=s.target.offsetWidth;this.twoLevelList[this.twoLevel]&&(this.productsCode=this.twoLevelList[this.twoLevel].categoryCode,this.queryProducts(this.productsCode)),a+o/2<=e?this.twoscroll.scrollTo(0,0):i-a-o/2<=e||a+o>i-20?this.twoscroll.scrollTo(Number("-".concat(i-2*e)),0):this.twoscroll.scrollTo(Number("-".concat(a-e+o/2)),0)},_initMenu:function(){var t=this;this.$nextTick((function(){var s=t.$refs.tab.children[t.oneLevelList.length-1];s&&(t.$refs.tab.style.width="".concat(s.offsetLeft+s.offsetWidth,"px")),t.scroll?t.scroll.refresh():t.scroll=new o.Z(t.$refs.tabsWrapper,{startX:0,scrollX:!0,scrollY:!1,click:!0,eventPassthrough:"vertical"}),t.countWidth(t.oneLevel)}))},_initTwoMenu:function(){var t=this;this.$nextTick((function(){var s=t.$refs.twoTab.children[t.twoLevelList.length-1];s&&(t.$refs.twoTab.style.width="".concat(s.offsetLeft+s.offsetWidth,"px")),t.twoscroll?t.twoscroll.refresh():t.twoscroll=new o.Z(t.$refs.twoTabsWrapper,{startX:0,scrollX:!0,scrollY:!1,click:!0,eventPassthrough:"vertical"})}))}},components:{templist:d.Z},props:{turnProductsIndex:{type:Number,default:0},categoryCode:{type:String,default:""},categorys:{type:Array,default:[]},isfixed:{type:Boolean,default:!1}}},h=(0,r.Z)(u,(function(){var t=this,s=t.$createElement,a=t._self._c||s;return a("div",{staticClass:"all-products"},[a("div",{staticClass:"level-one",class:{oneTabfixed:t.isfixed}},[a("div",{directives:[{name:"show",rawName:"v-show",value:t.oneLevelList&&t.oneLevelList.length>0,expression:"oneLevelList&&oneLevelList.length>0"}],staticClass:"one-level"},[a("div",{ref:"tabsWrapper",staticClass:"tabs"},[a("ul",{directives:[{name:"show",rawName:"v-show",value:t.isDown&&t.oneLevelList&&t.oneLevelList.length>0,expression:"isDown&&oneLevelList&&oneLevelList.length>0"}],ref:"tab"},t._l(t.oneLevelList,(function(s,e){return a("li",{key:e,class:{oneLevelAactive:e==t.oneLevel},on:{click:function(s){return t.selectItem(e,s)}}},[t._v(t._s(s.categoryName))])})),0)]),t._v(" "),a("div",{staticClass:"switch-wrap",on:{click:t.handleOneLevel}},[a("img",{staticClass:"shadow",attrs:{src:e(46957),alt:""}}),t._v(" "),t.isDown?a("img",{staticClass:"switch-item",attrs:{src:e(45836),alt:""}}):a("img",{staticClass:"switch-item",attrs:{src:e(79157),alt:""}})])])]),t._v(" "),a("div",{staticClass:"level-two",class:{twoTabfixed:t.isfixed}},[a("div",{directives:[{name:"show",rawName:"v-show",value:t.twoLevelList&&t.twoLevelList.length||!t.isDown&&t.oneLevelList&&t.oneLevelList.length,expression:"(twoLevelList && twoLevelList.length) || (!isDown && oneLevelList && oneLevelList.length)"}],staticClass:"two-level"},[a("ul",{directives:[{name:"show",rawName:"v-show",value:!t.isDown,expression:"!isDown"}],staticClass:"one-level-list"},t._l(t.oneLevelList,(function(s,e){return a("li",{key:e,class:{LevelAactive:e==t.oneLevel},on:{click:function(s){return t.downselectItem(e)}}},[a("span",[t._v(t._s(s.categoryName))])])})),0),t._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:t.isDown,expression:"isDown"}],staticClass:"two-tabswrap"},[a("div",{ref:"twoTabsWrapper",staticClass:"two-tabs"},[a("ul",{directives:[{name:"show",rawName:"v-show",value:t.isOpen,expression:"isOpen"}],ref:"twoTab"},t._l(t.twoLevelList,(function(s,e){return a("li",{key:e,class:{oneLevelAactive:e==t.twoLevel},on:{click:function(s){return t.twoLevelSelectItem(e,s)}}},[t._v(t._s(s.categoryName))])})),0)]),t._v(" "),a("div",{staticClass:"switch-wrap",on:{click:t.handleTwoLevel}},[t.isOpen?a("img",{staticClass:"switch-item",attrs:{src:e(49994),alt:""}}):a("img",{staticClass:"switch-item",attrs:{src:e(47897),alt:""}})])]),t._v(" "),a("ul",{directives:[{name:"show",rawName:"v-show",value:t.isDown&&!t.isOpen,expression:"isDown && !isOpen"}],staticClass:"two-level-list"},t._l(t.twoLevelList,(function(s,e){return a("li",{key:e,class:{LevelAactive:e==t.twoLevel},on:{click:function(s){return t.twoLeveldownselectItem(e)}}},[a("span",[t._v(t._s(s.categoryName))])])})),0)])]),t._v(" "),t.isDown&&t.isOpen?t._e():a("div",{staticClass:"shadow-dialog"}),t._v(" "),0!==t.totalpage?a("div",{staticClass:"shop",on:{touchmove:function(s){return s.stopPropagation(),t.moveEvent.apply(null,arguments)}}},[t.goodsData.length?a("div",[a("templist",{attrs:{"goods-data":t.goodsData,"license-status":t.licenseStatus||0}}),t._v(" "),t.isload?a("div",{staticClass:"loadingtips"},[t._v("\n        已经到底了\n        "),t.twoLevelList&&t.twoLevelList.length?a("span",{on:{click:t.nextLevel}},[t._v("前往查看更多商品")]):t._e()]):t._e()],1):t._e()]):a("div",{staticClass:"nodata"},[a("img",{attrs:{src:e(62119),alt:""}}),t._v(" "),a("p",[t._v("该分类暂无商品")])])])}),[],!1,null,"2fcca62f",null).exports,p=(e(91058),e(34553),{name:"shopHomepage",data:function(){return{currentActivity:0,surplusDay:0,surplusHour:0,surplusMin:0,surplusSecond:0,licenseStatus:0,pagecur:1,shopCode:"",isload:!1,totalpage:0,acttagList:[],goodsData:[],currentdata:[],loadOnce:!1}},filters:{formatDate:function(t){var s=new Date(t);return(0,n.p)(s,"yyyyMMdd hh:mm:ss")}},created:function(){var t=this.$route.query;this.shopCode=t.shopCode,this.goodsData=[],this.pagecur=1,this.getDataList()},mounted:function(){var t=this;this.$nextTick((function(){t._initMenu()}))},methods:{timeStamp:function(t){var s=this,e=setInterval((function(){var a=(new Date).getTime(),i=(t-a)/1e3;i<=0&&(clearInterval(e),s.getDataList());var o=parseInt(i)%60;o=o>=10?o:"0"+o;var n=parseInt(i/60)%60;n=n>=10?n:"0"+n;var c=parseInt(parseInt(i/60)/60)%24;c=c>=10?c:"0"+c;var r=parseInt(parseInt(parseInt(i/60)/60)/24);s.surplusDay=r,s.surplusHour=c,s.surplusMin=n,s.surplusSecond=o}),1e3)},moveEvent:function(){var t=window.screen.height-document.querySelector(".store-title-wrap").clientHeight,s=document.querySelector(".activity").scrollTop,e=document.querySelector(".activity").scrollHeight;s>800?a.Z.$emit("showBtn",{isShow:!0,dom:"#shop"}):a.Z.$emit("showBtn",{isShow:!1,dom:""}),e-s<=t&&!this.isload&&(this.isload=!0,this.pagecur>=this.totalpage?this.loadingmsg="无更多数据":(this.loadingmsg="正在加载···",this.pagecur++,this.getDataList()));try{window.webkit.messageHandlers.hideKeyboard.postMessage({hide:"1"})}catch(t){}},getDataList:function(){var t=this;a.Z.$emit("changeloading",!0);var s={shopCode:this.shopCode,actId:this.actId};this.putRequest("post","/app/acttag/list",s).then((function(s){if("success"==s.data.status){var e=s.data.data,i=e[t.currentActivity];if(t.acttagList=e,a.Z.$emit("changeloading",!1),t.actId&&!t.loadOnce){var o=e.findIndex((function(s){return s.actId==t.actId}));o<0?(a.Z.$emit("changesureDialog",{dialogmsg:"抱歉当前活动已结束",showsureDialog:!0}),i=e[0],t.currentActivity=0):(t.currentActivity=o,i=e[o]),t.loadOnce=!0}i&&(t.listProducts(i.actId),0===i.status&&t.timeStamp(i.startTime))}})).catch((function(t){a.Z.$emit("changeloading",!1)}))},listProducts:function(t){var s=this;a.Z.$emit("changeloading",!0),this.putRequest("post","/app/acttag/listProducts",{actId:t,shopCode:this.shopCode,pageSize:20,pageNum:this.pagecur}).then((function(t){if("success"==t.data.status){var e=t.data.data&&t.data.data.products;e&&e.length&&(s.goodsData.push.apply(s.goodsData,e),s.totalpage=t.data.data.totalPage,s.licenseStatus=t.data.data.licenseStatus)}s.isload=!1,a.Z.$emit("changeloading",!1)})).catch((function(t){s.isload=!1,a.Z.$emit("changeloading",!1)}))},selectItem:function(t,s){this.currentActivity=t;var e=Math.floor(this.$refs.tabsWrapper.clientWidth/2),a=s.target.offsetLeft,i=this.$refs.tab.offsetWidth,o=s.target.offsetWidth;this.goodsData=[],this.listProducts(this.acttagList[this.currentActivity].actId),a+o/2<=e?this.scroll.scrollTo(0,0):i-a-o<=e/2||a+o>i-20?this.scroll.scrollTo(Number("-".concat(i-2*e)),0):this.scroll.scrollTo(Number("-".concat(a-e+o/2)),0)},_initMenu:function(){var t=this;this.$refs.tab.style.width="".concat(this.$refs.tab.scrollWidth,"px"),this.$nextTick((function(){t.scroll?t.scroll.refresh():t.scroll=new o.Z(t.$refs.tabsWrapper,{startX:0,scrollX:!0,scrollY:!1,click:!1,eventPassthrough:"vertical"})}))}},components:{templist:i.Z},props:{isfixed:{type:Boolean,default:!1},actId:{type:String,default:""}}}),g=(0,r.Z)(p,(function(){var t=this,s=t.$createElement,a=t._self._c||s;return a("div",{staticClass:"activity",on:{touchmove:function(s){return s.stopPropagation(),t.moveEvent.apply(null,arguments)}}},[a("div",{class:{tabfixed:t.isfixed}},[a("div",{ref:"tabsWrapper",staticClass:"tabs"},[a("ul",{ref:"tab"},t._l(t.acttagList,(function(s,e){return a("li",{key:e,class:{oneLevelAactive:e==t.currentActivity},on:{click:function(s){return t.selectItem(e,s)}}},[a("p",{staticClass:"content"},[t._v(t._s(s.actTitle))]),t._v(" "),a("p",{staticClass:"status"},[t._v(t._s(0===s.status?"未开始":2===s.status?"已过期":"进行中"))])])})),0)]),t._v(" "),t.acttagList[t.currentActivity]?a("div",{staticClass:"explain-content"},[t.acttagList[t.currentActivity]&&0===t.acttagList[t.currentActivity].status?a("p",[t._v("\n        距离活动开始还剩"+t._s(t.surplusDay)+"天\n        "),a("span",[t._v(t._s(t.surplusHour))]),t._v(":\n        "),a("span",[t._v(t._s(t.surplusMin))]),t._v(":\n        "),a("span",[t._v(t._s(t.surplusSecond))])]):a("p",[t._v("活动时间："+t._s(t._f("formatDate")(t.acttagList[t.currentActivity]&&t.acttagList[t.currentActivity].startTime))+"至"+t._s(t._f("formatDate")(t.acttagList[t.currentActivity]&&t.acttagList[t.currentActivity].endTime)))]),t._v(" "),a("p",[t._v("活动规则："+t._s(t.acttagList[t.currentActivity]&&t.acttagList[t.currentActivity].ruleStr))])]):t._e()]),t._v(" "),0!==t.totalpage?a("div",{staticClass:"shop"},[t.goodsData.length?a("div",[a("templist",{attrs:{"goods-data":t.goodsData,"license-status":t.licenseStatus||0}}),t._v(" "),t.isload?a("div",{staticClass:"loadingtips"},[t._v(t._s(t.loadingmsg))]):t._e()],1):a("div",{staticClass:"nodata"},[a("img",{attrs:{src:e(62119),alt:""}}),t._v(" "),a("p",[t._v("暂无商品")])])]):t._e()])}),[],!1,null,"5d48f62e",null).exports,v=(e(56977),e(54678),e(54747),e(88264)),m=e(67087),f={props:["goodsData","licenseStatus"],data:function(){return{packList:[],imgUrl:"",tagList:[]}},computed:{detailUrl:function(){return a.Z.detailBaseUrl}},filters:{fixedtwo:function(t){return parseFloat(t).toFixed(2)}},watch:{goodsData:function(t){this.packList=this.goodsData;var s=[];this.packList.forEach((function(t){t&&t.skuList&&(s=s.concat(t.skuList))})),(0,m.dA)("h5_page_ListPage_ExpStatic",{list:s},"sku")}},methods:{isStartSale:function(t){return(new Date).getTime()<(null==t?0:new Date(t.replace(/-/g,"/")).getTime())},listScroll:function(t,s){var e=document.querySelector(".pack-list-".concat(t.id)).scrollLeft,a=document.querySelector(".inner-".concat(t.id)),i=document.querySelector(".innerbox-".concat(t.id)),o=document.querySelector(".pack-list").offsetWidth+20,n=document.querySelector(".pack-item").offsetWidth+10,c=o/(n*t.skuList.length);i.style.width=c*a.offsetWidth+"px";var r=e/(n*t.skuList.length);i.style.marginLeft=r*a.offsetWidth+"px",i.style.background=s%2==0?"#9900ec":"#ec7500",a.style.background=s%2==0?"#F3DCFF":"#ffeddc"}},mounted:function(){this.imgUrl=this.imgBaseUrl},components:{greenBtn:v.Z}},A={name:"packagePage",data:function(){return{clinicData:[],licenseStatus:0,isnodata:!1,shopCode:""}},mounted:function(){this.getDataList()},components:{packagecomp:(0,r.Z)(f,(function(){var t=this,s=t.$createElement,e=t._self._c||s;return e("div",{staticClass:"packagecomp"},t._l(t.packList,(function(s,a){return e("div",{key:s.id,staticClass:"packageclinic-item",class:{purpleBorder:a%2==0,orangeBorder:a%2==1}},[e("div",{staticClass:"price-box",class:{purpleTitle:a%2==0,orangeTitle:a%2==1}},[1===t.licenseStatus||5===t.licenseStatus?e("div",[t._v("价格认证资质可见")]):e("div",{staticClass:"price-title"},[10!=s.status?e("div",{staticClass:"total-price"},[e("span",[t._v("套餐价:")]),t._v(" "),e("i",[t._v("¥"+t._s(t._f("fixedtwo")(s.totalPrice)))])]):t._e(),t._v(" "),e("div",{staticClass:"skulist-length"},[t._v("\n          (共"+t._s(s.skuList.length)+"种)\n        ")]),t._v(" "),10!=s.status?e("div",{staticClass:"original-price"},[e("span",[t._v("原价:")]),t._v(" "),e("i",[t._v("¥"+t._s(t._f("fixedtwo")(s.discountPrice+s.totalPrice)))])]):t._e(),t._v(" "),10==s.status?e("div",[t._v("价格签署协议可见")]):t._e()])]),t._v(" "),0==s.status||10==s.status||t.isStartSale(s.startSaleTime)?t._e():e("div",{staticClass:"sold-out"},[t._v("售罄")]),t._v(" "),0!=s.status||t.isStartSale(s.startSaleTime)||1===t.licenseStatus||5===t.licenseStatus?t._e():e("greenBtn",{staticClass:"greenBtn",attrs:{"data-id":s.id,"is-pack":!0,"is-split":1,"product-num":s.packageCartCount,"med-num":1}}),t._v(" "),e("div",{staticClass:"pack-box"},[e("div",{staticClass:"pack-list",class:"pack-list-"+s.id,style:{justifyContent:s.skuList.length>2?"flex-start":"center"},on:{scroll:function(e){return t.listScroll(s,a)}}},t._l(s.skuList,(function(s){return e("a",{key:s.skuId,staticClass:"pack-item",class:"pack-item"+s.sku.id,attrs:{href:t.detailUrl+"product_id="+s.sku.id}},[e("div",{staticClass:"img"},[e("img",{attrs:{src:t.imgUrl+"/ybm/product/min/"+s.sku.imageUrl,alt:""}}),t._v(" "),s.sku.markerUrl&&!s.sku.reducePrice?e("img",{staticClass:"activity-token",attrs:{src:t.imgUrl+s.sku.markerUrl,alt:""}}):t._e(),t._v(" "),s.sku.markerUrl&&s.sku.reducePrice&&(1!=s.sku.isControl||1==s.sku.isPurchase&&1==s.sku.isControl)?e("div",{staticClass:"mark-text"},[e("img",{attrs:{src:t.imgUrl+s.sku.markerUrl,alt:""}}),t._v(" "),e("h4",[t._v("药采节价："+t._s(t._f("fixedtwo")(s.sku.reducePrice)))])]):t._e()]),t._v(" "),e("h2",{staticClass:"textellipsis"},[t._v(t._s(s.sku.commonName))]),t._v(" "),e("h3",{staticClass:"textellipsis"},[t._v(t._s(s.sku.spec))]),t._v(" "),1===t.licenseStatus||5===t.licenseStatus?e("b",[t._v("价格认证资质可见")]):e("div",[10!=s.sku.status?e("div",{staticClass:"sku-price"},[e("div",{staticClass:"fob"},[t._v("¥"+t._s(t._f("fixedtwo")(s.sku.fob)))]),t._v(" "),e("div",{staticClass:"productNumber"},[t._v("x"+t._s(s.productNumber))])]):t._e(),t._v(" "),10==s.sku.status?e("b",[t._v("价格签署协议可见")]):t._e()]),t._v(" "),e("div",{staticClass:"label-box"},[s.sku.tagList?e("div",{staticClass:"labels"},t._l(s.sku.tagList,(function(s,a){return"临期"==s.name||"近效期"==s.name?e("span",{key:a,class:"span"+s.uiType},[t._v("\n                "+t._s(s.name)+"\n              ")]):t._e()})),0):t._e()])])})),0),t._v(" "),e("div",{staticClass:"inner",class:"inner-"+s.id},[e("div",{staticClass:"innerbox",class:"innerbox-"+s.id})])])],1)})),0)}),[],!1,null,"d9c9fd82",null).exports},props:{isfixed:{type:Boolean,default:!1}},created:function(){var t=this.$route.query;this.shopCode=t.shopCode},methods:{getDataList:function(){var t=this;a.Z.$emit("changeloading",!0),this.putRequest("post","/shop/queryActivityPackage",{merchantId:this.merchantId,shopCode:this.shopCode}).then((function(s){if("success"==s.data.status)if(s.data.data.packageList&&s.data.data.packageList.length>0){var e=s.data.data.packageList;t.clinicData.push.apply(t.clinicData,e),t.licenseStatus=s.data.data.licenseStatus}else t.isnodata=!0;t.$nextTick((function(){a.Z.$emit("changeloading",!1)}))})).catch((function(){a.Z.$emit("changeloading",!1)}))},moveEvent:function(){try{window.webkit.messageHandlers.hideKeyboard.postMessage({hide:"1"})}catch(t){}},refreshNowPage:function(){this.clinicData=[],this.getDataList()}}},C={data:function(){return{isVisible:!1,initial:!1,hasTree:!1,isfixed:!1,shopInfo:{},categorys:[],shopImagesBanner:[],shopImagesBar:[],couponList:[],tabsname:[{title:"首页",titleId:0},{title:"全部商品",titleId:1}],tabIndex:0,shopCode:"",turnProductsIndex:0,trunActivityIndex:0,categoryCode:"",actId:"",actType:"",types:2,defaultImg:e(35904)}},methods:{moveEvent:function(){var t=document.querySelector(".fixwrap").offsetTop,s=document.querySelector("#shop").scrollTop;document.querySelector("#shop").scrollHeight;this.isfixed=s>=t},turnProducts:function(t){this.tabIndex=1,this.turnProductsIndex=t.index,this.categoryCode=t.categoryCode},turnActivity:function(t){this.tabsname.length>2&&(this.tabIndex=2,this.actId=t)},tabitemclick:function(t){this.tabIndex=t,this.actId="",this.categoryCode=""},err_load:function(t){t.path[0].src=this.defaultImg},listCouponBasicTemplateByMerchantIdAndShopCode:function(){var t=this;this.putRequest("post","/app/shopPromotion/listCouponBasicTemplateByMerchantIdAndShopCode",{shopCode:this.shopCode,merchantId:this.merchantId}).then((function(s){if("success"==s.data.status){var e=s.data.data;t.couponList=e.list}}))},getDataList:function(){var t=this;this.putRequest("post","/app/acttag/list",{shopCode:this.shopCode}).then((function(s){if("success"==s.data.status){var e=s.data.data;e&&e.length&&(t.tabsname.splice(2,0,{title:"活动",titleId:2}),(t.actId||"0"==t.actId)&&t.turnActivity(t.actId))}}))},getPackages:function(){var t=this;this.putRequest("post","/shop/queryActivityPackage",{merchantId:this.merchantId,shopCode:this.shopCode}).then((function(s){"success"==s.data.status&&s.data.data.packageList&&s.data.data.packageList.length>0&&(t.tabsname.splice(3,0,{title:"套餐",titleId:3}),"10"==t.actType&&(t.tabIndex=3))}))},queryCategoryTree:function(){var t=this;this.putRequest("post","/shop/queryCategoryTree",{shopCode:this.shopCode}).then((function(s){if("success"==s.data.status){var e=s.data.data,a=e.isVisible,i=e.categorys;t.hasTree=a,t.categorys=i}})).catch((function(t){}))},getUrlParams:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",s=arguments.length>1?arguments[1]:void 0;if(1==t.indexOf("?"))return!1;for(var e,a=t.split("&"),i=s||"",o=0;o<a.length;o++){var n=a[o].split("="),c={};c[n[0]]=decodeURI(n[1]),a[o]=c}if(i)for(o=0;o<a.length;o++)for(var r in a[o])r==i&&(e=a[o][r]);else e=a;return e},getInfo:function(){var t=this;this.initial=!1,a.Z.$emit("changeloading",!0),this.putRequest("post","/shop/getInfo",{shopCode:this.shopCode}).then((function(s){if("success"==s.data.status){var e=s.data.data,i=e.isVisible,o=e.shopInfo,n=e.shopImages;(0,m.M0)("h5_page_ActivePage",{pageName:o.showName}),t.shopImagesBanner=n.filter((function(s){return s.actId=t.getUrlParams(s.jumpurl,"actId"),s.productId=t.getUrlParams(s.jumpurl,"productId"),1===s.imageType})),t.shopImagesBar=n.filter((function(s){return s.actId=t.getUrlParams(s.jumpurl,"actId"),s.productId=t.getUrlParams(s.jumpurl,"productId"),2===s.imageType})),t.isVisible=i,t.shopInfo=o}a.Z.$emit("changeloading",!1),t.initial=!0})).catch((function(s){t.initial=!0,a.Z.$emit("changeloading",!1)}))},share:function(){var t;t=7===switchUrl?"https://new-app.test.ybm100.com":8===switchUrl?"https://new-app.stage.ybm100.com":"https://app-v4.ybm100.com";var s=!!navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/),e=this.shopInfo.appLink;s||(this.shopInfo.appLink=this.shopInfo.appLink.replace(/#/g,"{{Hashtag}}"));var a="".concat(t,"/static/xyyvue/dist/#/browserOpen?shopurl=").concat(this.shopInfo.appLink);s?this.appVersion&&+this.appVersion>=792&&(a="".concat(t,"/static/xyyvue/dist/#/launchYbmApp?jumpurl=").concat(encodeURIComponent(e))):this.appVersion&&+this.appVersion>=7902&&(a="".concat(t,"/static/xyyvue/dist/#/launchYbmApp?jumpurl=").concat(encodeURIComponent(e))),(0,m.M0)("shop_share_mobile_click",{pageName:"自然人店铺分享",pageUrl:e,shopCode:this.shopCode}),this.share_link(this.shopInfo.showName,a,"刚刚在药帮忙看到一个不错的店铺，分享给你","刚刚在药帮忙看到一个不错的店铺，分享给你","3",this.shopInfo.appLogoUrl)}},mounted:function(){document.querySelector("#shop").addEventListener("scroll",this.moveEvent)},activated:function(){var t=this.$route.query;this.shopCode=t.shopCode,this.actId=t.actId,this.actType=t.actType,this.getInfo(),this.queryCategoryTree(),this.getDataList(),this.getPackages(),this.listCouponBasicTemplateByMerchantIdAndShopCode(),this.setControlTitle(2,"#090712","#090712")},deactivated:function(){a.Z.$emit("showBtn",{isShow:!1,dom:""})},components:{homePage:l,allProducts:h,activity:g,packagePage:(0,r.Z)(A,(function(){var t=this,s=t.$createElement,a=t._self._c||s;return a("div",{staticClass:"packagesuper",on:{touchmove:t.moveEvent}},[a("div",[t.isnodata?a("div",{staticClass:"nodata"},[a("img",{attrs:{src:e(71476),alt:""}}),t._v(" "),a("p",[t._v("暂无数据")])]):a("div",[a("packagecomp",{attrs:{"goods-data":t.clinicData,"pack-type":1,"license-status":t.licenseStatus||0},on:{refreshPage:t.refreshNowPage}})],1)])])}),[],!1,null,"4805e07a",null).exports}},w=(0,r.Z)(C,(function(){var t=this,s=t.$createElement,a=t._self._c||s;return a("div",{attrs:{id:"shop"}},[t.isVisible?a("div",{staticClass:"shop-content"},[a("div",{staticClass:"store-title-wrap"},[a("div",{staticClass:"store-title-container"},[a("div",{staticClass:"store-icon"},[t.shopInfo.appLogoUrl?a("img",{attrs:{src:t.shopInfo.appLogoUrl,alt:""},on:{error:function(s){return t.err_load(s)}}}):t._e()]),t._v(" "),a("div",{staticClass:"store-tag-title"},[a("div",{staticClass:"store-title"},[t._v(t._s(t.shopInfo.showName))]),t._v(" "),a("div",{staticClass:"store-tag"},t._l(t.shopInfo.shopTags&&t.shopInfo.shopTags.split(","),(function(s,e){return a("span",{key:e,staticClass:"tag tag-item"},[t._v(t._s(s))])})),0)]),t._v(" "),a("div",{staticClass:"store-share",on:{click:t.share}},[a("img",{attrs:{src:e(52619),alt:""}}),t._v(" "),a("span",[t._v("分享")])])]),t._v(" "),a("div",{staticClass:"fixwrap"},[a("div",{staticClass:"store-title-tabs",class:{tabfixed:t.isfixed}},t._l(t.tabsname,(function(s){return a("div",{key:s.titleId,staticClass:"tab-item",class:{tabAactive:s.titleId==t.tabIndex},on:{click:function(e){return t.tabitemclick(s.titleId)}}},[t._v("\n            "+t._s(s.title)+"\n          ")])})),0)])]),t._v(" "),0==t.tabIndex?a("home-page",{attrs:{"shop-images-banner":t.shopImagesBanner,"shop-images-bar":t.shopImagesBar,categorys:t.categorys,couponList:t.couponList,"has-tree":t.hasTree},on:{allProducts:t.turnProducts,turnActivity:t.turnActivity}}):t._e(),t._v(" "),1==t.tabIndex?a("all-products",{attrs:{categorys:t.categorys,"has-tree":t.hasTree,"turn-products-index":t.turnProductsIndex,categoryCode:t.categoryCode,isfixed:t.isfixed}}):t._e(),t._v(" "),2==t.tabIndex?a("activity",{attrs:{isfixed:t.isfixed,"act-id":t.actId}}):t._e(),t._v(" "),3==t.tabIndex?a("package-page",{attrs:{isfixed:t.isfixed}}):t._e()],1):t._e(),t._v(" "),!t.isVisible&&t.initial?a("div",{staticClass:"nodata"},[a("img",{attrs:{src:e(62119),alt:""}}),t._v(" "),a("p",[t._v("控销店铺暂不支持自由浏览")])]):t._e()])}),[],!1,null,"3456d4e9",null).exports}}]);
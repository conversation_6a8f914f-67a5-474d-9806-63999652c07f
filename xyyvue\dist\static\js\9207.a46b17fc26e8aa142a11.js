(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[9207],{71476:function(t,a,s){t.exports=s.p+"static/img/nodata.ddcad20.jpg"},16807:function(t,a,s){t.exports=s.p+"static/img/protocolban.1fdd02f.jpg"},67127:function(t,a,s){"use strict";s.r(a),s.d(a,{default:function(){return o}});var e=[function(){var t=this.$createElement,a=this._self._c||t;return a("div",{staticClass:"banner"},[a("img",{attrs:{src:s(16807),alt:""}})])}],i=(s(92222),s(59502)),n={data:function(){return{isnodata:!1,licenseStatus:0,plantId:"",pageCur:1,tabData:[],translateleft:0,isShowMoreTab:!1,goodsData:[],totalpage:0,loadingmsg:"正在加载···",isload:!1,scrollload:!0}},methods:{changeTabs:function(t){var a=t.target.getAttribute("platid");if(this.isShowMoreTab=!1,null!=a){if(this.plantId=a,"menu-list"==t.currentTarget.className)this.translateleft=t.target.offsetLeft;else{var s=document.querySelector('[platid="'+a+'"]');this.translateleft=s.offsetLeft}this.goodsData=[],this.pageCur=1,this.isload=!1,i.Z.$emit("changeloading",!0),this.getInitData()}},showMoreTab:function(){this.isShowMoreTab=!this.isShowMoreTab},moveEvent:function(t){var a=window.screen.height,s=document.querySelector("#protocol").scrollTop;document.querySelector(".protocol").scrollHeight-s-400<=a&&this.scrollload&&(this.scrollload=!1,this.pageCur>=this.totalpage?(this.isload=!0,this.loadingmsg="无更多数据"):(this.isload=!0,this.loadingmsg="正在加载···",this.pageCur++,this.getInitData()));try{window.webkit.messageHandlers.hideKeyboard.postMessage({hide:"1"})}catch(t){}},getInitData:function(){var t=this;this.putRequest("post","/app/agreement/getAgreementSkuList",{merchantId:this.merchantId,platformId:this.plantId,limit:10,offset:this.pageCur}).then((function(a){if("success"==a.data.status){if(""==t.plantId&&(t.tabData=a.data.agreementList),a.data.data.rows.length<=0&&1==t.pageCur&&""==t.plantId)return t.isnodata=!0,void i.Z.$emit("changeloading",!1);t.isload=!1,t.scrollload=!0,t.totalpage=a.data.data.pageCount,t.licenseStatus=a.data.data.licenseStatus,t.goodsData=t.goodsData.concat(a.data.data.rows)}t.$nextTick((function(){i.Z.$emit("changeloading",!1)}))})).catch((function(t){}))}},created:function(){this.getInitData(),i.Z.$emit("changeloading",!0)},activated:function(){document.title="协议专区"},components:{temprow:s(24388).Z}},o=(0,s(51900).Z)(n,(function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{attrs:{id:"protocol"},on:{touchmove:t.moveEvent}},[t.isnodata?t._e():e("div",{staticClass:"protocol"},[e("div",{staticClass:"menubox"},[e("div",{staticClass:"menu-list",style:{transform:["translateX(-"+t.translateleft+"px)","-webkit-translateX(-"+t.translateleft+"px)"]},on:{click:t.changeTabs}},[t.tabData.length>1?e("span",{class:{activated:""==t.plantId},attrs:{platid:""}},[t._v("全部商品协议")]):t._e(),t._v(" "),t._l(t.tabData,(function(a,s){return e("span",{class:{activated:t.plantId==a.id},attrs:{platid:a.id}},[t._v(t._s(a.name))])}))],2),t._v(" "),t.tabData.length>1?e("div",{staticClass:"token",on:{click:t.showMoreTab}},[e("span"),e("span"),e("span")]):t._e()]),t._v(" "),t.tabData.length>1?e("div",{directives:[{name:"show",rawName:"v-show",value:t.isShowMoreTab,expression:"isShowMoreTab"}],staticClass:"allmenubox",on:{click:t.changeTabs}},[e("div",{staticClass:"allmenu"},[e("span",{class:{activated:""==t.plantId},attrs:{platid:""}},[t._v("全部商品协议")]),t._v(" "),t._l(t.tabData,(function(a,s){return e("span",{class:{activated:t.plantId==a.id},attrs:{platid:a.id}},[t._v(t._s(a.name))])}))],2)]):t._e(),t._v(" "),t._m(0),t._v(" "),e("temprow",{attrs:{"goods-data":t.goodsData,"license-status":t.licenseStatus}}),t._v(" "),e("div",{directives:[{name:"show",rawName:"v-show",value:t.isload,expression:"isload"}],staticClass:"loading"},[t._v(t._s(t.loadingmsg))])],1),t._v(" "),t.isnodata?e("div",{staticClass:"nodata"},[e("img",{attrs:{src:s(16807),alt:""}}),t._v(" "),e("img",{staticClass:"nodataimg",attrs:{src:s(71476),alt:""}}),t._v(" "),e("p",{staticClass:"tips"},[t._v("协议专区里面空空，去我的>协议管理中签署协议获取返利商品吧")]),t._v(" "),e("router-link",{staticClass:"sign",attrs:{to:"/protocollist?issign=0"}},[t._v("去签署协议>")])],1):t._e()])}),e,!1,null,"9c3abc6c",null).exports}}]);
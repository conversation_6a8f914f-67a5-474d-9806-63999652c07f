(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[63193],{1548:function(t,a,e){t.exports=e.p+"static/img/app_02.c217294.png"},63634:function(t,a,e){t.exports=e.p+"static/img/app_03.0470aa8.png"},34287:function(t,a,e){t.exports=e.p+"static/img/app_04.47872d4.png"},12212:function(t,a,e){t.exports=e.p+"static/img/app_05.9157777.png"},40510:function(t,a,e){t.exports=e.p+"static/img/app_07.974bd3a.png"},90982:function(t,a,e){t.exports=e.p+"static/img/banner_02.63ca8db.png"},58801:function(t,a,e){"use strict";e.r(a),e.d(a,{default:function(){return r}});var s=[function(){var t=this.$createElement,a=this._self._c||t;return a("div",{staticClass:"banner"},[a("img",{attrs:{src:e(90982),alt:""}})])}],i=e(4942),n=(e(91058),e(24388)),o=e(59502),c={data:function(){var t;return t={iscur:0,licenseStatus:0,temprowData:[],isload:!1,loadingmsg:"正在加载···",tabData:[{hdid:"ZS201909031558573434",title:"配方饮片"},{hdid:"ZS201908121526129610",title:"健康花茶"},{hdid:"ZS201908121526323985",title:"精致饮片"},{hdid:"ZS201908121526503871",title:"药食养生"},{hdid:"ZS201908121527056610",title:"参茸贵细"}],isfixed:!1,chooseHdid:"ZS201909031558573434",tabIndex:0,scrollload:!0},(0,i.Z)(t,"isload",!1),(0,i.Z)(t,"loadingmsg","正在加载···"),(0,i.Z)(t,"pagecur",0),(0,i.Z)(t,"totalpage",0),(0,i.Z)(t,"skiptext","健康花茶"),(0,i.Z)(t,"showBtn",!1),(0,i.Z)(t,"isShowto",!0),(0,i.Z)(t,"imgNumber",0),(0,i.Z)(t,"imgSrc",[e(40510),e(1548),e(63634),e(34287),e(12212)]),t},methods:{tabitemclick:function(t){document.querySelector("#zyzonefjpage").scrollTop=document.querySelector(".checktab").offsetTop,this.translatetabs()},skipNexTab:function(){this.tabIndex++,this.tabIndex=this.tabIndex==this.tabData.length?0:this.tabIndex,this.translatetabs()},moveEvent:function(){var t=document.querySelector(".checktab").offsetTop,a=document.querySelector("#zyzonefjpage").scrollTop;this.isfixed=a>=t,a>800&&o.Z.$emit("showBtn",{isShow:!0,dom:"#zyzonefjpage"});var e=window.screen.height;document.querySelector("#zyzonefjpage").scrollHeight-a<=e&&this.scrollload&&(this.scrollload=!1,this.pagecur>=this.totalpage-1?(this.isload=!1,this.showBtn=!0):(this.isload=!0,this.loadingmsg="正在加载···",this.pagecur++,this.getDatalist(this.chooseHdid,"temprowData")))},changetabs:function(t){this.tabIndex=parseInt(t.target.getAttribute("tabitem")),this.translatetabs(),this.imgNumber=this.tabIndex-0},translatetabs:function(){this.iscur=this.tabIndex,this.chooseHdid=this.tabData[this.tabIndex].hdid,document.querySelector("#zyzonefjpage").scrollTop=document.querySelector(".checktab").offsetTop;var t=this.tabIndex+1;t=t==this.tabData.length?0:t,this.skiptext=this.tabData[t].title,this.pagecur=0,this.showBtn=!1,this.getDatalist(this.chooseHdid,"temprowData"),this.imgNumber=this.tabIndex-0},getDatalist:function(t,a){var e=this;this.putRequest("post","/app/layout/initExhibitionModulePage?sort=1",{merchantId:this.merchantId,limit:10,offset:this.pagecur,exhibitionId:t}).then((function(t){if("success"==t.data.status){0==e.pagecur&&(e.totalpage=t.data.data.pageCount,e[a]=[]),e.isload=!1,e.scrollload=!0;var s=t.data.data.rows;e[a].push.apply(e[a],s),e.licenseStatus=t.data.data.licenseStatus}e.$nextTick((function(){o.Z.$emit("changeloading",!1)}))})).catch((function(t){}))}},mounted:function(){document.querySelector("#zyzonefjpage").addEventListener("scroll",this.moveEvent),this.getDatalist("ZS201909031558573434","temprowData")},components:{temprow:n.Z},activated:function(){this.setAppTitle("中药专区")}},r=(0,e(51900).Z)(c,(function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{attrs:{id:"zyzonefjpage"}},[t._m(0),t._v(" "),e("div",{staticClass:"checktab"},[e("div",{staticClass:"tabs-box",class:{tabfixed:t.isfixed},on:{click:t.changetabs}},[e("ul",{staticClass:"tab-scroll"},t._l(t.tabData,(function(a,s){return e("li",{key:s,staticClass:"tab-item",class:{cur:t.iscur==s},attrs:{tabitem:s}},[t._v(t._s(a.title)),e("i")])})),0)])]),t._v(" "),e("div",{staticClass:"temprow-box"},[e("img",{attrs:{src:t.imgSrc[t.imgNumber],alt:""}}),t._v(" "),e("temprow",{attrs:{"goods-data":t.temprowData,"license-status":t.licenseStatus||0}}),t._v(" "),e("div",{directives:[{name:"show",rawName:"v-show",value:t.isload,expression:"isload"}],staticClass:"loaing"},[t._v(t._s(t.loadingmsg))])],1),t._v(" "),e("div",{directives:[{name:"show",rawName:"v-show",value:t.showBtn,expression:"showBtn"}],staticClass:"highmargin-btn-box"},[e("div",{staticClass:"highmargin-btn",on:{click:t.skipNexTab}},[t._v("点击跳转至“"+t._s(t.skiptext)+"”")])])])}),s,!1,null,"36ca2aed",null).exports}}]);
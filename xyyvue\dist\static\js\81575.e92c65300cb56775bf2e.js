(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[81575],{98321:function(e){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAFHklEQVRYR+1Wf2hVZRh+3nPWcpm/qBRCKGzce85psO2eiyWImGEGxUqKmokFpQhCCFG2LCKNMiyhxL8s7QdCSoRmFoGZETIwd86mbeecTZuT6IcipknptnvuE9/dubpdz73XuSKJnf/u/d73eZ/3eX98n+Aq/+Qq54dRgiOt0KiCowqOVIGR+v8/e9CyrEpgzBwAd5JSS8IQwSSAE0nJiuAMwFMi4pE4KMJmzxv/HfBtZriKDkvBRMKeqetYBuA+AOOHGUwR/ozkBt933Mv1vSyClpWaQ8prSrEY4PMAAoAnATlFQhPhJEAmA0gCqCz0IbFX0/CC5zn7yxEtSbCmpmZKGFa+DcijwJBbpxWQrSLYXVXFdsdx+uMCVVdXX1tZObGW5DwAjQCsvB3JrIhsOncuXNHT03a6GNGiBC3LnkliG4CbB5yZBbBDRH/V8w60lcs87tww6mcA+ssivPciUXRrmvZQMcxYgoZhPyGCdwFcEwG1kVwcBK5zJcQKfUyzfhagKfxEdPaXiCzwvJadhbaXEDTN9FNkdqOIaEo2EuvGjsXKYmVUgIlEfa2uSxMgd6lJFpGjJD7p7cW67m7nTFxSU6fOqBo3rm89gMXReR/ARt93tw+2H0LQsuobSNkO5MhlSCwOAufDUqqZZnoRwPfihgHAj9ls/9zOzkNHi2EYhv2MCN6KerxPBHd7nrMvb3+BYDKZTopwvwgmkAgBWRQELR+XImdZdXWk/n2+FQZ2HnqiaZ+Sbw/fd2wAqodjP8uynybxTkTytzDMpLu6Dv6sjC8QNAx7jwjU8lWVXeH77pvl+s00bTVEj+SABMs9z1Elg2VZ15NVnwK4J8KY7/vOjjKVWAvwuSj+Nt931dRfJGia9p8ArgPQ4ftOTTly6tww7BMiuAlAq+87qcE+iYRt6Dr8XLrE+iBwlpfCnD17dsXx42ePRVvjmO87txYQTDUDMiMCWeL7juqrkp9p2ucAjBHh557nNgw2jlT8Q4lAyvtB0PJkKTDDsJtEsGYgIe4MAveBQgVTJPaJoApgrwge9Dz3q9JlsX8AoNQ+FYZIdnU5J/P2yWRqoabJlijgi0Hgvl4MyzTTjSS3iEAHcBoIp/t+2+EhBNUP00w/DjA3tSTOi+Bh33e+KAY8OGsALqBWTaYH0OcCOTXUfd0vQsvz3CNxOJZlP0ZCxayIbpeGwTFj9qD9PIA3IpKhpqHJ85x1inNhgGiXNQOoK5YEyVVB4L4Sc66bZmoVICsHZkHdVLK0sLVibxLLsp8lsTY/RCS+CUNZcvhwS3dhoETCvlHX8UH0wrlwTEL15+ogcHLJDu3P2tvJik0A7oj+z4jIUs9r2VxoW/QuTibrG0S0j9ReHHBiLyCbdV1b095+4KdCINO0VQ/PGbCXHl0/v6u9vf34UGKpalJeArBQlTTqzxO6Lo0dHc7euCqUec3Yt2Uy2HhxP+Yg+knZA3Armd3d2dn6S+lBqr+F1OdpGhuzWcyKBiHnIiI7M5n+ZfmlPGyCeQfDSC8Q4WoA1TEgv5I4BOAkwN9FcmtlkggnA1IL5PZk4ddBsikI3F2lksslUc5g0LlmGOn5AJeJYFa+RMPw7wPwtUh2g+e1qvV1ydBdsYKFjoYx/QYgvF9ENbnUATRIToheQGpFhSJqn8ET4cFsVmvu6+OXxV42pZIcjoLlxJJp0+zxmlbBI0f2n71chcqCljP4r8//SQX/lVxGCY5U1lEFRxUcqQIj9b/qe/BvtuwDR9RyDYMAAAAASUVORK5CYII="},87341:function(e){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAACsUlEQVRYR+3XW+jNWRQH8M9fMoZSbk0xKfMgpFzLrZCJXGoawii3eCHKXWoapSaXYUYUjcmTeTDeMGNGwoMQEgZRRjFFhjHDq9ugVfvUrzOnc37n8iDOfjmdvdfa67u/67v22r8Wb/loecvxaQKsN0NNBpsM1stAvf5NDb5XDK7DF5iFK/WevEb/ITiC3zAv9shq8B664zyG1RigXreTGIVb6FUM8Ct8nSJMwcF6o1XpPyGxF26L8EMxwHa4iY/xJwbhSZVBajXvgMv4JLHXFy+LAcb/0OD+FOUAptYasUq/nzATr/EZDhf8S92Du7A4GWzDqiqDVWu+GWuT03dYnd2gFMA2uIh+yXAHVuJVtZEr2EfsTRlwlzC0kNpyDE7HPrTOBDiB2XjQIJCd8SMmZfZ7jhk4VI7BLLiHuI3hyeFvrMee4lNWAboV5mIDuiW/U+iDLvgfyGyKpyHEGswFuE/xB7ZgWebOjDtqZ2L5n5zgokqjAJegf/KJgtiKL5OcjmdABlE/F1fxY3TMgLueCT4WocWCLmMpThunP4sLuI/Y4z90wkcYnDIwBu0z+/2Opcm/MB3AQ0qR/r8KDGcZ/AajsQA3SjATAe6mQ+QkrqTZI/TA0xKrATIkdBTReqv6aIqrIK6EGBtTVU/EQIS2So1gM6oz+uuHWJOMlqeMVDxo3vdgpCv0GFq6mrpMBI/RFj1TRX6b5iJ9x3AHz9JcaPsaeicpRK/9txLCvAB3Y2HabBxC0MVjBM6kyXiVxF1aPCZnusR2rGgUwAgWvfmX1IpK7TsSpysAjOXQ1/jEcPyWHXkZjOr9HN+XSUtegF0xB7+mx0lDAFY6aKxHmzqXDAc06tGbl8E8AD/A3tRl5uNFHqdKNo0EWClWTetNgDXRlnFqMthksF4G6vVvavCdZ/ANi8F8KVcWONUAAAAASUVORK5CYII="},81575:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return c}});s(47042);var i,n={name:"resetPhone",activated:function(){var e=this;this.init(),this.putRequest("post","/app/merchantAuthentication/getInfoByMerchantId ",{merchantId:this.merchantId}).then((function(t){var s=t.data;"success"===s.status?e.curPhone=s.data.mobile:e.control_remind(s.msg)})).catch((function(t){e.control_remind("服务忙，请稍后在试！")}))},computed:{can_submit:function(){return!!(this.check_info.newPassword&&this.check_info.tel&&this.check_info.check_code)}},data:function(){return{isShow_remind:!1,isShow_noPass:!1,remind_info:"",check_info:{newPassword:null,tel:null,check_code:null},is_pass_word:!0,re_get_checkCode_time:119,get_code_text:"获取验证码",timer:null,in_progress:!1,curPhone:null}},methods:{init:function(){this.get_code_text="获取验证码",this.in_progress=!1,this.re_get_checkCode_time=119,clearInterval(this.timer),this.curPhone=null,this.check_info={newPassword:null,tel:null,check_code:null}},submitAll:function(){var e=this;this.can_submit&&(this.check_input("tel")&&this.putRequest("post","/app/merchantAuthentication/changeAuthMobile",{authMobile:this.check_info.tel,password:this.check_info.newPassword,verificationCode:this.check_info.check_code,changeFlag:1,merchantId:this.merchantId}).then((function(t){var s=t.data;"success"===s.status?(e.control_remind("修改成功！即将跳转到审核详情页。"),setTimeout((function(){e.$router.push({name:"customConfirmProgress"})}),3e3)):(e.control_remind(s.msg),e.get_code_text="获取验证码",e.in_progress=!1,e.re_get_checkCode_time=119,clearInterval(e.timer))})).catch((function(t){e.control_remind("服务忙，请稍后在试！")})))},control_remind:function(e){var t=this;clearTimeout(i),this.remind_info=e,this.isShow_remind=!0,i=setTimeout((function(){t.isShow_remind=!1}),4e3)},check_input:function(e){if(!this.check_info[e])return this.control_remind("此信息是必填信息！"),!1;switch(e){case"tel":return!!/^1[3456789]\d{9}$/.test(this.check_info[e])||(this.control_remind("手机号格式不正确！"),!1)}},get_checkCode:function(){var e=this;this.in_progress||this.check_input("tel")&&(this.in_progress=!0,this.timer=setInterval((function(){0===e.re_get_checkCode_time?(e.get_code_text="获取验证码",e.in_progress=!1,clearInterval(e.timer)):(e.get_code_text=e.re_get_checkCode_time+"s重新获取",e.re_get_checkCode_time--)}),1e3),this.putRequest("post","/app/merchantAuthentication/sendAuthenticationVerificationCode",{mobile:this.check_info.tel,password:this.check_info.newPassword,changeFlag:1,merchantId:this.merchantId}).then((function(t){var s=t.data;"success"===s.status||(e.control_remind(s.msg),e.get_code_text="获取验证码",e.in_progress=!1,e.re_get_checkCode_time=119,clearInterval(e.timer))})).catch((function(t){e.in_progress=!1,clearInterval(e.timer),e.get_code_text="获取验证码",e.control_remind("服务忙，请稍后在试！")})))}},watch:{"check_info.tel":function(e){e.length>11&&(this.check_info.tel=this.check_info.tel.slice(0,11))},"check_info.check_code":function(e){e.length>10&&(this.check_info.check_code=this.check_info.check_code.slice(0,10))}}},c=(0,s(51900).Z)(n,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{attrs:{id:"resetPhone"}},[i("div",{directives:[{name:"show",rawName:"v-show",value:e.isShow_noPass,expression:"isShow_noPass"}],staticClass:"no-pass"},[i("div",{staticClass:"no-pass-info"},[i("div",{staticClass:"info"},[e._v("\n                输入的被委托人姓名/身份证号与平台留存的企业被委托人信息不符，如有疑问请联系您的专属销售进行核实\n            ")]),e._v(" "),i("div",{staticClass:"close",on:{click:function(t){e.isShow_noPass=!1}}},[e._v("确定")])])]),e._v(" "),i("div",{staticClass:"business_title"},[e._v("\n        当前账户："+e._s(e.curPhone)+"\n    ")]),e._v(" "),i("div",{staticClass:"input-container password-container"},[e._m(0),e._v(" "),i("div",{staticClass:"input"},["checkbox"==(e.is_pass_word?"password":"text")?i("input",{directives:[{name:"model",rawName:"v-model",value:e.check_info.newPassword,expression:"check_info.newPassword"}],attrs:{placeholder:"请输入登录密码",type:"checkbox"},domProps:{checked:Array.isArray(e.check_info.newPassword)?e._i(e.check_info.newPassword,null)>-1:e.check_info.newPassword},on:{change:function(t){var s=e.check_info.newPassword,i=t.target,n=!!i.checked;if(Array.isArray(s)){var c=e._i(s,null);i.checked?c<0&&e.$set(e.check_info,"newPassword",s.concat([null])):c>-1&&e.$set(e.check_info,"newPassword",s.slice(0,c).concat(s.slice(c+1)))}else e.$set(e.check_info,"newPassword",n)}}}):"radio"==(e.is_pass_word?"password":"text")?i("input",{directives:[{name:"model",rawName:"v-model",value:e.check_info.newPassword,expression:"check_info.newPassword"}],attrs:{placeholder:"请输入登录密码",type:"radio"},domProps:{checked:e._q(e.check_info.newPassword,null)},on:{change:function(t){return e.$set(e.check_info,"newPassword",null)}}}):i("input",{directives:[{name:"model",rawName:"v-model",value:e.check_info.newPassword,expression:"check_info.newPassword"}],attrs:{placeholder:"请输入登录密码",type:e.is_pass_word?"password":"text"},domProps:{value:e.check_info.newPassword},on:{input:function(t){t.target.composing||e.$set(e.check_info,"newPassword",t.target.value)}}})]),e._v(" "),i("div",{staticClass:"password-check",on:{click:function(t){e.is_pass_word=!e.is_pass_word}}},[i("img",{directives:[{name:"show",rawName:"v-show",value:e.is_pass_word,expression:"is_pass_word"}],attrs:{src:s(87341),alt:""}}),e._v(" "),i("img",{directives:[{name:"show",rawName:"v-show",value:!e.is_pass_word,expression:"!is_pass_word"}],attrs:{src:s(98321),alt:""}})])]),e._v(" "),i("div",{staticClass:"input-container phone-container"},[e._m(1),e._v(" "),i("div",{staticClass:"input"},[i("input",{directives:[{name:"model",rawName:"v-model",value:e.check_info.tel,expression:"check_info.tel"}],attrs:{type:"tel",placeholder:"请输入认证手机号"},domProps:{value:e.check_info.tel},on:{change:function(t){return e.check_input("tel")},input:function(t){t.target.composing||e.$set(e.check_info,"tel",t.target.value)}}})]),e._v(" "),i("div",{staticClass:"post-checkCode"},[i("span",{class:{in_progress:e.in_progress},on:{click:e.get_checkCode}},[e._v("\n                    "+e._s(e.get_code_text)+"\n            ")])])]),e._v(" "),i("div",{staticClass:"input-container"},[e._m(2),e._v(" "),i("div",{staticClass:"input"},[i("input",{directives:[{name:"model",rawName:"v-model",value:e.check_info.check_code,expression:"check_info.check_code"}],attrs:{type:"tel",placeholder:"请输入"},domProps:{value:e.check_info.check_code},on:{input:function(t){t.target.composing||e.$set(e.check_info,"check_code",t.target.value)}}})])]),e._v(" "),i("div",{staticClass:"submit-btn",style:{backgroundColor:e.can_submit?"#00B377":"#A9AEB7"},on:{click:e.submitAll}},[e._v("\n        提交\n    ")]),e._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:e.isShow_remind,expression:"isShow_remind"}],staticClass:"remind-container"},[e._v("\n        "+e._s(e.remind_info)+"\n    ")])])}),[function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"input-label"},[s("span",{staticStyle:{color:"red"}},[e._v("*")]),e._v("密码：\n        ")])},function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"input-label"},[s("span",{staticStyle:{color:"red"}},[e._v("*")]),e._v("认证手机号：\n        ")])},function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"input-label"},[s("span",{staticStyle:{color:"red"}},[e._v("*")]),e._v("验证码：\n        ")])}],!1,null,"b3e94354",null).exports}}]);
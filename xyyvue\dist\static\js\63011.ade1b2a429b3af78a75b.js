"use strict";(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[63011],{86370:function(t,e,s){s.r(e),s.d(e,{default:function(){return r}});s(82772);var a=s(24388),o=s(59502),i={data:function(){return{goodsData:[],licenseStatus:0,scrollload:!1,isload:!1,totalpage:0,pagecur:0,hdid:"",textcolor:"fff",bdcolor:"fff",picName:"",loadingmsg:"正在加载···",imgUrl:"",bannerUrl:"",ispadding:!1,recordObj:{recordPos:0},pagename:"",img_err_load:!0}},methods:{err_load:function(){this.img_err_load=!1},getDataList:function(){var t=this,e=null;this.isload=!1,o.Z.$emit("changeloading",!0),e=null!=window.sessionStorage.getItem("recordObj")?1e3:10,this.putRequest("post","/app/layout/initExhibitionModulePage?sort=1",{merchantId:this.merchantId,limit:e,offset:this.pagecur,exhibitionId:this.hdid}).then((function(e){if("success"==e.data.status){t.isload=!1;var s=e.data.data.rows;t.goodsData.push.apply(t.goodsData,s),t.totalpage=e.data.data.pageCount,t.scrollload=!0,t.licenseStatus=e.data.data.licenseStatus}t.$nextTick((function(){o.Z.$emit("changeloading",!1),null!=window.sessionStorage.getItem("recordObj")&&(this.scrollPos=JSON.parse(window.sessionStorage.getItem("recordObj")).recordPos,0!=this.scrollPos&&document.querySelector("#app").scroll(0,this.scrollPos))}))})).catch((function(t){o.Z.$emit("changeloading",!1)}))},getNaturalList:function(){var t=this;this.isload=!1,o.Z.$emit("changeloading",!0),this.putRequest("post","app/naturePerson/getProductsByBranch?sort=1",{merchantId:this.merchantId,typeCode:this.hdid}).then((function(e){if("success"==e.data.status){t.isload=!1;var s=e.data.data.skuDtoList;t.goodsData.push.apply(t.goodsData,s)}t.$nextTick((function(){o.Z.$emit("changeloading",!1),null!=window.sessionStorage.getItem("recordObj")&&(this.scrollPos=JSON.parse(window.sessionStorage.getItem("recordObj")).recordPos,0!=this.scrollPos&&document.querySelector("#app").scroll(0,this.scrollPos))}))})).catch((function(t){o.Z.$emit("changeloading",!1)}))},moveEvent:function(){var t=window.screen.height,e=document.querySelector("#temprowpage").scrollTop,s=document.querySelector(".temprowpage").scrollHeight;e>800?o.Z.$emit("showBtn",{isShow:!0,dom:"#temprowpage"}):o.Z.$emit("showBtn",{isShow:!1,dom:""}),s-e-400<=t&&this.scrollload&&(this.scrollload=!1,this.pagecur>=this.totalpage-1?(this.isload=!0,this.loadingmsg="无更多数据"):(this.isload=!0,this.loadingmsg="正在加载···",this.pagecur++,this.getDataList()));try{window.webkit.messageHandlers.hideKeyboard.postMessage({hide:"1"})}catch(t){}},fetchdata:function(){}},mounted:function(){this.imgUrl=this.imgBaseUrl,this.bannerUrl="/static/xyyvue/dist"},activated:function(){this.setAppTitle(""),this.pagecur=0,this.goodsData=[];var t=this.$route.params;-1!=this.$route.path.indexOf("/mainvenue")&&(this.ispadding=!0),this.hdid=t.hdid,this.textcolor=t.textcolor,this.bdcolor=t.bdcolor;var e=this.$route.query.ybm_title;e&&this.setAppTitle(e),this.picName=t.picname,this.pagename=this.$route.query.pagename,"naturalperson"==this.pagename?this.getNaturalList():this.getDataList()},deactivated:function(){o.Z.$emit("showBtn",{isShow:!1,dom:""})},components:{temprow:a.Z}},r=(0,s(51900).Z)(i,(function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{style:{background:"#"+t.bdcolor},attrs:{id:"temprowpage"}},[s("div",{staticClass:"temprowpage",class:{ispadding:t.ispadding},on:{touchmove:function(e){return e.stopPropagation(),t.moveEvent.apply(null,arguments)}}},[t.img_err_load?s("div",{staticClass:"banner"},["null"!=t.picName?s("img",{attrs:{src:t.imgUrl+"/ybm/applayoutbanner/"+t.picName+".jpg",alt:""},on:{error:t.err_load}}):s("img",{attrs:{src:t.bannerUrl+"/static/images/tempbanner/"+t.hdid+".jpg",alt:""},on:{error:t.err_load}})]):t._e(),t._v(" "),s("temprow",{attrs:{"goods-data":t.goodsData,recordObj:t.recordObj,"license-status":t.licenseStatus||0}}),t._v(" "),t.isload?s("div",{staticClass:"loadingtips",style:{color:"#"+t.textcolor}},[t._v("\n      "+t._s(t.loadingmsg)+"\n    ")]):t._e()],1)])}),[],!1,null,"02f44373",null).exports}}]);
(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[68108],{26855:function(t,a,i){t.exports=i.p+"static/img/yiyuan_02.21539a7.png"},25647:function(t,a,i){"use strict";i.r(a),i.d(a,{default:function(){return l}});var e=[function(){var t=this.$createElement,a=this._self._c||t;return a("div",{staticClass:"banner"},[a("img",{attrs:{src:i(26855),alt:""}})])}],s=i(4942),n=(i(91058),i(24388)),o=i(59502),c={data:function(){var t;return t={iscur:0,temprowData:[],licenseStatus:0,isload:!1,loadingmsg:"正在加载···",tabData:[{hdid:"ZS201907241056355264",title:"慢病用药"},{hdid:"ZS201907241056476890",title:"抗菌消炎"},{hdid:"ZS201907241057007730",title:"其他热销"}],isfixed:!1,chooseHdid:"ZS201907241056355264",tabIndex:0,scrollload:!0},(0,s.Z)(t,"isload",!1),(0,s.Z)(t,"loadingmsg","正在加载···"),(0,s.Z)(t,"pagecur",0),(0,s.Z)(t,"totalpage",0),(0,s.Z)(t,"skiptext","抗菌消炎"),(0,s.Z)(t,"showBtn",!1),t},methods:{tabitemclick:function(t){document.querySelector("#pinleiyiyuan").scrollTop=document.querySelector(".checktab").offsetTop,this.translatetabs()},skipNexTab:function(){this.tabIndex++,this.tabIndex=this.tabIndex==this.tabData.length?0:this.tabIndex,this.translatetabs()},moveEvent:function(){var t=document.querySelector(".checktab").offsetTop,a=document.querySelector("#pinleiyiyuan").scrollTop;this.isfixed=a>=t,a>800&&o.Z.$emit("showBtn",{isShow:!0,dom:"#pinleiyiyuan"});var i=window.screen.height;document.querySelector("#pinleiyiyuan").scrollHeight-a<=i&&this.scrollload&&(this.scrollload=!1,this.pagecur>=this.totalpage-1?(this.isload=!1,this.showBtn=!0):(this.isload=!0,this.loadingmsg="正在加载···",this.pagecur++,this.getDatalist(this.chooseHdid,"temprowData")))},changetabs:function(t){this.tabIndex=parseInt(t.target.getAttribute("tabitem")),this.translatetabs()},translatetabs:function(){this.iscur=this.tabIndex,this.chooseHdid=this.tabData[this.tabIndex].hdid,document.querySelector("#pinleiyiyuan").scrollTop=document.querySelector(".checktab").offsetTop;var t=this.tabIndex+1;t=t==this.tabData.length?0:t,this.skiptext=this.tabData[t].title,this.pagecur=0,this.showBtn=!1,this.getDatalist(this.chooseHdid,"temprowData")},getDatalist:function(t,a){var i=this;this.putRequest("post","/app/layout/initExhibitionModulePage?sort=1",{merchantId:this.merchantId,limit:10,offset:this.pagecur,exhibitionId:t}).then((function(t){if("success"==t.data.status){0==i.pagecur&&(i.totalpage=t.data.data.pageCount,i[a]=[]),i.isload=!1,i.scrollload=!0;var e=t.data.data.rows;i[a].push.apply(i[a],e),i.licenseStatus=t.data.data.licenseStatus}i.$nextTick((function(){o.Z.$emit("changeloading",!1)}))})).catch((function(t){}))}},mounted:function(){document.querySelector("#pinleiyiyuan").addEventListener("scroll",this.moveEvent),this.getDatalist("ZS201907241056355264","temprowData")},components:{temprow:n.Z},activated:function(){this.setAppTitle("医院品种专区")}},l=(0,i(51900).Z)(c,(function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("div",{attrs:{id:"pinleiyiyuan"}},[t._m(0),t._v(" "),i("div",{staticClass:"checktab"},[i("div",{staticClass:"tabs-box",class:{tabfixed:t.isfixed},on:{click:t.changetabs}},[i("ul",{staticClass:"tab-scroll"},t._l(t.tabData,(function(a,e){return i("li",{key:e,staticClass:"tab-item",class:{cur:t.iscur==e},attrs:{tabitem:e}},[t._v(t._s(a.title)),i("i")])})),0)])]),t._v(" "),i("div",{staticClass:"temprow-box"},[i("temprow",{attrs:{"goods-data":t.temprowData,"license-status":t.licenseStatus||0}}),t._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:t.isload,expression:"isload"}],staticClass:"loaing"},[t._v(t._s(t.loadingmsg))])],1),t._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:t.showBtn,expression:"showBtn"}],staticClass:"highmargin-btn-box"},[i("div",{staticClass:"highmargin-btn",on:{click:t.skipNexTab}},[t._v("点击跳转至“"+t._s(t.skiptext)+"”")])])])}),e,!1,null,"d786233c",null).exports}}]);
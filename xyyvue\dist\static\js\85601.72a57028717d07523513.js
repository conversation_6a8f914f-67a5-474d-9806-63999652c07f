(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[85601],{81150:function(t){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t==1/e:t!=t&&e!=e}},64765:function(t,e,s){"use strict";var r=s(27007),n=s(19670),a=s(84488),i=s(81150),o=s(41340),u=s(97651);r("search",(function(t,e,s){return[function(e){var s=a(this),r=null==e?void 0:e[t];return void 0!==r?r.call(e,s):new RegExp(e)[t](o(s))},function(t){var r=n(this),a=o(t),c=s(e,r,a);if(c.done)return c.value;var l=r.lastIndex;i(l,0)||(r.lastIndex=0);var v=u(r,a);return i(r.lastIndex,l)||(r.lastIndex=l),null===v?-1:v.index}]}))},85601:function(t,e,s){"use strict";s.r(e),s.d(e,{default:function(){return n}});s(74916),s(64765),s(54747),s(24603),s(39714),s(69600),s(23123);var r={data:function(){return{routes:[],words:"",remarkStatus:!1}},mounted:function(){this.routes=this.$router.options.routes},watch:{words:function(t){this.search(t)}},methods:{search:function(t){var e=this;this.routes=t?[]:this.$router.options.routes;var s=this.$router.options.routes,r=[];s.forEach((function(s){var n=s.meta&&s.meta.title?s.meta.title:"";n&&(e.getWordRegStatus(t,n)&&r.push(s))})),r.length?this.remarkStatus=!1:this.remarkStatus=!0,this.routes=r},getWordRegStatus:function(t,e){return!!new RegExp("(.*)(".concat(t.split("").join(")(.*)("),")(.*)"),"i").test(e)}}},n=(0,s(51900).Z)(r,(function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{attrs:{id:"testOne"}},[s("div",{staticClass:"testOne-title-con"},[s("div",{staticClass:"testOne-title"},[t._v("活动页面导航")]),t._v(" "),s("div",{staticClass:"testOne-search"},[s("input",{directives:[{name:"model",rawName:"v-model",value:t.words,expression:"words"}],attrs:{type:"text",placeholder:"请输入关键字搜索"},domProps:{value:t.words},on:{input:function(e){e.target.composing||(t.words=e.target.value)}}})])]),t._v(" "),s("div",{staticClass:"testOne-content"},[s("div",{staticStyle:{clear:"both"}}),t._v(" "),t._l(t.routes,(function(e,r){return s("div",{key:r,staticClass:"testOne-con"},[s("router-link",{staticClass:"testOne-con-a",attrs:{to:e.path}},[t._v(t._s(e.meta&&e.meta.title?e.meta.title:"无名字"))])],1)})),t._v(" "),s("div",{staticStyle:{clear:"both"}}),t._v(" "),s("div",{directives:[{name:"show",rawName:"v-show",value:t.remarkStatus,expression:"remarkStatus"}],staticClass:"testOne-con-foot"},[t._v("未搜索到相关信息～")])],2)])}),[],!1,null,"0c55ed20",null).exports}}]);
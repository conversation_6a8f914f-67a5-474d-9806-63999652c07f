(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[92051],{9625:function(t,e,n){t.exports=n.p+"static/img/app_02.6a4a417.png"},31451:function(t,e,n){t.exports=n.p+"static/img/quan_02.7efa372.png"},47070:function(t,e,n){t.exports=n.p+"static/img/quan_03.a450710.png"},25199:function(t,e,n){t.exports=n.p+"static/img/quan_04.f34028e.png"},22727:function(t,e,n){"use strict";n.r(e),n.d(e,{default:function(){return c}});var s=[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("img",{attrs:{src:n(31451)}})])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"cont"},[e("img",{attrs:{src:n(25199)}})])}],a=(n(74916),n(15306),n(24603),n(39714),n(59502)),i={data:function(){return{autoHeight:{height:"auto"},branchCode:"",isshownow:!1}},methods:{getDatalist:function(){var t=this;this.putRequest("post","/app/getBranchCode",{merchantId:this.merchantId}).then((function(e){a.Z.$emit("changeloading",!1),t.isdata=!0,"success"==e.data.status&&(t.branchCode=e.data.data)})).catch((function(t){}))},getinitdata:function(){Date.prototype.format=function(t){var e={"M+":this.getMonth()+1,"d+":this.getDate(),"h+":this.getHours(),"m+":this.getMinutes(),"s+":this.getSeconds(),"q+":Math.floor((this.getMonth()+3)/3),S:this.getMilliseconds()};for(var n in/(y+)/.test(t)&&(t=t.replace(RegExp.$1,(this.getFullYear()+"").substr(4-RegExp.$1.length))),e)new RegExp("("+n+")").test(t)&&(t=t.replace(RegExp.$1,1==RegExp.$1.length?e[n]:("00"+e[n]).substr((""+e[n]).length)));return t};var t=(new Date).format("yyyy-MM-dd hh:mm:ss");new Date(t.replace(/-/g,"/")).getTime()>new Date("2019-10-12 00:00:00".replace(/-/g,"/")).getTime()&&(this.isshownow=!0)}},activated:function(){this.setAppTitle("新人专享券")},mounted:function(){this.autoHeight={height:document.documentElement.clientHeight+"px"}},created:function(){this.getDatalist(),this.getinitdata()}},c=(0,n(51900).Z)(i,(function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{attrs:{id:"stampspage"}},[t._m(0),t._v(" "),s("div",{staticClass:"quan"},[s("router-link",{attrs:{to:"/voucherscenter?ybm_title=领券中心"}},["XS130000"==t.branchCode&&t.isshownow?s("img",{attrs:{src:n(9625)}}):s("img",{attrs:{src:n(47070)}})])],1),t._v(" "),t._m(1)])}),s,!1,null,"5c53c60a",null).exports}}]);
(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[79190],{71129:function(t,a,i){t.exports=i.p+"static/img/app_01.2391891.png"},59598:function(t,a,i){t.exports=i.p+"static/img/app_02.fc78c4d.png"},77988:function(t,a,i){t.exports=i.p+"static/img/app_03.3d464e3.png"},35884:function(t,a,i){t.exports=i.p+"static/img/app_04.ab0131e.png"},8185:function(t,a,i){t.exports=i.p+"static/img/app_05.019718a.png"},93445:function(t,a,i){t.exports=i.p+"static/img/app_06.affac66.png"},26568:function(t,a,i){t.exports=i.p+"static/img/app_07.65c1828.png"},84993:function(t,a,i){t.exports=i.p+"static/img/app_08.29ab0ab.png"},37545:function(t,a,i){t.exports=i.p+"static/img/app_09.6984557.png"},59104:function(t,a,i){"use strict";i.r(a),i.d(a,{default:function(){return r}});var s=[function(){var t=this.$createElement,a=this._self._c||t;return a("div",{staticClass:"banner"},[a("img",{attrs:{src:i(71129),alt:""}})])}],e=i(4942),o=(i(91058),i(24388)),n=i(59502),c={data:function(){var t;return t={iscur:0,temprowData:[],licenseStatus:0,isload:!1,loadingmsg:"正在加载···",tabData:[{hdid:"ZS201908031027513937",title:"慢病用药"},{hdid:"ZS201908031028202158",title:"感冒用药"},{hdid:"ZS201908031028412732",title:"皮肤用药"},{hdid:"ZS201908031029002532",title:"消化系统"},{hdid:"ZS201908031029194326",title:"抗菌消炎"},{hdid:"ZS201908031029355840",title:"滋补保健"},{hdid:"ZS201908031029537472",title:"风湿骨痛"},{hdid:"ZS201908031030164067",title:"其他用药"}],isfixed:!1,chooseHdid:"ZS201908031027513937",tabIndex:0,scrollload:!0},(0,e.Z)(t,"isload",!1),(0,e.Z)(t,"loadingmsg","正在加载···"),(0,e.Z)(t,"pagecur",0),(0,e.Z)(t,"totalpage",0),(0,e.Z)(t,"skiptext","感冒用药"),(0,e.Z)(t,"showBtn",!1),(0,e.Z)(t,"imgNumber",0),(0,e.Z)(t,"imgSrc",[i(59598),i(77988),i(35884),i(8185),i(93445),i(26568),i(84993),i(37545)]),t},methods:{tabitemclick:function(t){document.querySelector("#haigoucqpage").scrollTop=document.querySelector(".checktab").offsetTop,this.translatetabs()},skipNexTab:function(){this.tabIndex++,this.tabIndex=this.tabIndex==this.tabData.length?0:this.tabIndex,this.translatetabs()},moveEvent:function(){var t=document.querySelector(".checktab").offsetTop,a=document.querySelector("#haigoucqpage").scrollTop;this.isfixed=a>=t,a>800&&n.Z.$emit("showBtn",{isShow:!0,dom:"#haigoucqpage"});var i=window.screen.height;document.querySelector("#haigoucqpage").scrollHeight-a<=i&&this.scrollload&&(this.scrollload=!1,this.pagecur>=this.totalpage-1?(this.isload=!1,this.showBtn=!0):(this.isload=!0,this.loadingmsg="正在加载···",this.pagecur++,this.getDatalist(this.chooseHdid,"temprowData")))},changetabs:function(t){this.tabIndex=parseInt(t.target.getAttribute("tabitem")),this.translatetabs(),this.imgNumber=this.tabIndex-0},translatetabs:function(){this.iscur=this.tabIndex,this.chooseHdid=this.tabData[this.tabIndex].hdid,document.querySelector("#haigoucqpage").scrollTop=document.querySelector(".checktab").offsetTop;var t=this.tabIndex+1;t=t==this.tabData.length?0:t,this.skiptext=this.tabData[t].title,this.pagecur=0,this.showBtn=!1,this.getDatalist(this.chooseHdid,"temprowData"),this.imgNumber=this.tabIndex-0},getDatalist:function(t,a){var i=this;this.putRequest("post","/app/layout/initExhibitionModulePage?sort=1",{merchantId:this.merchantId,limit:10,offset:this.pagecur,exhibitionId:t}).then((function(t){if("success"==t.data.status){0==i.pagecur&&(i.totalpage=t.data.data.pageCount,i[a]=[]),i.isload=!1,i.scrollload=!0;var s=t.data.data.rows;i[a].push.apply(i[a],s),i.licenseStatus=t.data.data.licenseStatus}i.$nextTick((function(){n.Z.$emit("changeloading",!1)}))})).catch((function(t){}))}},mounted:function(){document.querySelector("#haigoucqpage").addEventListener("scroll",this.moveEvent),this.getDatalist("ZS201908031027513937","temprowData")},components:{temprow:o.Z},activated:function(){this.setAppTitle("盛夏嗨购")}},r=(0,i(51900).Z)(c,(function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("div",{attrs:{id:"haigoucqpage"}},[t._m(0),t._v(" "),i("div",{staticClass:"checktab"},[i("div",{staticClass:"tabs-box",class:{tabfixed:t.isfixed},on:{click:t.changetabs}},[i("ul",{staticClass:"tab-scroll"},t._l(t.tabData,(function(a,s){return i("li",{key:s,staticClass:"tab-item",class:{cur:t.iscur==s},attrs:{tabitem:s}},[t._v(t._s(a.title)),i("i")])})),0)])]),t._v(" "),i("div",{staticClass:"temprow-box"},[i("img",{attrs:{src:t.imgSrc[t.imgNumber],alt:""}}),t._v(" "),i("temprow",{attrs:{"goods-data":t.temprowData,"license-status":t.licenseStatus||0}}),t._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:t.isload,expression:"isload"}],staticClass:"loaing"},[t._v(t._s(t.loadingmsg))])],1),t._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:t.showBtn,expression:"showBtn"}],staticClass:"highmargin-btn-box"},[i("div",{staticClass:"highmargin-btn",on:{click:t.skipNexTab}},[t._v("点击跳转至“"+t._s(t.skiptext)+"”")])])])}),s,!1,null,"79027133",null).exports}}]);
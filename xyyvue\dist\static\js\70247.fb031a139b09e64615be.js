(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[70247],{46702:function(t,a,s){t.exports=s.p+"static/img/app_01.df890c2.png"},85229:function(t,a,s){t.exports=s.p+"static/img/app_02.60ddef5.png"},672:function(t,a,s){t.exports=s.p+"static/img/app_03.3a4bc7b.png"},87490:function(t,a,s){t.exports=s.p+"static/img/app_04.eb89b5a.png"},16140:function(t,a,s){t.exports=s.p+"static/img/app_05.9e7dbfa.png"},27169:function(t,a,s){t.exports=s.p+"static/img/app_06.260cd02.png"},38826:function(t,a,s){"use strict";s.r(a),s.d(a,{default:function(){return r}});var e=[function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"banner"},[e("img",{attrs:{src:s(46702),alt:""}}),t._v(" "),e("img",{attrs:{src:s(85229),alt:""}})])},function(){var t=this.$createElement,a=this._self._c||t;return a("div",[a("img",{attrs:{src:s(87490),alt:""}})])},function(){var t=this.$createElement,a=this._self._c||t;return a("div",[a("img",{attrs:{src:s(27169),alt:""}})])}],i=s(24388),o=s(59502),n={data:function(){return{temprowData:[],isload:!1,tabIndex:0,scrollload:!0,loadingmsg:"正在加载···",licenseStatus:0,pagecur:0,totalpage:0}},methods:{getDatalist:function(t,a){var s=this;this.putRequest("post","/app/layout/initExhibitionModulePage?sort=1",{merchantId:this.merchantId,limit:10,offset:this.pagecur,exhibitionId:"ZS201809180930551000"}).then((function(t){if("success"==t.data.status){s.isload=!1;var a=t.data.data.rows;s.temprowData.push.apply(s.temprowData,a),s.totalpage=t.data.data.pageCount,s.scrollload=!0,s.licenseStatus=t.data.data.licenseStatus}s.$nextTick((function(){o.Z.$emit("changeloading",!1)}))})).catch((function(t){}))},moveEvent:function(){var t=window.screen.height,a=document.querySelector("#storegoodsholiday").scrollTop,s=document.querySelector("#storegoodsholiday").scrollHeight;a>800?o.Z.$emit("showBtn",{isShow:!0,dom:"#storegoodsholiday"}):o.Z.$emit("showBtn",{isShow:!1,dom:""}),s-a-400<=t&&this.scrollload&&(this.scrollload=!1,this.pagecur>=this.totalpage-1?(this.isload=!0,this.loadingmsg="无更多数据"):(this.isload=!0,this.loadingmsg="正在加载···",this.pagecur++,this.getDatalist()));try{window.webkit.messageHandlers.hideKeyboard.postMessage({hide:"1"})}catch(t){}}},mounted:function(){document.querySelector("#storegoodsholiday"),this.getDatalist("ZS201809180930551000","temprowData")},activated:function(){this.setAppTitle("节前囤货日")},components:{temprow:i.Z}},r=(0,s(51900).Z)(n,(function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{attrs:{id:"storegoodsholiday"},on:{touchmove:t.moveEvent}},[t._m(0),t._v(" "),e("router-link",{attrs:{to:"/voucherscenter?ybm_title=领券中心"}},[e("img",{attrs:{src:s(672),alt:""}})]),t._v(" "),t._m(1),t._v(" "),e("div",{staticClass:"com"},[e("img",{attrs:{src:s(16140),alt:""}}),t._v(" "),e("div",{staticClass:"com-in"},[e("a",{attrs:{href:"https://app.ybm100.com/public/2019/9/306.html"}}),t._v(" "),e("router-link",{attrs:{to:"/packagesuper?ybm_title=超值套餐"}})],1)]),t._v(" "),t._m(2),t._v(" "),e("div",{staticClass:"temprow-box"},[e("temprow",{attrs:{"goods-data":t.temprowData,"license-status":t.licenseStatus||0}}),t._v(" "),e("div",{directives:[{name:"show",rawName:"v-show",value:t.isload,expression:"isload"}],staticClass:"loaing"},[t._v(t._s(t.loadingmsg))])],1)],1)}),e,!1,null,"5ddd7b92",null).exports}}]);
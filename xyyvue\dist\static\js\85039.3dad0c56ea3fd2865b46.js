(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[85039],{85258:function(t,a,e){t.exports=e.p+"static/img/app_01.12cceb4.png"},77258:function(t,a,e){t.exports=e.p+"static/img/app_03.6c20853.png"},53048:function(t,a,e){t.exports=e.p+"static/img/app_07.618b7e2.png"},11498:function(t,a,e){t.exports=e.p+"static/img/app_08.5380a43.png"},64260:function(t,a,e){t.exports=e.p+"static/img/app_09.72a11a4.png"},71144:function(t,a,e){t.exports=e.p+"static/img/app_10.2ef8816.png"},18417:function(t,a,e){"use strict";e.r(a),e.d(a,{default:function(){return r}});var s=[function(){var t=this.$createElement,a=this._self._c||t;return a("div",{staticClass:"banner"},[a("img",{attrs:{src:e(85258),alt:""}})])}],i=e(4942),n=(e(91058),e(24388)),c=e(59502),o={data:function(){var t;return t={iscur:0,licenseStatus:0,temprowData:[],isload:!1,loadingmsg:"正在加载···",tabData:[{hdid:"ZS201907151030002759",title:"慢病用药"},{hdid:"ZS201909181017175003",title:"风湿骨痛"},{hdid:"ZS201909181016481410",title:"感冒呼吸"},{hdid:"ZS201909181017032568",title:"滋补保健"},{hdid:"ZS201909181017303324",title:"热销品类"}],isfixed:!1,chooseHdid:"ZS201907151030002759",tabIndex:0,scrollload:!0},(0,i.Z)(t,"isload",!1),(0,i.Z)(t,"loadingmsg","正在加载···"),(0,i.Z)(t,"pagecur",0),(0,i.Z)(t,"totalpage",0),(0,i.Z)(t,"skiptext","风湿骨痛"),(0,i.Z)(t,"showBtn",!1),(0,i.Z)(t,"imgNumber",0),(0,i.Z)(t,"imgSrc",[e(77258),e(64260),e(71144),e(11498),e(53048)]),(0,i.Z)(t,"cur_tab","慢病用药"),t},methods:{tabitemclick:function(t){document.querySelector("#newproductcq").scrollTop=document.querySelector(".checktab").offsetTop,this.translatetabs()},skipNexTab:function(){this.tabIndex++,this.tabIndex=this.tabIndex==this.tabData.length?0:this.tabIndex,this.translatetabs()},moveEvent:function(){var t=document.querySelector(".checktab").offsetTop,a=document.querySelector("#newproductcq").scrollTop;this.isfixed=a>=t,a>800&&c.Z.$emit("showBtn",{isShow:!0,dom:"#newproductcq"});var e=window.screen.height;document.querySelector("#newproductcq").scrollHeight-a<=e&&this.scrollload&&(this.scrollload=!1,this.pagecur>=this.totalpage-1?(this.isload=!1,this.showBtn=!0):(this.isload=!0,this.loadingmsg="正在加载···",this.pagecur++,this.getDatalist(this.chooseHdid,"temprowData")))},changetabs:function(t){this.tabIndex=parseInt(t.target.getAttribute("tabitem")),this.translatetabs(),this.imgNumber=this.tabIndex-0,this.cur_tab=t.target.innerText},translatetabs:function(){this.iscur=this.tabIndex,this.chooseHdid=this.tabData[this.tabIndex].hdid,document.querySelector("#newproductcq").scrollTop=document.querySelector(".checktab").offsetTop;var t=this.tabIndex+1;t=t==this.tabData.length?0:t,this.skiptext=this.tabData[t].title,this.pagecur=0,this.showBtn=!1,this.getDatalist(this.chooseHdid,"temprowData"),this.imgNumber=this.tabIndex-0},getDatalist:function(t,a){var e=this;this.putRequest("post","/app/layout/initExhibitionModulePage?sort=1",{merchantId:this.merchantId,limit:10,offset:this.pagecur,exhibitionId:t}).then((function(t){if("success"==t.data.status){t.data.data.rows.length<3&&(e.showBtn=!0),0==e.pagecur&&(e.totalpage=t.data.data.pageCount,e[a]=[]),e.isload=!1,e.scrollload=!0;var s=t.data.data.rows;e[a].push.apply(e[a],s),e.licenseStatus=t.data.data.licenseStatus}e.$nextTick((function(){c.Z.$emit("changeloading",!1)}))})).catch((function(t){}))}},mounted:function(){document.querySelector("#newproductcq").addEventListener("scroll",this.moveEvent),this.getDatalist("ZS201907151030002759","temprowData")},components:{temprow:n.Z},activated:function(){this.setAppTitle("新品发布汇")}},r=(0,e(51900).Z)(o,(function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{attrs:{id:"newproductcq"}},[t._m(0),t._v(" "),e("div",{staticClass:"checktab"},[e("div",{staticClass:"tabs-box",class:{tabfixed:t.isfixed},on:{click:t.changetabs}},[e("ul",{staticClass:"tab-scroll"},t._l(t.tabData,(function(a,s){return e("li",{directives:[{name:"tracking",rawName:"v-tracking",value:{eventName:"h5_check_tab",params:{tabPage:"newproductcq_tab",tabName:a.title}},expression:"{eventName:'h5_check_tab',params:{tabPage: 'newproductcq_tab',tabName:item.title}}"}],key:s,staticClass:"tab-item",class:{cur:t.iscur==s},attrs:{tabitem:s}},[t._v("\n          "+t._s(a.title)+"\n          "),e("i")])})),0)])]),t._v(" "),e("div",{staticClass:"temprow-box"},[e("img",{attrs:{src:t.imgSrc[t.imgNumber],alt:""}}),t._v(" "),e("temprow",{attrs:{"goods-data":t.temprowData,from_tab:t.cur_tab,"license-status":t.licenseStatus||0}}),t._v(" "),e("div",{directives:[{name:"show",rawName:"v-show",value:t.isload,expression:"isload"}],staticClass:"loaing"},[t._v(t._s(t.loadingmsg))])],1),t._v(" "),e("div",{directives:[{name:"show",rawName:"v-show",value:t.showBtn,expression:"showBtn"}],staticClass:"highmargin-btn-box"},[e("div",{staticClass:"highmargin-btn",on:{click:t.skipNexTab}},[t._v("点击跳转至“"+t._s(t.skiptext)+"”")])])])}),s,!1,null,"8cf0ed28",null).exports}}]);
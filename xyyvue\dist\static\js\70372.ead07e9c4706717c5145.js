(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[70372],{76091:function(t,a,i){var s=i(47293),e=i(81361);t.exports=function(t){return s((function(){return!!e[t]()||"​᠎"!="​᠎"[t]()||e[t].name!==t}))}},73210:function(t,a,i){"use strict";var s=i(82109),e=i(53111).trim;s({target:"String",proto:!0,forced:i(76091)("trim")},{trim:function(){return e(this)}})},33292:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEIAAABCCAYAAADjVADoAAAKSklEQVR4Xu2beZAcdRXHv6+nZ9hNdpMYQCSEsJIh090Jm2R6l4iKJB4omiogcmiJIaAcymUACUQr4VBK0UJESIxRMSIlEAxqUQIekKCgyaY3sBW6e1NDEsMSRQrJLmGP6Zl+1m92ZulpZ3d6ruxS2d8/Se38jvc+/XvH7/26CeMtQ4BGkQNpWutc102dxixJoZC0xTTbOgC4oyHTqIFQ1ZY5gLuJGTNoUIp9RPRF0zS2HlYgFEV/kgif9CrNjKdt2/jYYQVCVeMHAZqYrzS/bVntDYcZCJ0LKWxZxqiY66gsKgCo6mEMIhpdMCkUSs4CMI1I2kSEkM9HpIlwruu6XQMDnNi794UDh8pMDsGO0MOaRme6Li8hgg5gMoAJzDyVKBsvstoyMxPRmwDeBtAD8A4iaWMyeeCpRCIxUEsotQIRam5uPjaVCl/AjCsBvL9CJfYx48eRiPNAR8fUfwObUxXO93/Dqw4iFmuJEWEpEZ/PzCcSkVQNoZnZJaLdAG8i4g2mucOsxry5OaoJIqRp8c8xYyVAwg/I1RTUM5fYDbuJ+DbTbH8YQFV2R1VATJ9+an1Dw8CFRHS3sH8/AGakifgtZnqTiP8G0LNE1CHLyb29vcd1RyL/ZFmWp/T3H3GCLGMOc3oRQB8EeCozTfI71ez8vQBWdndj/f79hvh/Ra1iEJoWjzLTTQCWAflRICtZAsBDrsvPhEL920zTPBhE4ubm5onJZKiFSPo4M84lglJgXBrgXxPhVtNsF+uU3SoCMWuWfpQk4WEifMRvCszoJuJ1qZS0Lhzu7TJNM1mOlJqmRVw3Mo0odAkzriHKRJ2hJnYbgC2yLC3bubPtlXLWEGPKBiHMobHRuR/gC/IFyzg103Xdizs7d2wvV7BC42Kx+S2SFFrLzPMLmMtj3d24sFwzKQuESIwikdS3mHG1T+C3iejBZDJ5eyLR0VVNCLm5YrH500IhupmZLgbgO6vgp44jX59IbO0pde1yQIRUNS4A3A7Q0AGJGf1E+LbjyPeUI0gpggv/4Tjh5QCEbxqCMSgD32xZ7feWGk1KBqFpp0aZk1tEmpwTfjDGS49MmMBLDcNwSlGq3L66rod7e7GWGct8ZrKfSPqMaba9UMrcJYEQjou5/hEAZ3n9FYCNjiNfWuud4FcsFos1SlLjGoC/kO/v+AnH6TmnlLS8RBDx85npwfwIwc8T4aJKw1cpT8/bNxptnh4Oyw8P5h1DLQXQJZa1/YGg8wYGoWlaA3P9nwB8wBO8BohwrWm2rwu6YC36aVr8cma6y5fMmUR9C4LmLYFBKEp8CRGtBzDVo4zhOO6ZicSO12uhYNA5o9H5R4fD9ChAIp/JNj7IjItsu31TkHkCgtDDqorvMfPV3kMUkXuWae74fZCFKu0TjUaPiEQmX+W6WExEzznOgTsTicRQmNS0+KeY6QmPAxdH+nscp3tFEF8RCISmaVNdt+5JImr1KPSCZRnzK1UwyPhBCJNuYqbVWafoAPRNy9r+fW/5X1Vb2gBu8cBoS6VSS4LkNIFAKMops4hShjdvAHClZRlrgihSSZ/ByDBxBZA5z3grWhuI+q7y+gBVbVkK8AavebguLwqS4QYCkV3gF54QdcB1+fTOznZxIVPLFtI0fTkzr/Y9hBQzVtm2cSeQOWtkmqLMayIK7QAwJecosg9sbTEhA4HQNP0eXzq9LZ1OLdm168VXiy1Qwe80e/YpZ7huWuQtk7y2D0jPp1Lpc/xOWpgwc73wE6d41l1vWcZlxeQIBEJV45sBOt0z2ca+vvRlNSyuSprWKoq49xHhKM+64grgCcdxLi9k99Om6RMmT4bYued5zON5y2r/UJVA6HsANHmeyr2pVM8NI3njpqaFdXV1PQJeeOJEetowghdPVLXl0wCL7TwjXwHeQoQvD5+8LZQVpecHRHSVZ9w+yzJOqBYIUVnO2Z2Y87uWZXzDa5/ehZqamurq64+8V5wDABHF8CCzvNy2t71RTKCZM+PRSIR+C2C2r+8+otR5pvnithHmkFRVvwPAinceGrpt2/DKXnB4QNPQRV1wyGMz4zbbNkQoK9hmzZp7XCgkC0eaS75SzPTzgwfDX+vq+nvfcOMURT8Z4J/5wrTo/rLr4tLOTuOZYiAVRb+VCKs8/dKWZRStnwYFkXc9x8y32Hb7rcMJpar6sQAMAOLfXEuKkrxtG9cV2kmqOu8kQN4A8KneeZnRJUnSMtNs+0sxCOJ3RYmvJqJbvH2DXCPWBIQ4pQL1d4jSmvARHqF6AbrGsrYLhzYU9mbMOPk9DQ3h9cxYAuRd+vQQsTjL/DLoexNjCoRQXNNa38fsioPQ5/22zsxX2nb74+LvTU3zptTVyXcBvDS/rsCi2rWyvh5rS6lxjDkQQkld14/q7eXf5B+GMs+8K5VyF9fVpROpVFg41Qt9R/sBIrrRNLEWKK3QMyZBCBhz5849LpmUn2JmzXfXKULyUwBfDNARnl0jssa7e3qwupxC7JgFIdJyRdE/QYSfACgaz5npfkD6epBQW8h5jmUQQt6QprUsBvhR5pGuAvmhvj73K5VkrGMdRObhaVr8MtelHxKhLj9EZu5C/tzfP7B0z56drwUJk8PnImMofA4npMg46+qOXE6EG3LJVvZe9I9E4RtMc2vFN9zvih0hAE2fPr2+oeHoeZJEZzBLkyUJz/X3J//68ssd/6lkJ+TGvmtAVEPZkeYYB5GlMw5iHES+oYzviPEdMb4jCgaPqpjGMcc0T5wyRT5bkuhEZh56LdBf6GDGZoA31zoUljc/LSTCQl/mOlSoISLXdXm349Dju3cb3bl+eYUZRWldBbjXA9zofyu2PKHG3ijxdi9AbwG4z7aNlcOAiL9GRO8de+JXXyJmft2224d09e2I+GaivPuL6kswRmZkxnO2bXy44I6IxRY0E6XWEGFBDd+cHVUU2dcR/+G6dN2uXduHrgYCFW/LlVxV9WcBnOYZv6+v743Y3r17+0eaM3tK3UaEk3P9mNFh28bccmUpNq6mIBRF/xURxPtNmSbsUpJCi0yz7aWRBNO01tnM6ccBGrpdA/A7yzLOLqZQub/XFISq6l8V3tkjnMNMt9h2w50jfGoQUpSWFYC7iuidWiYzrrdtQ1TFa9JqCkLceEmSnPBVpF4B5PMta2tbgYue0Jw58dnpNIlS//GenTQgy6GTKnnFuBi9moIQi6uq/h0AN3pf/2PmVwFpvSTRoz098m5Z/hdPmHD0TNfls4j4Ci+EQYvCj2zbuLaYMpX8XnMQM2e2Hh+JuH8AMMcnaBLg/cyZ5AZE3MiMY73mkO2/03Xp3M7O7Z2VKFpsbM1BCAE0TT9PPFUAxxQTyPf7f4lwhWkaG0scV3L3QwICWCir6luLxeUxETUH+CpAXDq/RITbTLPxsVp8w+UndYhADC4bjeozZTnzzcWXCrxZnwuxAwCtY6Y1tTYHL4xDCiK3sIgmRPJnJQkfJUITc+a7kT3MeFaWpY21jA7D2cyogCjZgA/BgHEQWcj/A6nj0H/qIYpjAAAAAElFTkSuQmCC"},55880:function(t,a,i){"use strict";i.d(a,{Z:function(){return c}});i(91058),i(47042),i(41539),i(68309),i(91038),i(78783),i(82526),i(41817),i(32165),i(66992),i(33948),i(82772),i(9653);var s=i(59502),e=i(67087);function r(t,a){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!i){if(Array.isArray(t)||(i=function(t,a){if(!t)return;if("string"==typeof t)return o(t,a);var i=Object.prototype.toString.call(t).slice(8,-1);"Object"===i&&t.constructor&&(i=t.constructor.name);if("Map"===i||"Set"===i)return Array.from(t);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return o(t,a)}(t))||a&&t&&"number"==typeof t.length){i&&(t=i);var s=0,e=function(){};return{s:e,n:function(){return s>=t.length?{done:!0}:{done:!1,value:t[s++]}},e:function(t){throw t},f:e}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,n=!0,c=!1;return{s:function(){i=i.call(t)},n:function(){var t=i.next();return n=t.done,t},e:function(t){c=!0,r=t},f:function(){try{n||null==i.return||i.return()}finally{if(c)throw r}}}}function o(t,a){(null==a||a>t.length)&&(a=t.length);for(var i=0,s=new Array(a);i<a;i++)s[i]=t[i];return s}var n={computed:{isExtend:function(){return 0!=this.productValue}},mounted:function(){var t=this;s.Z.$on("listenToChildEvent",(function(a){var i=a.isExtend,s=a.dataId;parseInt(s)===parseInt(t.goodsId)&&(i||(t.productValue=0))})),s.Z.$on("android_response",(function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,i=arguments.length>1?arguments[1]:void 0;i===t.dataId&&(t.productValue=parseInt(a))})),s.Z.$on("update_cart",(function(a){var i,s=a.length,e=0,o=r(a);try{for(o.s();!(i=o.n()).done;){var n=i.value;if(n.item.id===t.goodsId){t.productValue=n.item.amount;break}e++}}catch(t){o.e(t)}finally{o.f()}s===e&&(t.productValue=0)}))},watch:{isExtend:function(){s.Z.$emit("cart_isExtend",this.isExtend,this.goodsId)}},mixins:[{props:["dataId","productNum","isSplit","medNum","isPack","bgcolor"],data:function(){return{goodsId:0,issplit:0,productValue:0,mednum:0,ispack:!1}},watch:{dataId:function(t){this.goodsId=t,this.productValue=this.productNum?this.productNum:0,this.issplit=this.isSplit,this.mednum=this.medNum,this.ispack=this.isPack}},methods:{addProductCart:function(t){if("add"===t)this.productValue+=this.mednum;else{if("min"!==t)return;this.productValue>0&&(this.productValue=1==this.issplit?this.productValue-1:this.productValue-this.mednum)}this.productValue=this.productValue>0?this.productValue:0,this.postCartData(this.productValue),s.Z.$emit("listenToChildEvent",{isExtend:this.isExtend,dataId:this.goodsId})},inputCart:function(t){var a=parseInt(t.target.value);a=a>0?a:0,this.productValue=a,this.postCartData(this.productValue),s.Z.$emit("listenToChildEvent",{isExtend:this.isExtend,dataId:this.goodsId})},androidclick:function(t){if(navigator.userAgent.indexOf("Android")>=0){t.target.setAttribute("readonly",!0),t.target.blur();var a=parseInt(t.target.value);this.productValue=a,s.Z.$emit("showandorideditcomp",{id:this.goodsId,val:a,showandoridedit:!0,split:this.issplit,medpack:this.mednum,package:this.ispack})}},postCartData:function(t){var a=this;if(t>0){var i=1==this.ispack?{merchantId:this.merchantId,amount:t,packageId:this.goodsId}:{merchantId:this.merchantId,amount:t,skuId:this.goodsId};this.putRequest("post","/app/changeCart",i).then((function(i){if("success"==i.data.status){i.data.data.qty!=t&&(a.productValue=parseInt(i.data.data.qty)),Number(i.data.data.qty)&&(0,e.M0)("h5_page_CommodityDetails_o",{commodityId:a.goodsId,real:1}),null!=i.data.dialog&&(20==i.data.dialog.style?s.Z.$emit("changesureDialog",{dialogmsg:i.data.dialog.msg,showsureDialog:!0}):s.Z.$emit("changeprompt",{dialog:i.data.dialog.msg,showprompt:!0})),i.errorMsg&&s.Z.$emit("changeprompt",{dialog:i.errorMsg,showprompt:!0});try{var r=1==a.ispack?{proid:a.goodsId,pronum:a.productValue,isAdd:1,type:1}:{proid:a.goodsId,pronum:a.productValue,isAdd:1};window.webkit.messageHandlers.addPlanNumber.postMessage(r)}catch(t){}try{1==a.ispack?window.hybrid.addPlanNumber(a.goodsId,a.productValue,1,1):window.hybrid.addPlanNumber(a.goodsId,a.productValue,1)}catch(t){}}else a.productValue=0,i.data.errorMsg?s.Z.$emit("changeprompt",{dialog:i.data.errorMsg,showprompt:!0}):i.data.msg?s.Z.$emit("changesureDialog",{dialogmsg:i.data.msg,showsureDialog:!0}):s.Z.$emit("changeprompt",{dialog:i.data.dialog.msg,showprompt:!0}),s.Z.$emit("listenToChildEvent",{isExtend:a.isExtend,dataId:a.goodsId})})).catch((function(t){}))}else{var r=1==this.ispack?{merchantId:this.merchantId,packageIds:this.goodsId}:{merchantId:this.merchantId,ids:this.goodsId};this.putRequest("post","/app/batchRemoveProductFromCart",r).then((function(t){if("success"==t.data.status){a.productValue=0;try{var i=1==a.ispack?{proid:a.goodsId,pronum:0,isAdd:1,type:1}:{proid:a.goodsId,pronum:0,isAdd:1};window.webkit.messageHandlers.addPlanNumber.postMessage(i)}catch(t){}try{1==a.ispack?window.hybrid.addPlanNumber(a.goodsId,0,1,1):window.hybrid.addPlanNumber(a.goodsId,0,1)}catch(t){}}}))}}},created:function(){this.goodsId=this.dataId,this.productValue=this.productNum?this.productNum:0,this.issplit=this.isSplit,this.mednum=this.medNum,this.ispack=this.isPack}}]},c=(0,i(51900).Z)(n,(function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("div",{staticClass:"btn-container"},[i("div",{directives:[{name:"show",rawName:"v-show",value:!t.isExtend,expression:"!isExtend"}],staticClass:"plus-container",on:{click:function(a){return a.stopPropagation(),a.preventDefault(),t.addProductCart("add")}}}),t._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:t.isExtend,expression:"isExtend"}],staticClass:"spread-add"},[i("div",{staticClass:"reduce",on:{click:function(a){return a.stopPropagation(),a.preventDefault(),t.addProductCart("min")}}}),t._v(" "),i("div",{staticClass:"cont"},[i("input",{staticClass:"input-val",class:"input-val"+t.goodsId,attrs:{type:"tel"},domProps:{value:t.productValue},on:{change:t.inputCart,click:function(a){return a.preventDefault(),a.stopPropagation(),t.androidclick.apply(null,arguments)}}})]),t._v(" "),i("div",{staticClass:"plus",on:{click:function(a){return a.stopPropagation(),a.preventDefault(),t.addProductCart("add")}}})])])}),[],!1,null,"3c1b85ad",null).exports},99194:function(t,a,i){"use strict";i.d(a,{Z:function(){return e}});i(56977),i(54678);var s={filters:{fixedtwo:function(t){return parseFloat(t).toFixed(2)}},props:["dataId","uniformPrice","suggestPrice","grossMargin","status"]},e=(0,i(51900).Z)(s,(function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("div",{staticClass:"priceBox"},[t.uniformPrice?i("span",[t._v("\n        控销价¥"+t._s(t._f("fixedtwo")(t.uniformPrice))+"\n    ")]):t._e(),t._v(" "),t.suggestPrice?i("span",[t._v("\n        零售价¥"+t._s(t._f("fixedtwo")(t.suggestPrice))+"\n    ")]):t._e(),t._v(" "),t.grossMargin&&2!=t.status?i("span",[t._v("\n        (毛利率"+t._s(t.grossMargin)+")\n    ")]):t._e()])}),[],!1,null,"98f040f6",null).exports},70372:function(t,a,i){"use strict";i.d(a,{Z:function(){return c}});i(9653),i(56977),i(54678),i(74916),i(15306),i(73210),i(54747),i(47042),i(69600);var s=i(55880),e=i(99194),r=i(67087),o=i(59502),n={props:{goodsData:Array,licenseStatus:Number,trackingStatus:{type:Boolean,default:!0}},data:function(){return{datalist:[],imgUrl:""}},filters:{fixedtwo:function(t){return parseFloat(t).toFixed(2)},replace:function(t){return t.replace(/-/g,".")},trim:function(t){return t.trim()}},components:{cartBtn:s.Z,priceBox:e.Z},methods:{getIdList:function(t){var a=[];t&&t.length>0?(t.forEach((function(t,i){a.push(t.id)})),this.getPrice(t,a)):this.datalist=t},splitData:function(t){for(var a=[],i=0,s=t.length;i<s;i+=20)a.push(t.slice(i,i+20));return a},getPrice:function(t,a){var i=this,s=this.splitData(a),e=0;s.forEach((function(a,r){i.putRequest("post","/app/marketing/discount/satisfactoryInHandPrice",{merchantId:i.merchantId,skuIds:a.join(",")}).then((function(a){if(i.$nextTick((function(){o.Z.$emit("changeloading",!1)})),"success"==a.data.status){var r=a.data.data;r&&r.length>0&&r.forEach((function(a,i){t.forEach((function(t,i){a.skuId==t.id&&(t.zheHouPrice=a.price)}))}))}++e===s.length&&t&&t.length>0&&(i.datalist=[],t.forEach((function(t,a){i.datalist.push(t)})))})).catch((function(a){i.datalist=t}))}))}},mounted:function(){var t=this.goodsData;this.getIdList(t),this.imgUrl=this.imgBaseUrl,(0,r.dA)("h5_page_ListPage_ExpStatic",{list:t},"",this.trackingStatus)},computed:{detailUrl:function(){return o.Z.detailBaseUrl}},watch:{goodsData:function(t,a){this.getIdList(t),(0,r.dA)("h5_page_ListPage_ExpStatic",{list:t},"",this.trackingStatus)}}},c=(0,i(51900).Z)(n,(function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("ul",{staticClass:"templist"},t._l(t.datalist,(function(a,e){return s("li",{key:e,staticClass:"templist-item"},[s("a",{staticClass:"images",attrs:{href:t.detailUrl+"product_id="+a.id}},[2===a.status?s("div",{staticClass:"sold-out"},[t._v("售罄")]):t._e(),t._v(" "),s("div",[s("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.imgUrl+"/ybm/product/min/"+a.imageUrl,expression:"imgUrl + '/ybm/product/min/' + item.imageUrl"}],staticClass:"pic",attrs:{alt:a.commonName}}),t._v(" "),a.markerUrl&&!a.reducePrice?s("img",{staticClass:"activity-token",attrs:{src:t.imgUrl+a.markerUrl,alt:""}}):t._e(),t._v(" "),a.markerUrl&&a.reducePrice&&(1!=a.isControl||1==a.isPurchase&&1==a.isControl)?s("div",{staticClass:"mark-text"},[s("img",{attrs:{src:t.imgUrl+a.markerUrl,alt:"药采节价"}}),t._v(" "),s("h4",[t._v("药采节价："+t._s(t._f("fixedtwo")(a.reducePrice)))])]):t._e(),t._v(" "),a.promotionSkuImageUrl&&(1!=a.isControl||1==a.isPurchase&&1==a.isControl)?s("div",{staticClass:"promotionSkuPrice"},[s("img",{attrs:{src:t.imgUrl+a.promotionSkuImageUrl,alt:""}}),t._v(" "),s("span",[t._v("￥"+t._s(t._f("fixedtwo")(a.promotionSkuPrice)))])]):t._e(),t._v(" "),a.activityTag&&a.activityTag.tagNoteBackGroupUrl?s("div",{staticClass:"active-tags"},[s("img",{attrs:{src:t.imgUrl+a.activityTag.tagNoteBackGroupUrl,alt:""}}),t._v(" "),s("div",{staticClass:"tejia806"},[s("span",{staticClass:"discount"},[t._v("\n              "+t._s(a.activityTag.timeStr)+"\n            ")]),t._v(" "),s("div",{staticClass:"labelBox"},t._l(a.activityTag.skuTagNotes,(function(a,i){return s("span",{key:i,staticClass:"price806",style:{color:"#"+a.textColor}},[t._v(t._s(a.text))])})),0)])]):t._e(),t._v(" "),a.activityTag&&a.activityTag.tagNoteBackGroupUrl&&2==+a.activityTag.sourceType?s("div",{staticClass:"active-tags shop-biaoqian"},[s("img",{attrs:{src:a.activityTag.tagNoteBackGroupUrl,alt:""}}),t._v(" "),a.activityTag.customTopNote?s("div",{staticClass:"customTopNote"},[t._v(t._s(a.activityTag.customTopNote))]):t._e(),t._v(" "),s("div",[s("span",{staticClass:"shop-discount"},[t._v(" "+t._s(a.activityTag.timeStr)+" ")]),t._v(" "),t._l(a.activityTag.skuTagNotes,(function(a,i){return s("span",{key:i,staticClass:"shop-text"},[t._v(t._s(a.text))])}))],2),t._v(" "),a.activityTag.customBottomNote?s("div",{staticClass:"customBottomNote"},[t._v(t._s(a.activityTag.customBottomNote))]):t._e()]):t._e()])]),t._v(" "),s("div",{staticClass:"info"},[s("a",{attrs:{href:t.detailUrl+"product_id="+a.id}},[s("div",{staticClass:"commonName"},[1===a.agent?s("span",{staticClass:"dujia"},[t._v("独家")]):t._e(),t._v(" "),s("span",{staticClass:"name"},[t._v(t._s(t._f("trim")(a.commonName)))]),t._v(" "),s("span",{staticClass:"spec"},[t._v("/"+t._s(a.spec))])]),t._v(" "),s("div",{staticClass:"factory"},[s("div"),t._v(" "),s("div",{staticClass:"name"},[t._v(t._s(a.manufacturer))])]),t._v(" "),s("div",{staticClass:"date"},[s("div"),t._v(" "),s("div",{staticClass:"detail"},[a.nearEffect?s("span",{staticClass:"date-text"},[t._v(t._s(t._f("replace")(a.nearEffect))+"/"+t._s(t._f("replace")(a.farEffect)))]):t._e(),t._v(" "),a.mediumPackageTitle?s("span",{staticClass:"PackInfo"},[t._v(t._s(a.mediumPackageTitle))]):t._e()])])]),t._v(" "),s("div",{staticClass:"price-add-wrap"},[s("div",{staticClass:"price-wrap"},[s("div",{staticClass:"price-container"},[1===t.licenseStatus||5===t.licenseStatus?s("div",{staticClass:"qualifications"},[t._v("\n              价格认证资质可见\n            ")]):1!=a.isPurchase&&1==a.isControl?s("div",{staticClass:"nobuy"},[t._v("\n              暂无购买权限\n            ")]):s("div",{staticClass:"price-numer"},["true"==a.isOEM&&0==a.signStatus||0==a.showAgree?s("span",{staticClass:"price-permission"},[t._v("价格签署协议可见")]):2==a.priceType&&a.skuPriceRangeList?s("i",[t._v("\n                ￥"+t._s(t._f("fixedtwo")(a.skuPriceRangeList[0].price))+"~"+t._s(t._f("fixedtwo")(a.skuPriceRangeList[a.skuPriceRangeList.length-1].price))+"\n              ")]):s("div",{staticClass:"pricewapper"},[s("div",{staticClass:"price-box clearfixed"},[s("div",{staticClass:"price-two"},[s("p",[s("span",[t._v("￥"+t._s(t._f("fixedtwo")(a.fob)))]),t._v(" "),a.zheHouPrice?s("span",{staticClass:"zhekou"},[t._v(t._s(a.zheHouPrice))]):t._e()])])])])])])]),t._v(" "),s("div",{staticClass:"inputNumber"},[2===a.status?s("div",{staticClass:"no-goods"},[s("img",{attrs:{src:i(33292),alt:""}})]):s("div",["true"==a.isOEM&&0==a.signStatus||0==a.showAgree?s("div"):s("div",[!0===a.isPurchase||1!==a.isControl||1!==t.licenseStatus&&5!==t.licenseStatus?s("div",[1!=a.isControl||1==a.isPurchase&&1==a.isControl?s("cartBtn",{attrs:{"is-pack":!1,"data-id":a.id,"is-split":a.isSplit,"product-num":a.cartProductNum,"med-num":a.mediumPackageNum}}):t._e()],1):t._e()])])])]),t._v(" "),s("div",{staticClass:"control-hid"},[1!==t.licenseStatus&&5!==t.licenseStatus?s("div",{staticClass:"control-box"},["true"!=a.isOEM&&(1!=a.isControl||1==a.isPurchase&&1==a.isControl)||"true"==a.isOEM&&1==a.signStatus?s("priceBox",{attrs:{uniformPrice:a.uniformPrice,suggestPrice:a.suggestPrice,grossMargin:a.grossMargin}}):t._e()],1):t._e()]),t._v(" "),s("div",{staticClass:"label-box",class:{"label-box-hide":a.tagList.length<=0&&!(1==a.isUsableMedicalStr)}},[s("div",{staticClass:"labels"},t._l(a.tagList.slice(0,3),(function(a,i){return s("span",{key:i,class:"span"+a.uiType},[t._v(t._s(a.name))])})),0)])])])})),0)}),[],!1,null,"727732e4",null).exports}}]);
(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[96195],{61255:function(t,a,s){"use strict";s(91058),s(82772),s(9653);var i=s(59502),d=s(67087);a.Z={props:["dataId","productNum","isSplit","medNum","isPack","bgcolor","btncolor"],data:function(){return{goodsId:"",issplit:"",productValue:"",mednum:"",ispack:!1}},watch:{dataId:function(t){this.goodsId=t,this.productValue=this.productNum?this.productNum:0,this.issplit=this.isSplit,this.mednum=this.medNum,this.ispack=this.isPack},isExtend:function(){i.Z.$emit("cart_isExtend",this.isExtend,this.goodsId)}},methods:{addProductCart:function(t){if(this.btn_hidden)this.postCartData(this.mednum);else{var a=t.target.getAttribute("edit-cate"),s=parseInt(t.currentTarget.children[1].value),d=parseInt(this.mednum);if("add"==a)s+=d;else{if("min"!=a)return;s=1==this.issplit?s-1:s-d}s=s>0?s:0,this.productValue=s,i.Z.$emit("listenToChildEvent",{isExtend:this.isExtend,dataId:this.goodsId,productValue:this.productValue}),this.postCartData(s)}},inputCart:function(t){var a=parseInt(t.target.value);a=a>=0?a:0,this.productValue=a,i.Z.$emit("listenToChildEvent",{isExtend:this.isExtend,dataId:this.goodsId,productValue:this.productValue}),this.postCartData(t.target.value)},androidclick:function(t){if(navigator.userAgent.indexOf("Android")>=0){t.target.setAttribute("readonly",!0),t.target.blur();var a=t.target.value;i.Z.$emit("showandorideditcomp",{id:this.goodsId,val:a,showandoridedit:!0,split:this.issplit,medpack:this.mednum,package:this.ispack})}},postCartData:function(t){var a=this;if(t>0){var s=1==this.ispack?{merchantId:this.merchantId,amount:t,packageId:this.goodsId}:{merchantId:this.merchantId,amount:t,skuId:this.goodsId};this.putRequest("post","/app/changeCart",s).then((function(s){if("success"===s.data.status){a.btn_hidden&&i.Z.$emit("changeprompt",{dialog:"已添加到购物车!",showprompt:!0}),Number(s.data.data.qty)&&(0,d.M0)("h5_page_CommodityDetails_o",{commodityId:a.goodsId,real:1}),s.data.data.qty!=t&&(a.productValue=s.data.data.qty),null!=s.data.dialog&&(20==s.data.dialog.style?i.Z.$emit("changesureDialog",{dialogmsg:s.data.dialog.msg,showsureDialog:!0}):i.Z.$emit("changeprompt",{dialog:s.data.dialog.msg,showprompt:!0})),s.errorMsg&&i.Z.$emit("changeprompt",{dialog:s.errorMsg,showprompt:!0});try{var e=1==a.ispack?{proid:a.goodsId,pronum:a.productValue,isAdd:1,type:1}:{proid:a.goodsId,pronum:a.productValue,isAdd:1};window.webkit.messageHandlers.addPlanNumber.postMessage(e)}catch(t){}try{1==a.ispack?window.hybrid.addPlanNumber(a.goodsId,a.productValue,1,1):window.hybrid.addPlanNumber(a.goodsId,a.productValue,1)}catch(t){}}else a.productValue=0,i.Z.$emit("listenToChildEvent",{isExtend:a.isExtend,dataId:a.goodsId,productValue:a.productValue}),s.data.errorMsg?i.Z.$emit("changeprompt",{dialog:s.data.errorMsg,showprompt:!0}):s.data.msg?i.Z.$emit("changesureDialog",{dialogmsg:s.data.msg,showsureDialog:!0}):i.Z.$emit("changeprompt",{dialog:s.data.dialog.msg,showprompt:!0})})).catch((function(t){}))}else{var e=1==this.ispack?{merchantId:this.merchantId,packageIds:this.goodsId}:{merchantId:this.merchantId,ids:this.goodsId};this.putRequest("post","/app/batchRemoveProductFromCart",e).then((function(t){if("success"==t.data.status){a.btn_hidden&&i.Z.$emit("changeprompt",{dialog:"已添从购物车删除!",showprompt:!0}),a.productValue=0;try{var s=1==a.ispack?{proid:a.goodsId,pronum:0,isAdd:1,type:1}:{proid:a.goodsId,pronum:0,isAdd:1};window.webkit.messageHandlers.addPlanNumber.postMessage(s)}catch(t){}try{1==a.ispack?window.hybrid.addPlanNumber(a.goodsId,0,1,1):window.hybrid.addPlanNumber(a.goodsId,0,1)}catch(t){}}}))}}},created:function(){this.goodsId=this.dataId,this.productValue=this.productNum?this.productNum:0,this.issplit=this.isSplit,this.mednum=this.medNum,this.ispack=this.isPack}}},64400:function(t,a,s){t.exports=s.p+"static/img/app_01.9e8c80f.png"},55267:function(t,a,s){t.exports=s.p+"static/img/app_04.9d802ed.png"},15966:function(t,a,s){t.exports=s.p+"static/img/app_05.d1c204b.png"},40621:function(t,a,s){t.exports=s.p+"static/img/banner.828e120.png"},88264:function(t,a,s){"use strict";s.d(a,{Z:function(){return d}});var i={mixins:[s(61255).Z]},d=(0,s(51900).Z)(i,(function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"green-btn",attrs:{"data-id":t.goodsId,"is-slipt":t.issplit,"med-pack-num":t.mednum},on:{click:function(a){return a.preventDefault(),t.addProductCart.apply(null,arguments)}}},[s("span",{staticClass:"min",attrs:{"edit-cate":"min"}},[t._v("-")]),t._v(" "),s("input",{directives:[{name:"model",rawName:"v-model",value:t.productValue,expression:"productValue"}],staticClass:"input-val",class:"input-val"+t.goodsId,attrs:{"edit-cate":"edit",type:"tel"},domProps:{value:t.productValue},on:{change:t.inputCart,click:function(a){return a.preventDefault(),t.androidclick.apply(null,arguments)},input:function(a){a.target.composing||(t.productValue=a.target.value)}}}),t._v(" "),s("span",{staticClass:"add",attrs:{"edit-cate":"add"}},[t._v("+")])])}),[],!1,null,"5d221bb3",null).exports},54706:function(t,a,s){"use strict";s.r(a),s.d(a,{default:function(){return o}});var i=[function(){var t=this.$createElement,a=this._self._c||t;return a("div",{staticClass:"banner"},[a("img",{attrs:{src:s(64400),alt:""}})])},function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("div",{staticClass:"hot"},[i("img",{attrs:{src:s(55267),alt:""}}),t._v(" "),i("img",{attrs:{src:s(15966),alt:""}})])}],d=s(88264),e=(s(59502),{data:function(){return{price1:"12",price2:"13.95"}},components:{greenBtn:d.Z},activated:function(){this.setAppTitle("吴太感康")}}),o=(0,s(51900).Z)(e,(function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("div",{staticClass:"wutaipagehn"},[t._m(0),t._v(" "),i("div",{staticClass:"goodsdata"},[i("img",{attrs:{src:s(40621),alt:""}}),t._v(" "),i("div",{staticClass:"goodsdata-in"},[i("a",{staticClass:"goodslist",attrs:{href:"ybmpage://productdetail?product_id=55562"}},[i("greenBtn",{staticClass:"prolistA",attrs:{"data-id":55562,"is-pack":!1,"is-split":1,"product-num":0,"med-num":10}})],1),t._v(" "),i("a",{staticClass:"goodslist",attrs:{href:"ybmpage://productdetail?product_id=55151"}},[i("greenBtn",{staticClass:"prolistB",attrs:{"data-id":55151,"is-pack":!1,"is-split":1,"product-num":0,"med-num":5}})],1)])]),t._v(" "),t._m(1)])}),i,!1,null,"173ff5b6",null).exports}}]);
(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[89945],{13620:function(t,a,e){t.exports=e.p+"static/img/passionate.741bfc0.jpg"},47084:function(t,a,e){t.exports=e.p+"static/img/voucher1.d09fb23.jpg"},94179:function(t,a,e){t.exports=e.p+"static/img/voucher2.ef98bf8.jpg"},91046:function(t,a,e){"use strict";e.r(a),e.d(a,{default:function(){return n}});var s=[function(){var t=this.$createElement,a=this._self._c||t;return a("div",{staticClass:"banner"},[a("img",{attrs:{src:e(13620),alt:""}})])}],i=e(24388),c=e(59502),o={data:function(){return{isCur:"plan-content",voucher1:!1,voucher2:!1,istabfixed:!1,planData:[],healthData:[],girlData:[]}},methods:{getVouhcer:function(t){this.putRequest("post","/app/voucher/receiveVoucher",{merchantId:this.merchantId,voucherTemplateId:t}).then((function(t){"failure"==t.data.status&&9996==t.data.code?c.Z.$emit("changeprompt",{dialog:"您已领取过此券，用完可继续领取",showprompt:!0}):c.Z.$emit("changeprompt",{dialog:t.data.msg,showprompt:!0})})).catch((function(t){}))},getDataList:function(t,a){var e=this;this.putRequest("post","/app/layout/initExhibitionModulePage?sort=1",{merchantId:this.merchantId,limit:1e3,offset:0,exhibitionId:t}).then((function(t){"success"==t.data.status&&(e[a]=t.data.data.rows,e[a].licenseStatus=t.data.data.licenseStatus),e.$nextTick((function(){c.Z.$emit("changeloading",!1)}))})).catch((function(t){}))},moveEvent:function(){var t=document.querySelector("#app").scrollTop,a=document.querySelector(".tabs-box").offsetTop,e=document.querySelector(".tabs-box").offsetHeight;this.istabfixed=t>=a;var s=this.$refs.plan.offsetTop,i=this.$refs.health.offsetTop,c=this.$refs.girl.offsetTop;t>=s-e&&t<i-e?this.isCur="plan-content":t>=i-e&&t<c-e?this.isCur="health-content":t>=c-e&&(this.isCur="girl-content")},tabitemclick:function(t){this.istabfixed=!0;var a=t.currentTarget.getAttribute("contClass");this.isCur=a;var e=document.querySelector("."+a).offsetTop,s=document.querySelector(".tabs-box").offsetHeight;document.querySelector("#app").scrollTop=e-s;document.querySelector("#app").scrollTop}},mounted:function(){this.getDataList("ZS201808161720321423","planData"),this.getDataList("ZS201808161721161716","healthData"),this.getDataList("ZS201808161721504722","girlData")},components:{temprow:i.Z}},n=(0,e(51900).Z)(o,(function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{attrs:{id:"passionate"},on:{touchmove:t.moveEvent}},[t._m(0),t._v(" "),s("div",{staticClass:"voucher-list"},[s("div",{staticClass:"voucher-item"},[s("div",{on:{click:function(a){return t.getVouhcer(976)}}},[s("img",{attrs:{src:e(47084),alt:""}})])]),t._v(" "),s("div",{staticClass:"voucher-item"},[s("div",{on:{click:function(a){return t.getVouhcer(977)}}},[s("img",{attrs:{src:e(94179),alt:""}})])])]),t._v(" "),s("div",{staticClass:"tabs-title"},[t._v("*限活动页内使用，用完可继续领取")]),t._v(" "),s("div",{staticClass:"tabs-box"},[s("div",{staticClass:"tab-list",class:{tabfiexd:t.istabfixed}},[s("div",{staticClass:"tab-item",class:{tabclickcolor:"plan-content"==t.isCur},attrs:{contClass:"plan-content"},on:{click:t.tabitemclick}},[t._v("计生用品")]),t._v(" "),s("div",{staticClass:"tab-item",class:{tabclickcolor:"health-content"==t.isCur},attrs:{contClass:"health-content"},on:{click:t.tabitemclick}},[t._v("补肾健体")]),t._v(" "),s("div",{staticClass:"tab-item",class:{tabclickcolor:"girl-content"==t.isCur},attrs:{contClass:"girl-content"},on:{click:t.tabitemclick}},[t._v("女性健康")])])]),t._v(" "),s("div",{ref:"plan",staticClass:"plan-content"},[s("temprow",{attrs:{"goods-data":t.planData,"license-status":t.planData.licenseStatus||0}})],1),t._v(" "),s("div",{ref:"health",staticClass:"health-content"},[s("temprow",{attrs:{"goods-data":t.healthData,"license-status":t.healthData.licenseStatus||0}})],1),t._v(" "),s("div",{ref:"girl",staticClass:"girl-content"},[s("temprow",{attrs:{"goods-data":t.girlData,"license-status":t.girlData.licenseStatus||0}})],1)])}),s,!1,null,"29bfae58",null).exports}}]);
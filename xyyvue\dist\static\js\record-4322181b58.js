'use strict';

$(function () {
	var $emptyBox = $('.empty-box'),
	    $record = $('.record');
	api.getUserLuck().then(function (res) {
		if (res.success) {
			setData();
			if (res.data.total == 0 || res.data.total == 1) return;
			// $("#paginator").jqPaginator({
			// 	totalPages:res.data.total ,
			// 	visiblePages: 10,
			// 	currentPage: 1,
			// 	first: '<li class="first"><a href="javascript:void(0);">首页<\/a><\/li>',
			// 	prev: '<li class="prev"><a href="javascript:void(0);">上一页<\/a><\/li>',
			// 	next: '<li class="next"><a href="javascript:void(0);">下一页<\/a><\/li>',
			// 	last: '<li class="last"><a href="javascript:void(0);">末页<\/a><\/li>',
			// 	page: '<li class="page"><a href="javascript:void(0);">{{page}}<\/a><\/li>',
			// 	onPageChange: function (n) {
			// 		setData(n)
			// 	}
			// });
		}
	});
	function setData(pageNum) {
		api.getUserLuck(pageNum).then(function (res) {
			if (res.success) {
				if (res.data.list.length === 0) {
					$record.hide().find('header').hide();
					$emptyBox.fadeIn();
					$record.fadeIn().find('ul').empty();
				} else {
					$emptyBox.hide();
					$record.fadeIn().find('ul').empty();
					var html = "";
					res.data.list.forEach(function (item) {
						var drawPictureUrl = item.drawPictureUrl,
						    mobile = item.mobile,
						    createTime = item.createTime,
						    titile = item.titile;

						var WinningTime = formatDate(createTime);
						html += '<li><div class="left"> <img src="' + lucky.imgUrl + drawPictureUrl + '" alt="levelTitile"></div>\n                                   <div class="right"> <h2>' + titile + '</h2><p>' + mobile + '</p><p>' + WinningTime + '</p></div></li>';
					});
					$record.fadeIn().find('ul').append(html);
				}
			} else {
				console.log(res.msg);
			}
		});
	}
	// 时间戳转换为年月日时分秒
	function add0(m) {
		return m < 10 ? '0' + m : m;
	}
	function formatDate(needTime) {
		//needTime是整数，否则要parseInt转换
		var time = new Date(needTime);
		var y = time.getFullYear();
		var m = time.getMonth() + 1;
		var d = time.getDate();
		var h = time.getHours();
		var mm = time.getMinutes();
		var s = time.getSeconds();
		return y + '.' + add0(m) + '.' + add0(d) + ' ' + add0(h) + ':' + add0(mm) + ':' + add0(s);
	}
});
(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[74957],{40863:function(t,a,e){t.exports=e.p+"static/img/banner_01.c931010.png"},33530:function(t,a,e){t.exports=e.p+"static/img/yiling_03.353257f.png"},13797:function(t,a,e){t.exports=e.p+"static/img/yiling_04.4d0ed39.png"},76587:function(t,a,e){"use strict";e.r(a),e.d(a,{default:function(){return c}});var s=[function(){var t=this.$createElement,a=this._self._c||t;return a("div",{staticClass:"banner"},[a("img",{attrs:{src:e(40863),alt:""}})])}],i=(e(91058),e(24388)),n=e(59502),o={data:function(){return{iscur:0,licenseStatus:0,temprowData:[],tabData:[{hdid:"ZS201909231401464088",title:"8折区"},{hdid:"ZS201909231402133840",title:"7折区"},{hdid:"ZS201909231402392447",title:"6折区"},{hdid:"ZS201909231402533463",title:"≤5折区"}],isfixed:!1,chooseHdid:"ZS201909231401464088",tabIndex:0,scrollload:!0,isload:!1,loadingmsg:"正在加载···",pagecur:0,totalpage:0,skiptext:"7折区",showBtn:!1,imgNumber:0,imgSrc:[e(33530),e(13797)],cur_tab:"8折区"}},methods:{tabitemclick:function(t){document.querySelector("#valuediscountarea").scrollTop=document.querySelector(".checktab").offsetTop,this.translatetabs()},skipNexTab:function(){this.tabIndex++,this.tabIndex=this.tabIndex==this.tabData.length?0:this.tabIndex,this.translatetabs()},moveEvent:function(){var t=document.querySelector(".checktab").offsetTop,a=document.querySelector("#valuediscountarea").scrollTop;this.isfixed=a>=t,a>800&&n.Z.$emit("showBtn",{isShow:!0,dom:"#valuediscountarea"});var e=window.screen.height;document.querySelector("#valuediscountarea").scrollHeight-a<=e&&this.scrollload&&(this.scrollload=!1,this.pagecur>=this.totalpage-1?(this.isload=!1,this.showBtn=!0):(this.isload=!0,this.loadingmsg="正在加载···",this.pagecur++,this.getDatalist(this.chooseHdid,"temprowData")))},changetabs:function(t){this.tabIndex=parseInt(t.target.getAttribute("tabitem")),this.translatetabs(),this.imgNumber=this.tabIndex-0,this.cur_tab=t.target.innerText},translatetabs:function(){this.iscur=this.tabIndex,this.chooseHdid=this.tabData[this.tabIndex].hdid,document.querySelector("#valuediscountarea").scrollTop=document.querySelector(".checktab").offsetTop;var t=this.tabIndex+1;t=t==this.tabData.length?0:t,this.skiptext=this.tabData[t].title,this.pagecur=0,this.showBtn=!1,this.getDatalist(this.chooseHdid,"temprowData"),this.imgNumber=this.tabIndex-0},getDatalist:function(t,a){var e=this;this.putRequest("post","/app/layout/initExhibitionModulePage?sort=1",{merchantId:this.merchantId,limit:10,offset:this.pagecur,exhibitionId:t}).then((function(t){if("success"==t.data.status){t.data.data.rows.length<3&&(e.showBtn=!0),0==e.pagecur&&(e.totalpage=t.data.data.pageCount,e[a]=[]),e.isload=!1,e.scrollload=!0;var s=t.data.data.rows;e[a].push.apply(e[a],s),e.licenseStatus=t.data.data.licenseStatus}e.$nextTick((function(){n.Z.$emit("changeloading",!1)}))})).catch((function(t){}))}},mounted:function(){document.querySelector("#valuediscountarea").addEventListener("scroll",this.moveEvent),this.getDatalist("ZS201909231401464088","temprowData")},components:{temprow:i.Z},activated:function(){this.setAppTitle("超值折扣区")}},c=(0,e(51900).Z)(o,(function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{attrs:{id:"valuediscountarea"}},[t._m(0),t._v(" "),e("div",{staticClass:"checktab"},[e("div",{staticClass:"tabs-box",class:{tabfixed:t.isfixed},on:{click:t.changetabs}},[e("ul",{staticClass:"tab-scroll"},t._l(t.tabData,(function(a,s){return e("li",{directives:[{name:"tracking",rawName:"v-tracking",value:{eventName:"h5_check_tab",params:{tabPage:"valuediscountarea_tab",tabName:a.title}},expression:"{eventName:'h5_check_tab',params:{tabPage: 'valuediscountarea_tab',tabName:item.title}}"}],key:s,staticClass:"tab-item",class:{cur:t.iscur==s},attrs:{tabitem:s}},[t._v("\n          "+t._s(a.title)+"\n          "),e("i")])})),0)])]),t._v(" "),e("div",{staticClass:"temprow-box"},[e("temprow",{attrs:{"goods-data":t.temprowData,from_tab:t.cur_tab,"license-status":t.licenseStatus||0}}),t._v(" "),e("div",{directives:[{name:"show",rawName:"v-show",value:t.isload,expression:"isload"}],staticClass:"loaing"},[t._v(t._s(t.loadingmsg))])],1),t._v(" "),e("div",{directives:[{name:"show",rawName:"v-show",value:t.showBtn,expression:"showBtn"}],staticClass:"highmargin-btn-box"},[e("div",{staticClass:"highmargin-btn",on:{click:t.skipNexTab}},[t._v("点击跳转至“"+t._s(t.skiptext)+"”")])])])}),s,!1,null,"2213dde2",null).exports}}]);
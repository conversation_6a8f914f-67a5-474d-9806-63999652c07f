(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[92782],{61255:function(t,a,s){"use strict";s(91058),s(82772),s(9653);var i=s(59502),d=s(67087);a.Z={props:["dataId","productNum","isSplit","medNum","isPack","bgcolor","btncolor"],data:function(){return{goodsId:"",issplit:"",productValue:"",mednum:"",ispack:!1}},watch:{dataId:function(t){this.goodsId=t,this.productValue=this.productNum?this.productNum:0,this.issplit=this.isSplit,this.mednum=this.medNum,this.ispack=this.isPack},isExtend:function(){i.Z.$emit("cart_isExtend",this.isExtend,this.goodsId)}},methods:{addProductCart:function(t){if(this.btn_hidden)this.postCartData(this.mednum);else{var a=t.target.getAttribute("edit-cate"),s=parseInt(t.currentTarget.children[1].value),d=parseInt(this.mednum);if("add"==a)s+=d;else{if("min"!=a)return;s=1==this.issplit?s-1:s-d}s=s>0?s:0,this.productValue=s,i.Z.$emit("listenToChildEvent",{isExtend:this.isExtend,dataId:this.goodsId,productValue:this.productValue}),this.postCartData(s)}},inputCart:function(t){var a=parseInt(t.target.value);a=a>=0?a:0,this.productValue=a,i.Z.$emit("listenToChildEvent",{isExtend:this.isExtend,dataId:this.goodsId,productValue:this.productValue}),this.postCartData(t.target.value)},androidclick:function(t){if(navigator.userAgent.indexOf("Android")>=0){t.target.setAttribute("readonly",!0),t.target.blur();var a=t.target.value;i.Z.$emit("showandorideditcomp",{id:this.goodsId,val:a,showandoridedit:!0,split:this.issplit,medpack:this.mednum,package:this.ispack})}},postCartData:function(t){var a=this;if(t>0){var s=1==this.ispack?{merchantId:this.merchantId,amount:t,packageId:this.goodsId}:{merchantId:this.merchantId,amount:t,skuId:this.goodsId};this.putRequest("post","/app/changeCart",s).then((function(s){if("success"===s.data.status){a.btn_hidden&&i.Z.$emit("changeprompt",{dialog:"已添加到购物车!",showprompt:!0}),Number(s.data.data.qty)&&(0,d.M0)("h5_page_CommodityDetails_o",{commodityId:a.goodsId,real:1}),s.data.data.qty!=t&&(a.productValue=s.data.data.qty),null!=s.data.dialog&&(20==s.data.dialog.style?i.Z.$emit("changesureDialog",{dialogmsg:s.data.dialog.msg,showsureDialog:!0}):i.Z.$emit("changeprompt",{dialog:s.data.dialog.msg,showprompt:!0})),s.errorMsg&&i.Z.$emit("changeprompt",{dialog:s.errorMsg,showprompt:!0});try{var o=1==a.ispack?{proid:a.goodsId,pronum:a.productValue,isAdd:1,type:1}:{proid:a.goodsId,pronum:a.productValue,isAdd:1};window.webkit.messageHandlers.addPlanNumber.postMessage(o)}catch(t){}try{1==a.ispack?window.hybrid.addPlanNumber(a.goodsId,a.productValue,1,1):window.hybrid.addPlanNumber(a.goodsId,a.productValue,1)}catch(t){}}else a.productValue=0,i.Z.$emit("listenToChildEvent",{isExtend:a.isExtend,dataId:a.goodsId,productValue:a.productValue}),s.data.errorMsg?i.Z.$emit("changeprompt",{dialog:s.data.errorMsg,showprompt:!0}):s.data.msg?i.Z.$emit("changesureDialog",{dialogmsg:s.data.msg,showsureDialog:!0}):i.Z.$emit("changeprompt",{dialog:s.data.dialog.msg,showprompt:!0})})).catch((function(t){}))}else{var o=1==this.ispack?{merchantId:this.merchantId,packageIds:this.goodsId}:{merchantId:this.merchantId,ids:this.goodsId};this.putRequest("post","/app/batchRemoveProductFromCart",o).then((function(t){if("success"==t.data.status){a.btn_hidden&&i.Z.$emit("changeprompt",{dialog:"已添从购物车删除!",showprompt:!0}),a.productValue=0;try{var s=1==a.ispack?{proid:a.goodsId,pronum:0,isAdd:1,type:1}:{proid:a.goodsId,pronum:0,isAdd:1};window.webkit.messageHandlers.addPlanNumber.postMessage(s)}catch(t){}try{1==a.ispack?window.hybrid.addPlanNumber(a.goodsId,0,1,1):window.hybrid.addPlanNumber(a.goodsId,0,1)}catch(t){}}}))}}},created:function(){this.goodsId=this.dataId,this.productValue=this.productNum?this.productNum:0,this.issplit=this.isSplit,this.mednum=this.medNum,this.ispack=this.isPack}}},61380:function(t,a,s){t.exports=s.p+"static/img/app_01.7decb00.png"},2442:function(t,a,s){t.exports=s.p+"static/img/app_02.2dfd1b4.png"},2667:function(t,a,s){t.exports=s.p+"static/img/app_03.9294184.png"},38078:function(t,a,s){t.exports=s.p+"static/img/app_04.1f8b80d.png"},76984:function(t,a,s){t.exports=s.p+"static/img/app_05.f82cc87.png"},36468:function(t,a,s){"use strict";s.d(a,{Z:function(){return d}});var i={data:function(){return{isExtend:!1}},mounted:function(){},mixins:[s(61255).Z]},d=(0,s(51900).Z)(i,(function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"buybtn",style:{borderColor:t.bgcolor},attrs:{bgcolr:t.bgcolor,"data-id":t.goodsId,"is-slipt":t.issplit,"med-pack-num":t.mednum,"is-extend":t.isExtend},on:{click:function(a){return a.stopPropagation(),a.preventDefault(),t.addProductCart.apply(null,arguments)}}},[s("span",{staticClass:"min",style:{backgroundColor:t.bgcolor},attrs:{"edit-cate":"min"}}),t._v(" "),s("input",{directives:[{name:"model",rawName:"v-model",value:t.productValue,expression:"productValue"}],staticClass:"input-val",class:"input-val"+t.goodsId,attrs:{"edit-cate":"edit",type:"tel"},domProps:{value:t.productValue},on:{change:t.inputCart,click:function(a){return a.preventDefault(),a.stopPropagation(),t.androidclick.apply(null,arguments)},input:function(a){a.target.composing||(t.productValue=a.target.value)}}}),t._v(" "),s("span",{staticClass:"add",style:{backgroundColor:t.bgcolor},attrs:{"edit-cate":"add"}})])}),[],!1,null,"2f0de2f5",null).exports},32172:function(t,a,s){"use strict";s.r(a),s.d(a,{default:function(){return e}});var i=[function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("div",{staticClass:"banner"},[i("img",{attrs:{src:s(61380),alt:""}}),t._v(" "),i("img",{attrs:{src:s(2442),alt:""}})])}],d=s(36468),o=(s(59502),{data:function(){return{hotData:[],dataId:"2080",proNum:0,bgcolor:"#762a06"}},methods:{},activated:function(){this.setAppTitle("东阿阿胶")},components:{cartBtn:d.Z}}),e=(0,s(51900).Z)(o,(function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("div",{attrs:{id:"ejiaopage"}},[t._m(0),t._v(" "),i("div",{staticClass:"manjian"},[i("img",{attrs:{src:s(2667),alt:""}}),t._v(" "),i("div",{staticClass:"manjian-in"},[i("div",{staticClass:"good-pills"},[i("a",{staticStyle:{display:"block"},attrs:{href:"ybmpage://productdetail?product_id=420751"}}),t._v(" "),i("div",{staticClass:"btn-box"},[i("cartBtn",{attrs:{bgcolor:t.bgcolor,"is-pack":!1,"data-id":420751,"is-split":0,"product-num":t.proNum,"med-num":2}})],1)]),t._v(" "),i("div",{staticClass:"good-pills"},[i("a",{staticStyle:{display:"block"},attrs:{href:"ybmpage://productdetail?product_id=633195"}}),t._v(" "),i("div",{staticClass:"btn-box"},[i("cartBtn",{attrs:{bgcolor:t.bgcolor,"is-pack":!1,"data-id":633195,"is-split":1,"product-num":t.proNum,"med-num":4}})],1)])])]),t._v(" "),i("div",{staticClass:"hot-content"},[i("div",[i("img",{attrs:{src:s(38078),alt:""}}),t._v(" "),i("router-link",{attrs:{to:"/packagesuper?ybm_title=超值套餐"}},[i("img",{attrs:{src:s(76984),alt:""}})])],1),t._v(" "),i("div",{staticClass:"btn-box"},[i("cartBtn",{attrs:{bgcolor:t.bgcolor,"is-pack":!0,"data-id":t.dataId,"is-split":1,"product-num":t.proNum,"med-num":1}})],1)])])}),i,!1,null,"4b4b1b0c",null).exports}}]);
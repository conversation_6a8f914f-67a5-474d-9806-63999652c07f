(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[79431],{9518:function(t,s,e){t.exports=e.p+"static/img/app_01.8ecadd8.png"},79190:function(t,s,e){t.exports=e.p+"static/img/app_02.43eca65.png"},17900:function(t,s,e){t.exports=e.p+"static/img/app_04.930e2cb.png"},68398:function(t,s,e){t.exports=e.p+"static/img/app_05.3348747.png"},61513:function(t,s,e){"use strict";e.r(s),e.d(s,{default:function(){return o}});var a=[function(){var t=this.$createElement,s=this._self._c||t;return s("div",{staticClass:"banner"},[s("img",{attrs:{src:e(9518),alt:""}})])}],i=e(24388),n=e(59502),c={data:function(){return{isCur:"first-content",istabfixed:!1,firstData:[],secondData:[]}},methods:{getDataList:function(t,s){var e=this;this.putRequest("post","/app/layout/initExhibitionModulePage?sort=1",{merchantId:this.merchantId,limit:1e3,offset:0,exhibitionId:t}).then((function(t){"success"==t.data.status&&(e[s]=t.data.data.rows,e[s].licenseStatus=t.data.data.licenseStatus),e.$nextTick((function(){n.Z.$emit("changeloading",!1)}))})).catch((function(t){}))},moveEvent:function(){this.istabfixed=!0;var t=document.querySelector("#jieqiantunhuopage").scrollTop,s=document.querySelector(".tabs-box").offsetTop,e=document.querySelector(".tabs-box").offsetHeight;this.istabfixed=t>=s;var a=this.$refs.first.offsetTop,i=this.$refs.second.offsetTop;t>=a-e&&t<i-e?this.isCur="first-content":t>=i-e&&(this.isCur="second-content")},tabitemclick:function(t){this.istabfixed=!0;var s=t.currentTarget.getAttribute("contClass");this.isCur=s;var e=document.querySelector("."+s).offsetTop,a=document.querySelector(".tabs-box").offsetHeight;document.querySelector("#jieqiantunhuopage").scrollTop=e-a}},mounted:function(){this.getDataList("ZS201905301111294916","firstData"),this.getDataList("ZS201905301113055560","secondData")},activated:function(){this.setAppTitle("节前囤货大放价")},components:{temprow:i.Z}},o=(0,e(51900).Z)(c,(function(){var t=this,s=t.$createElement,a=t._self._c||s;return a("div",{attrs:{id:"jieqiantunhuopage"},on:{touchmove:t.moveEvent}},[t._m(0),t._v(" "),a("div",{staticClass:"quan"},[a("img",{attrs:{src:e(79190),alt:""}}),t._v(" "),a("router-link",{staticClass:"linkA",attrs:{to:"/voucherscenter?ybm_title=领券中心"}})],1),t._v(" "),a("div",{staticClass:"tabs-box"},[a("div",{staticClass:"tab-list",class:{tabfiexd:t.istabfixed}},[a("div",{staticClass:"tab-item",class:{tabclickcolor:"first-content"==t.isCur},attrs:{contClass:"first-content"},on:{click:t.tabitemclick}},[t._v("\n                抢爆款\n                "),a("p",[t._v("每满1500元减35元")])]),t._v(" "),a("div",{staticClass:"tab-item",class:{tabclickcolor:"second-content"==t.isCur},attrs:{contClass:"second-content"},on:{click:t.tabitemclick}},[t._v("\n                热卖推荐\n                "),a("p",[t._v("每满699元减20元")])])])]),t._v(" "),a("div",{ref:"first",staticClass:"first-content"},[a("img",{attrs:{src:e(17900),alt:""}}),t._v(" "),a("temprow",{attrs:{"goods-data":t.firstData,"license-status":t.firstData.licenseStatus||0}})],1),t._v(" "),a("div",{ref:"second",staticClass:"second-content"},[a("img",{attrs:{src:e(68398),alt:""}}),t._v(" "),a("temprow",{attrs:{"goods-data":t.secondData,"license-status":t.secondData.licenseStatus||0}})],1)])}),a,!1,null,"e804d14a",null).exports}}]);
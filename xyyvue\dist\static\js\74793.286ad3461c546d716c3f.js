(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[74793],{57997:function(t,a,e){t.exports=e.p+"static/img/jiuzhen_01.07c8ee0.png"},58582:function(t,a,e){"use strict";e.r(a),e.d(a,{default:function(){return l}});var s=[function(){var t=this.$createElement,a=this._self._c||t;return a("div",{staticClass:"banner"},[a("img",{attrs:{src:e(57997),alt:""}})])}],i=e(4942),o=(e(91058),e(24388)),n=e(59502),c={data:function(){var t;return t={iscur:0,licenseStatus:0,temprowData:[],isload:!1,loadingmsg:"正在加载···",tabData:[{hdid:"ZS201907241007355500",title:"单品满减"},{hdid:"ZS201907241007493752",title:"满100元减6元"}],isfixed:!1,chooseHdid:"ZS201907241007355500",tabIndex:0,scrollload:!0},(0,i.Z)(t,"isload",!1),(0,i.Z)(t,"loadingmsg","正在加载···"),(0,i.Z)(t,"pagecur",0),(0,i.Z)(t,"totalpage",0),(0,i.Z)(t,"skiptext","解热镇痛"),(0,i.Z)(t,"showBtn",!1),t},methods:{tabitemclick:function(t){document.querySelector("#jzthpage").scrollTop=document.querySelector(".checktab").offsetTop,this.translatetabs()},skipNexTab:function(){this.tabIndex++,this.tabIndex=this.tabIndex==this.tabData.length?0:this.tabIndex,this.translatetabs()},moveEvent:function(){var t=document.querySelector(".checktab").offsetTop,a=document.querySelector("#jzthpage").scrollTop;this.isfixed=a>=t,a>800&&n.Z.$emit("showBtn",{isShow:!0,dom:"#jzthpage"});var e=window.screen.height;document.querySelector("#jzthpage").scrollHeight-a<=e&&this.scrollload&&(this.scrollload=!1,this.pagecur>=this.totalpage-1?(this.isload=!1,this.showBtn=!0):(this.isload=!0,this.loadingmsg="正在加载···",this.pagecur++,this.getDatalist(this.chooseHdid,"temprowData")))},changetabs:function(t){this.tabIndex=parseInt(t.target.getAttribute("tabitem")),this.translatetabs()},translatetabs:function(){this.iscur=this.tabIndex,this.chooseHdid=this.tabData[this.tabIndex].hdid,document.querySelector("#jzthpage").scrollTop=document.querySelector(".checktab").offsetTop;var t=this.tabIndex+1;t=t==this.tabData.length?0:t,this.skiptext=this.tabData[t].title,this.pagecur=0,this.showBtn=!1,this.getDatalist(this.chooseHdid,"temprowData")},getDatalist:function(t,a){var e=this;this.putRequest("post","/app/layout/initExhibitionModulePage?sort=1",{merchantId:this.merchantId,limit:10,offset:this.pagecur,exhibitionId:t}).then((function(t){if("success"==t.data.status){0==e.pagecur&&(e.totalpage=t.data.data.pageCount,e[a]=[]),e.isload=!1,e.scrollload=!0;var s=t.data.data.rows;e[a].push.apply(e[a],s),e.licenseStatus=t.data.data.licenseStatus}e.$nextTick((function(){n.Z.$emit("changeloading",!1)}))})).catch((function(t){}))}},mounted:function(){document.querySelector("#jzthpage").addEventListener("scroll",this.moveEvent),this.getDatalist("ZS201907241007355500","temprowData")},components:{temprow:o.Z},activated:function(){this.setAppTitle("九珍堂")}},l=(0,e(51900).Z)(c,(function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{attrs:{id:"jzthpage"}},[t._m(0),t._v(" "),e("div",{staticClass:"checktab"},[e("div",{staticClass:"tabs-box",class:{tabfixed:t.isfixed},on:{click:t.changetabs}},[e("ul",{staticClass:"tab-scroll"},t._l(t.tabData,(function(a,s){return e("li",{key:s,staticClass:"tab-item",class:{cur:t.iscur==s},attrs:{tabitem:s}},[t._v(t._s(a.title)),e("i")])})),0)])]),t._v(" "),e("div",{staticClass:"temprow-box"},[e("temprow",{attrs:{"goods-data":t.temprowData,"license-status":t.licenseStatus||0}}),t._v(" "),e("div",{directives:[{name:"show",rawName:"v-show",value:t.isload,expression:"isload"}],staticClass:"loaing"},[t._v(t._s(t.loadingmsg))])],1)])}),s,!1,null,"3ff80004",null).exports}}]);
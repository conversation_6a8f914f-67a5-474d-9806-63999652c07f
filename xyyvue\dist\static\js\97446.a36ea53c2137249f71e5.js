(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[97446],{61255:function(t,a,s){"use strict";s(91058),s(82772),s(9653);var i=s(59502),e=s(67087);a.Z={props:["dataId","productNum","isSplit","medNum","isPack","bgcolor","btncolor"],data:function(){return{goodsId:"",issplit:"",productValue:"",mednum:"",ispack:!1}},watch:{dataId:function(t){this.goodsId=t,this.productValue=this.productNum?this.productNum:0,this.issplit=this.isSplit,this.mednum=this.medNum,this.ispack=this.isPack},isExtend:function(){i.Z.$emit("cart_isExtend",this.isExtend,this.goodsId)}},methods:{addProductCart:function(t){if(this.btn_hidden)this.postCartData(this.mednum);else{var a=t.target.getAttribute("edit-cate"),s=parseInt(t.currentTarget.children[1].value),e=parseInt(this.mednum);if("add"==a)s+=e;else{if("min"!=a)return;s=1==this.issplit?s-1:s-e}s=s>0?s:0,this.productValue=s,i.Z.$emit("listenToChildEvent",{isExtend:this.isExtend,dataId:this.goodsId,productValue:this.productValue}),this.postCartData(s)}},inputCart:function(t){var a=parseInt(t.target.value);a=a>=0?a:0,this.productValue=a,i.Z.$emit("listenToChildEvent",{isExtend:this.isExtend,dataId:this.goodsId,productValue:this.productValue}),this.postCartData(t.target.value)},androidclick:function(t){if(navigator.userAgent.indexOf("Android")>=0){t.target.setAttribute("readonly",!0),t.target.blur();var a=t.target.value;i.Z.$emit("showandorideditcomp",{id:this.goodsId,val:a,showandoridedit:!0,split:this.issplit,medpack:this.mednum,package:this.ispack})}},postCartData:function(t){var a=this;if(t>0){var s=1==this.ispack?{merchantId:this.merchantId,amount:t,packageId:this.goodsId}:{merchantId:this.merchantId,amount:t,skuId:this.goodsId};this.putRequest("post","/app/changeCart",s).then((function(s){if("success"===s.data.status){a.btn_hidden&&i.Z.$emit("changeprompt",{dialog:"已添加到购物车!",showprompt:!0}),Number(s.data.data.qty)&&(0,e.M0)("h5_page_CommodityDetails_o",{commodityId:a.goodsId,real:1}),s.data.data.qty!=t&&(a.productValue=s.data.data.qty),null!=s.data.dialog&&(20==s.data.dialog.style?i.Z.$emit("changesureDialog",{dialogmsg:s.data.dialog.msg,showsureDialog:!0}):i.Z.$emit("changeprompt",{dialog:s.data.dialog.msg,showprompt:!0})),s.errorMsg&&i.Z.$emit("changeprompt",{dialog:s.errorMsg,showprompt:!0});try{var d=1==a.ispack?{proid:a.goodsId,pronum:a.productValue,isAdd:1,type:1}:{proid:a.goodsId,pronum:a.productValue,isAdd:1};window.webkit.messageHandlers.addPlanNumber.postMessage(d)}catch(t){}try{1==a.ispack?window.hybrid.addPlanNumber(a.goodsId,a.productValue,1,1):window.hybrid.addPlanNumber(a.goodsId,a.productValue,1)}catch(t){}}else a.productValue=0,i.Z.$emit("listenToChildEvent",{isExtend:a.isExtend,dataId:a.goodsId,productValue:a.productValue}),s.data.errorMsg?i.Z.$emit("changeprompt",{dialog:s.data.errorMsg,showprompt:!0}):s.data.msg?i.Z.$emit("changesureDialog",{dialogmsg:s.data.msg,showsureDialog:!0}):i.Z.$emit("changeprompt",{dialog:s.data.dialog.msg,showprompt:!0})})).catch((function(t){}))}else{var d=1==this.ispack?{merchantId:this.merchantId,packageIds:this.goodsId}:{merchantId:this.merchantId,ids:this.goodsId};this.putRequest("post","/app/batchRemoveProductFromCart",d).then((function(t){if("success"==t.data.status){a.btn_hidden&&i.Z.$emit("changeprompt",{dialog:"已添从购物车删除!",showprompt:!0}),a.productValue=0;try{var s=1==a.ispack?{proid:a.goodsId,pronum:0,isAdd:1,type:1}:{proid:a.goodsId,pronum:0,isAdd:1};window.webkit.messageHandlers.addPlanNumber.postMessage(s)}catch(t){}try{1==a.ispack?window.hybrid.addPlanNumber(a.goodsId,0,1,1):window.hybrid.addPlanNumber(a.goodsId,0,1)}catch(t){}}}))}}},created:function(){this.goodsId=this.dataId,this.productValue=this.productNum?this.productNum:0,this.issplit=this.isSplit,this.mednum=this.medNum,this.ispack=this.isPack}}},4502:function(t,a,s){t.exports=s.p+"static/img/app_02.856aae3.png"},78614:function(t,a,s){t.exports=s.p+"static/img/app_03.0dd50c3.png"},24456:function(t,a,s){t.exports=s.p+"static/img/app_04.2738e44.png"},33094:function(t,a,s){t.exports=s.p+"static/img/app_05.de6263c.png"},21967:function(t,a,s){t.exports=s.p+"static/img/app_06.3362f97.png"},92515:function(t,a,s){t.exports=s.p+"static/img/app_07.7a68752.png"},21947:function(t,a,s){t.exports=s.p+"static/img/app_08.2a732c1.png"},36468:function(t,a,s){"use strict";s.d(a,{Z:function(){return e}});var i={data:function(){return{isExtend:!1}},mounted:function(){},mixins:[s(61255).Z]},e=(0,s(51900).Z)(i,(function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"buybtn",style:{borderColor:t.bgcolor},attrs:{bgcolr:t.bgcolor,"data-id":t.goodsId,"is-slipt":t.issplit,"med-pack-num":t.mednum,"is-extend":t.isExtend},on:{click:function(a){return a.stopPropagation(),a.preventDefault(),t.addProductCart.apply(null,arguments)}}},[s("span",{staticClass:"min",style:{backgroundColor:t.bgcolor},attrs:{"edit-cate":"min"}}),t._v(" "),s("input",{directives:[{name:"model",rawName:"v-model",value:t.productValue,expression:"productValue"}],staticClass:"input-val",class:"input-val"+t.goodsId,attrs:{"edit-cate":"edit",type:"tel"},domProps:{value:t.productValue},on:{change:t.inputCart,click:function(a){return a.preventDefault(),a.stopPropagation(),t.androidclick.apply(null,arguments)},input:function(a){a.target.composing||(t.productValue=a.target.value)}}}),t._v(" "),s("span",{staticClass:"add",style:{backgroundColor:t.bgcolor},attrs:{"edit-cate":"add"}})])}),[],!1,null,"2f0de2f5",null).exports},67901:function(t,a,s){"use strict";s.r(a),s.d(a,{default:function(){return d}});var i=[function(){var t=this.$createElement,a=this._self._c||t;return a("div",[a("img",{attrs:{src:s(4502)}})])},function(){var t=this.$createElement,a=this._self._c||t;return a("div",{staticClass:"cont"},[a("img",{attrs:{src:s(24456),alt:""}})])},function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("div",{staticClass:"btn"},[i("img",{attrs:{src:s(21947),alt:""}}),t._v(" "),i("a",{staticClass:"toindex",attrs:{href:"ybmpage://maintab?tab=0"}})])}],e={data:function(){return{proNum:0}},activated:function(){this.setAppTitle("新人专享")},components:{cartBtn:s(36468).Z}},d=(0,s(51900).Z)(e,(function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("div",{attrs:{id:"newstampshubei"}},[t._m(0),t._v(" "),i("div",{staticClass:"voucher"},[i("img",{attrs:{src:s(78614),alt:""}}),t._v(" "),i("router-link",{attrs:{to:"/voucherscenter?ybm_title=领券中心"}})],1),t._v(" "),t._m(1),t._v(" "),i("div",{staticClass:"good-pills"},[i("img",{attrs:{src:s(33094),alt:""}}),t._v(" "),i("img",{attrs:{src:s(21967),alt:""}}),t._v(" "),i("img",{attrs:{src:s(92515),alt:""}}),t._v(" "),i("div",{staticClass:"btn-box"},[i("cartBtn",{ref:"autotext",attrs:{"is-pack":!0,"data-id":1689,"is-split":1,"product-num":t.proNum,"med-num":1}})],1)]),t._v(" "),t._m(2)])}),i,!1,null,"82dbdb34",null).exports}}]);
(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[62944],{61255:function(t,s,a){"use strict";a(91058),a(82772),a(9653);var i=a(59502),e=a(67087);s.Z={props:["dataId","productNum","isSplit","medNum","isPack","bgcolor","btncolor"],data:function(){return{goodsId:"",issplit:"",productValue:"",mednum:"",ispack:!1}},watch:{dataId:function(t){this.goodsId=t,this.productValue=this.productNum?this.productNum:0,this.issplit=this.isSplit,this.mednum=this.medNum,this.ispack=this.isPack},isExtend:function(){i.Z.$emit("cart_isExtend",this.isExtend,this.goodsId)}},methods:{addProductCart:function(t){if(this.btn_hidden)this.postCartData(this.mednum);else{var s=t.target.getAttribute("edit-cate"),a=parseInt(t.currentTarget.children[1].value),e=parseInt(this.mednum);if("add"==s)a+=e;else{if("min"!=s)return;a=1==this.issplit?a-1:a-e}a=a>0?a:0,this.productValue=a,i.Z.$emit("listenToChildEvent",{isExtend:this.isExtend,dataId:this.goodsId,productValue:this.productValue}),this.postCartData(a)}},inputCart:function(t){var s=parseInt(t.target.value);s=s>=0?s:0,this.productValue=s,i.Z.$emit("listenToChildEvent",{isExtend:this.isExtend,dataId:this.goodsId,productValue:this.productValue}),this.postCartData(t.target.value)},androidclick:function(t){if(navigator.userAgent.indexOf("Android")>=0){t.target.setAttribute("readonly",!0),t.target.blur();var s=t.target.value;i.Z.$emit("showandorideditcomp",{id:this.goodsId,val:s,showandoridedit:!0,split:this.issplit,medpack:this.mednum,package:this.ispack})}},postCartData:function(t){var s=this;if(t>0){var a=1==this.ispack?{merchantId:this.merchantId,amount:t,packageId:this.goodsId}:{merchantId:this.merchantId,amount:t,skuId:this.goodsId};this.putRequest("post","/app/changeCart",a).then((function(a){if("success"===a.data.status){s.btn_hidden&&i.Z.$emit("changeprompt",{dialog:"已添加到购物车!",showprompt:!0}),Number(a.data.data.qty)&&(0,e.M0)("h5_page_CommodityDetails_o",{commodityId:s.goodsId,real:1}),a.data.data.qty!=t&&(s.productValue=a.data.data.qty),null!=a.data.dialog&&(20==a.data.dialog.style?i.Z.$emit("changesureDialog",{dialogmsg:a.data.dialog.msg,showsureDialog:!0}):i.Z.$emit("changeprompt",{dialog:a.data.dialog.msg,showprompt:!0})),a.errorMsg&&i.Z.$emit("changeprompt",{dialog:a.errorMsg,showprompt:!0});try{var r=1==s.ispack?{proid:s.goodsId,pronum:s.productValue,isAdd:1,type:1}:{proid:s.goodsId,pronum:s.productValue,isAdd:1};window.webkit.messageHandlers.addPlanNumber.postMessage(r)}catch(t){}try{1==s.ispack?window.hybrid.addPlanNumber(s.goodsId,s.productValue,1,1):window.hybrid.addPlanNumber(s.goodsId,s.productValue,1)}catch(t){}}else s.productValue=0,i.Z.$emit("listenToChildEvent",{isExtend:s.isExtend,dataId:s.goodsId,productValue:s.productValue}),a.data.errorMsg?i.Z.$emit("changeprompt",{dialog:a.data.errorMsg,showprompt:!0}):a.data.msg?i.Z.$emit("changesureDialog",{dialogmsg:a.data.msg,showsureDialog:!0}):i.Z.$emit("changeprompt",{dialog:a.data.dialog.msg,showprompt:!0})})).catch((function(t){}))}else{var r=1==this.ispack?{merchantId:this.merchantId,packageIds:this.goodsId}:{merchantId:this.merchantId,ids:this.goodsId};this.putRequest("post","/app/batchRemoveProductFromCart",r).then((function(t){if("success"==t.data.status){s.btn_hidden&&i.Z.$emit("changeprompt",{dialog:"已添从购物车删除!",showprompt:!0}),s.productValue=0;try{var a=1==s.ispack?{proid:s.goodsId,pronum:0,isAdd:1,type:1}:{proid:s.goodsId,pronum:0,isAdd:1};window.webkit.messageHandlers.addPlanNumber.postMessage(a)}catch(t){}try{1==s.ispack?window.hybrid.addPlanNumber(s.goodsId,0,1,1):window.hybrid.addPlanNumber(s.goodsId,0,1)}catch(t){}}}))}}},created:function(){this.goodsId=this.dataId,this.productValue=this.productNum?this.productNum:0,this.issplit=this.isSplit,this.mednum=this.medNum,this.ispack=this.isPack}}},11932:function(t){t.exports="data:image/png;base64,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"},42845:function(t,s,a){t.exports=a.p+"static/img/baokuan_03.b5ccaad.png"},31280:function(t,s,a){t.exports=a.p+"static/img/baokuan_04.c61b1d6.png"},84786:function(t,s,a){"use strict";a.d(s,{Z:function(){return c}});a(91058),a(47042),a(41539),a(68309),a(91038),a(78783),a(82526),a(41817),a(32165),a(66992),a(33948);var i=a(61255),e=a(59502);function r(t,s){var a="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!a){if(Array.isArray(t)||(a=function(t,s){if(!t)return;if("string"==typeof t)return n(t,s);var a=Object.prototype.toString.call(t).slice(8,-1);"Object"===a&&t.constructor&&(a=t.constructor.name);if("Map"===a||"Set"===a)return Array.from(t);if("Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return n(t,s)}(t))||s&&t&&"number"==typeof t.length){a&&(t=a);var i=0,e=function(){};return{s:e,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:e}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,o=!0,c=!1;return{s:function(){a=a.call(t)},n:function(){var t=a.next();return o=t.done,t},e:function(t){c=!0,r=t},f:function(){try{o||null==a.return||a.return()}finally{if(c)throw r}}}}function n(t,s){(null==s||s>t.length)&&(s=t.length);for(var a=0,i=new Array(s);a<s;a++)i[a]=t[a];return i}var o={props:{btn_hidden:{default:!1},from_tab:{type:String,default:""},goodItem:Object,big:{type:Boolean,default:!1}},computed:{isExtend:function(){return 0!=this.productValue}},mounted:function(){var t=this;e.Z.$on("listenToChildEvent",(function(s){var a=s.isExtend,i=s.dataId;parseInt(i)===parseInt(t.goodsId)&&(a||(t.productValue=0))})),e.Z.$on("update_cart",(function(s){var a,i=s.length,e=0,n=r(s);try{for(n.s();!(a=n.n()).done;){var o=a.value;if(o.item.id===t.goodsId){t.productValue=o.item.amount;break}e++}}catch(t){n.e(t)}finally{n.f()}i===e&&(t.productValue=0)}))},watch:{isExtend:function(){e.Z.$emit("cart_isExtend",this.isExtend,this.goodsId)}},mixins:[i.Z]},c=(0,a(51900).Z)(o,(function(){var t=this,s=t.$createElement,a=t._self._c||s;return a("div",{staticClass:"buybtn",class:{bigStyle:t.big},attrs:{"data-id":t.goodsId,"is-slipt":t.issplit,"med-pack-num":t.mednum,"is-extend":t.isExtend},on:{click:function(s){return s.stopPropagation(),s.preventDefault(),t.addProductCart.apply(null,arguments)}}},[t.btn_hidden?t._e():a("span",{directives:[{name:"show",rawName:"v-show",value:t.isExtend,expression:"isExtend"}],staticClass:"min",attrs:{"edit-cate":"min"}}),t._v(" "),t.btn_hidden?t._e():a("input",{directives:[{name:"show",rawName:"v-show",value:t.isExtend,expression:"isExtend"},{name:"model",rawName:"v-model",value:t.productValue,expression:"productValue"}],staticClass:"input-val",class:"input-val"+t.goodsId,attrs:{"edit-cate":"edit",type:"tel"},domProps:{value:t.productValue},on:{change:t.inputCart,click:function(s){return s.preventDefault(),s.stopPropagation(),t.androidclick.apply(null,arguments)},input:function(s){s.target.composing||(t.productValue=s.target.value)}}}),t._v(" "),a("span",{directives:[{name:"show",rawName:"v-show",value:t.isExtend,expression:"isExtend"}],staticClass:"add addshow",attrs:{"edit-cate":"add"}},[t._v("+")]),t._v(" "),a("span",{directives:[{name:"show",rawName:"v-show",value:!t.isExtend,expression:"!isExtend"}],staticClass:"plus",attrs:{"edit-cate":"add"}})])}),[],!1,null,"3c07d9fa",null).exports},46825:function(t,s,a){"use strict";a.d(s,{Z:function(){return r}});a(56977),a(54678);var i=a(59502),e={data:function(){return{isExtend:!1,productNum:""}},filters:{fixedtwo:function(t){return parseFloat(t).toFixed(2)}},props:["dataId","uniformPrice","suggestPrice","grossMargin","status"],watch:{productNum:function(){}},methods:{showExtend:function(){var t=this;i.Z.$on("listenToChildEvent",(function(s){t.dataId==s.dataId&&(t.isExtend=s.isExtend,t.productNum=s.productValue)}))}},mounted:function(){this.showExtend()}},r=(0,a(51900).Z)(e,(function(){var t=this,s=t.$createElement,a=t._self._c||s;return a("div",{staticClass:"priceBox",attrs:{"data-id":t.dataId,productNum:t.productNum,uniformPrice:t.uniformPrice,suggestPrice:t.suggestPrice,grossMargin:t.grossMargin,status:t.status}},[t.uniformPrice?a("div",{staticClass:"control-price"},[a("span",{staticClass:"text"},[t._v("控销价")]),a("span",{staticClass:"jiage"},[t._v("¥"+t._s(t._f("fixedtwo")(t.uniformPrice)))])]):t._e(),t._v(" "),t.suggestPrice?a("div",{staticClass:"purchase-price"},[a("span",{staticClass:"text"},[t._v("零售价")]),a("span",{staticClass:"jiage"},[t._v("¥"+t._s(t._f("fixedtwo")(t.suggestPrice)))])]):t._e(),t._v(" "),t.grossMargin?a("div",{staticClass:"gross-margin"},[2!=t.status?a("span",{directives:[{name:"show",rawName:"v-show",value:t.isExtend,expression:"isExtend"}],staticClass:"maolilv"},[t._v("...")]):t._e(),t._v(" "),2==t.status?a("span",{staticClass:"maolilv"},[t._v("...")]):t._e(),t._v(" "),2!=t.status?a("div",{directives:[{name:"show",rawName:"v-show",value:!t.isExtend,expression:"!isExtend"}]},[a("span",{staticClass:"text"},[t._v("(毛利率")]),a("span",{staticClass:"jiage"},[t._v(t._s(t.grossMargin)+")")])]):t._e()]):t._e()])}),[],!1,null,"481a41d3",null).exports},89095:function(t,s,a){"use strict";a.d(s,{Z:function(){return c}});a(9653),a(56977),a(54678),a(74916),a(15306),a(54747),a(47042),a(69600);var i=a(84786),e=a(46825),r=a(67087),n=a(59502),o={props:{goodsData:Array,licenseStatus:Number,from_tab:{type:String,default:""},trackingStatus:{type:Boolean,default:!0},isLive:Number},data:function(){return{datalist:[],imgUrl:"",isExtend:"",dataId:""}},computed:{detailUrl:function(){return n.Z.detailBaseUrl}},filters:{fixedtwo:function(t){return parseFloat(t).toFixed(2)},replace:function(t){return t.replace(/-/g,".")}},components:{cartBtn:i.Z,priceBox:e.Z},methods:{getIdList:function(t){var s=[];t&&t.length>0?(t.forEach((function(t,a){s.push(t.id)})),this.getPrice(t,s)):this.datalist=t},splitData:function(t){for(var s=[],a=0,i=t.length;a<i;a+=20)s.push(t.slice(a,a+20));return s},getPrice:function(t,s){var a=this,i=this.splitData(s),e=0;i.forEach((function(s,r){a.putRequest("post","/app/marketing/discount/satisfactoryInHandPrice",{merchantId:a.merchantId,skuIds:s.join(",")}).then((function(s){if(a.$nextTick((function(){n.Z.$emit("changeloading",!1)})),"success"==s.data.status){var r=s.data.data;r&&r.length>0&&r.forEach((function(s,a){t.forEach((function(t,a){s.skuId==t.id&&(t.zheHouPrice=s.price)}))}))}++e===i.length&&t&&t.length>0&&(a.datalist=[],t.forEach((function(t,s){a.datalist.push(t)})))})).catch((function(s){a.datalist=t}))}))},showExtend:function(t){this.isExtend=t.isExtend,this.dataId=t.dataId,this.productNum=t.productValue}},mounted:function(){var t=this.goodsData;this.getIdList(t),this.imgUrl=this.imgBaseUrl,(0,r.dA)("h5_page_ListPage_ExpStatic",{list:t},"",this.trackingStatus)},watch:{goodsData:function(t,s){this.getIdList(t),(0,r.dA)("h5_page_ListPage_ExpStatic",{list:t},"",this.trackingStatus)}}},c=(0,a(51900).Z)(o,(function(){var t=this,s=t.$createElement,i=t._self._c||s;return i("div",{staticClass:"templist"},t._l(t.datalist,(function(s,e){return i("div",{key:e,staticClass:"templist-item"},[i("a",{staticClass:"img-box",attrs:{href:t.detailUrl+"product_id="+s.id}},[i("div",{staticClass:"images"},[i("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.imgUrl+"/ybm/product/min/"+s.imageUrl,expression:"imgUrl + '/ybm/product/min/' + item.imageUrl"}],staticClass:"pic",attrs:{alt:""}}),t._v(" "),s.markerUrl&&!s.reducePrice?i("img",{staticClass:"activity-token",attrs:{src:t.imgUrl+s.markerUrl,alt:""}}):t._e(),t._v(" "),s.markerUrl&&s.reducePrice&&(1!=s.isControl||1==s.isPurchase&&1==s.isControl)?i("div",{staticClass:"mark-text"},[i("img",{attrs:{src:t.imgUrl+s.markerUrl,alt:""}}),t._v(" "),i("h4",[t._v("药采节价："+t._s(s.reducePrice))])]):t._e(),t._v(" "),s.activityTag&&s.activityTag.tagNoteBackGroupUrl?i("div",{staticClass:"biaoqian"},[i("img",{attrs:{src:t.imgUrl+s.activityTag.tagNoteBackGroupUrl,alt:""}}),t._v(" "),i("div",{staticClass:"tejia806"},[i("span",{staticClass:"discount"},[t._v("\n              "+t._s(s.activityTag.timeStr)+"\n            ")]),t._v(" "),i("div",{staticClass:"labelBox"},t._l(s.activityTag.skuTagNotes,(function(s,a){return i("span",{key:a,staticClass:"price806",style:{color:"#"+s.textColor}},[t._v(t._s(s.text))])})),0)])]):t._e(),t._v(" "),s.activityTag&&s.activityTag.tagNoteBackGroupUrl&&2==+s.activityTag.sourceType?i("div",{staticClass:"biaoqian shop-biaoqian"},[i("img",{attrs:{src:s.activityTag.tagNoteBackGroupUrl,alt:""}}),t._v(" "),s.activityTag.customTopNote?i("div",{staticClass:"customTopNote"},[t._v(t._s(s.activityTag.customTopNote))]):t._e(),t._v(" "),i("div",[i("span",{staticClass:"shop-discount"},[t._v(" "+t._s(s.activityTag.timeStr)+" ")]),t._v(" "),t._l(s.activityTag.skuTagNotes,(function(s,a){return i("span",{key:a,staticClass:"shop-text"},[t._v(t._s(s.text))])}))],2),t._v(" "),s.activityTag.customBottomNote?i("div",{staticClass:"customBottomNote"},[t._v(t._s(s.activityTag.customBottomNote))]):t._e()]):t._e()]),t._v(" "),2==s.status?i("div",{staticClass:"sold-out posmiddle"},[t._v("售罄")]):t._e()]),t._v(" "),i("div",{staticClass:"med-mesg"},[i("a",{attrs:{href:t.detailUrl+"product_id="+s.id}},[i("div",{staticClass:"commonName"},[s.activityTag&&s.activityTag.tagUrl?i("span",{staticClass:"bq-hgj"},[i("img",{attrs:{src:t.imgUrl+s.activityTag.tagUrl,alt:""}})]):t._e(),t._v(" "),1==s.agent?i("span",{staticClass:"dujia"},[t._v("独家")]):t._e(),t._v(" "),i("span",{staticClass:"name"},[t._v(t._s(s.commonName))]),t._v(" "),i("span",{staticClass:"spec"},[t._v("/"+t._s(s.spec))])]),t._v(" "),i("div",{staticClass:"factory"},[i("div",{staticClass:"chang"}),t._v(" "),i("div",{staticClass:"name"},[t._v(t._s(s.manufacturer))])]),t._v(" "),i("div",{staticClass:"effect"},[i("div",{staticClass:"xiao"}),t._v(" "),s.nearEffect?i("div",{staticClass:"name"},[t._v("\n            "+t._s(t._f("replace")(s.nearEffect))+"\n          ")]):t._e(),t._v("\n          /\n          "),s.nearEffect?i("div",{staticClass:"name"},[t._v("\n            "+t._s(t._f("replace")(s.farEffect))+"\n          ")]):t._e(),t._v(" "),i("div",{staticClass:"midPack"},[t._v(t._s(s.mediumPackageTitle))])])]),t._v(" "),i("div",{staticClass:"price-btn-box"},[1===t.licenseStatus||5===t.licenseStatus?i("div",{staticClass:"qualifications"},[t._v("\n          价格认证资质可见\n        ")]):1!=s.isPurchase&&1==s.isControl?i("div",{staticClass:"nobuy"},[t._v("\n          暂无购买权限\n        ")]):i("div",{staticClass:"price-numer"},["true"==s.isOEM&&0==s.signStatus||0==s.showAgree?i("span",{staticClass:"price-permission"},[t._v("价格签署协议可见")]):2==s.priceType?i("i",[t._v("\n            ¥"+t._s(t._f("fixedtwo")(s.skuPriceRangeList[0].price))+"~"+t._s(t._f("fixedtwo")(s.skuPriceRangeList[s.skuPriceRangeList.length-1].price))+"\n          ")]):i("div",{staticClass:"pricewapper"},[i("div",{staticClass:"price-box clearfixed"},[t.isLive?i("div",{staticClass:"price-two",staticStyle:{width:"83%"}},[t._m(0,!0)]):i("div",{staticClass:"price-two"},[i("p",{staticClass:"ellipsis-box"},[i("span",[t._v("¥"+t._s(t._f("fixedtwo")(s.fob)))]),t._v(" "),s.zheHouPrice?i("span",{staticClass:"zhekou"},[t._v(t._s(s.zheHouPrice))]):t._e()])]),t._v(" "),i("div",{staticClass:"btn-box"},[2!==s.status&&1!==t.licenseStatus&&5!==t.licenseStatus?i("div",["true"==s.isOEM&&0==s.signStatus||0==s.showAgree?i("div"):i("div",[1!=s.isControl||1==s.isPurchase&&1==s.isControl?i("cartBtn",{attrs:{"is-pack":!1,"data-id":s.id,"is-split":s.isSplit,"product-num":s.cartProductNum,"med-num":s.mediumPackageNum}}):t._e()],1)]):i("img",{staticClass:"bell",attrs:{src:a(11932),alt:""}})])]),t._v(" "),i("div",{staticClass:"control-hid clearfixed"},[1!==t.licenseStatus&&5!==t.licenseStatus?i("div",{staticClass:"control-box"},["true"!=s.isOEM&&(1!=s.isControl||1==s.isPurchase&&1==s.isControl)||"true"==s.isOEM&&1==s.signStatus?i("priceBox",{attrs:{uniformPrice:s.uniformPrice,suggestPrice:s.suggestPrice,grossMargin:s.grossMargin}}):t._e()],1):t._e()])])])]),t._v(" "),i("div",{staticClass:"label-box",class:{"label-box-hide":(!s.tagList||s.tagList.length<=0)&&!(1==s.isUsableMedicalStr)}},[s.tagList&&s.tagList.length>0?i("div",{staticClass:"labels"},t._l(s.tagList.slice(0,3),(function(s,a){return i("span",{key:a,class:"span"+s.uiType},[t._v(t._s(s.name))])})),0):t._e()]),t._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:0!==s.isThirdCompany,expression:"item.isThirdCompany !== 0"}],staticClass:"enter-shop"},[s.companyName?i("i",{staticClass:"icon"}):t._e(),t._v(" "),s.companyName?i("span",[i("span",{staticClass:"companyName"},[t._v(t._s(s.companyName))])]):t._e()])])])})),0)}),[function(){var t=this,s=t.$createElement,a=t._self._c||s;return a("p",[a("span",{staticStyle:{"font-size":"0.28rem",color:"#ff2121"}},[t._v("直播价？")])])}],!1,null,"2644f787",null).exports}}]);
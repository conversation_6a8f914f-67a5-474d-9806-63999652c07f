(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[63101],{76399:function(t,a,s){t.exports=s.p+"static/img/app_01.47caff8.png"},99775:function(t,a,s){t.exports=s.p+"static/img/app_02.aa03807.png"},48273:function(t,a,s){t.exports=s.p+"static/img/app_03.fe34920.png"},19486:function(t,a,s){"use strict";s.r(a),s.d(a,{default:function(){return c}});var e=[function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"banner"},[e("img",{attrs:{src:s(76399),alt:""}}),t._v(" "),e("img",{attrs:{src:s(99775),alt:""}})])}],i=s(24388),o=s(59502),n={data:function(){return{rxData:[],licenseStatus:0,scrollload:!1,isload:!1,totalpage:0,pagecur:0,loadingmsg:"正在加载···",chooseHdid:"ZS201807231344002143",isshowToast:!1}},methods:{getDataList:function(){var t=this;this.putRequest("post","/app/layout/initExhibitionModulePage?sort=1",{merchantId:this.merchantId,limit:10,offset:this.pagecur,exhibitionId:"ZS201807231344002143"}).then((function(a){if("success"==a.data.status){t.isload=!1;var s=a.data.data.rows;t.rxData.push.apply(t.rxData,s),t.totalpage=a.data.data.pageCount,t.scrollload=!0,t.licenseStatus=a.data.data.licenseStatus}t.$nextTick((function(){o.Z.$emit("changeloading",!1)}))})).catch((function(t){}))},moveEvent:function(){var t=window.screen.height,a=document.querySelector("#ordervouchercq").scrollTop,s=document.querySelector("#ordervouchercq").scrollHeight;a>800?o.Z.$emit("showBtn",{isShow:!0,dom:"#ordervouchercq"}):o.Z.$emit("showBtn",{isShow:!1,dom:""}),s-a-400<=t&&this.scrollload&&(this.scrollload=!1,this.pagecur>=this.totalpage-1?(this.isload=!0,this.loadingmsg="无更多数据"):(this.isload=!0,this.loadingmsg="正在加载···",this.pagecur++,this.getDataList()));try{window.webkit.messageHandlers.hideKeyboard.postMessage({hide:"1"})}catch(t){}}},mounted:function(){this.getDataList()},activated:function(){this.setAppTitle("下单返券")},components:{temprow:i.Z}},c=(0,s(51900).Z)(n,(function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{attrs:{id:"ordervouchercq"},on:{touchmove:t.moveEvent}},[t._m(0),t._v(" "),e("div",{staticClass:"hot-content"},[e("img",{attrs:{src:s(48273),alt:""}}),t._v(" "),e("temprow",{attrs:{"goods-data":t.rxData,"license-status":t.licenseStatus||0}})],1),t._v(" "),e("div",{directives:[{name:"show",rawName:"v-show",value:t.isload,expression:"isload"}],staticClass:"loadingtips"},[t._v(t._s(t.loadingmsg))])])}),e,!1,null,"78063859",null).exports}}]);
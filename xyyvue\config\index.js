'use strict'
// Template version: 1.3.1
// see http://vuejs-templates.github.io/webpack for documentation.

const path = require('path')
const ip = require("ip").address();
module.exports = {
  dev: {
    // Paths
    assetsSubDirectory: 'static',
    assetsPublicPath: '/',
    proxyTable: {
      '/apiUrl': {
        // target: 'https://t-app.ybm100.com', //开发地址
        // target: 'https://stage-app.ybm100.com', //预发环境
        // target: 'https://app-v4.ybm100.com', //线上代理
        target: 'https://new-app.test.ybm100.com', //重构测试环境
        // target: 'http://***************:8015',//gelong
        // target: 'https://new-app.dev.ybm100.com', //重构正式环境
        // target:'https://b-app.ybm100.com',
        // target:'https://app2.test.ybm100.com',
        // target: 'http://*************:8080',
        // target: 'https://new-app.dev.ybm100.com',
        pathRewrite: { '^/apiUrl': '' },
        changeOrigin: true,
        logLevel: 'debug'
      }
    },

    // Various Dev Server settings
    host: ip, // can be overwritten by process.env.HOST
    port: 8080, // can be overwritten by process.env.PORT, if port is in use, a free one will be determined
    autoOpenBrowser: true,
    errorOverlay: true,
    notifyOnErrors: true,
    poll: false, // https://webpack.js.org/configuration/dev-server/#devserver-watchoptions-

    /**
     * Source Maps
     */

    // https://webpack.js.org/configuration/devtool/#development
    devtool: 'source-map',

    // If you have problems debugging vue-files in devtools,
    // set this to false - it *may* help
    // https://vue-loader.vuejs.org/en/options.html#cachebusting
    cacheBusting: true,
    cssSourceMap: true
  },

  build: {
    // Template for index.html
    index: path.resolve(__dirname, '../dist/index1.html'),
    // Paths
    assetsRoot: path.resolve(__dirname, '../dist'),
    assetsSubDirectory: 'static',
    //打包后的路径 /env-stage
    assetsPublicPath: '/static/xyyvue/dist/',

    /**
     * Source Maps
     */

    productionSourceMap: true,
    // https://webpack.js.org/configuration/devtool/#production
    devtool: 'cheap-module-source-map',

    // Gzip off by default as many popular static hosts such as
    // Surge or Netlify already gzip all static assets for you.
    // Before setting to `true`, make sure to:
    // npm install --save-dev compression-webpack-plugin
    productionGzip: false,
    productionGzipExtensions: ['js', 'css', 'vue', 'scss', 'json'],

    // Run the build command with an extra argument to
    // View the bundle analyzer report after build finishes:
    // `npm run build --report`
    // Set to `true` or `false` to always turn it on or off
    bundleAnalyzerReport: process.env.npm_config_report
  }
}

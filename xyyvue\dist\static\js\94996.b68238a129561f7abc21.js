(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[94996],{87809:function(t,s,a){"use strict";a.d(s,{p:function(){return i}});a(74916),a(15306),a(24603),a(39714);function i(t,s){if(!t)return"";t="string"==typeof t?t.replace(/-/g,"/"):t,t=new Date(t),/(y+)/.test(s)&&(s=s.replace(RegExp.$1,(t.getFullYear()+"").substr(4-RegExp.$1.length)));var a={"M+":t.getMonth()+1,"d+":t.getDate(),"h+":t.getHours(),"m+":t.getMinutes(),"s+":t.getSeconds()};for(var i in a)if(new RegExp("(".concat(i,")")).test(s)){var n=a[i]+"";s=s.replace(RegExp.$1,1===RegExp.$1.length?n:e(n))}return s}function e(t){return("00"+t).substr(t.length)}},62119:function(t,s,a){t.exports=a.p+"static/img/icon_empty.fff9bd9.png"},57947:function(t,s,a){"use strict";a.r(s),a.d(s,{default:function(){return o}});a(91058);var i=a(59502),e=a(87809),n={data:function(){return{isnodata:!1,nodatatip:"暂无已签署协议",protocollist:[],issign:null}},filters:{formatDate:function(t){return(0,e.p)(t,"yyyy-MM-dd hh:mm")}},methods:{changeTabs:function(t){this.issign=parseInt(t.target.getAttribute("tabidx")),this.getDataList()},getDataList:function(){var t=this;i.Z.$emit("changeloading",!0),this.putRequest("post","/app/agreement/getUserAgreementList",{merchantId:this.merchantId,signStatus:this.issign}).then((function(s){"success"==s.data.status&&(s.data.data.rows.length<=0?(t.isnodata=!0,0==t.issign?t.nodatatip="暂无未签署协议":1==t.issign&&(t.nodatatip="暂无已签署协议")):t.isnodata=!1,t.protocollist=[],t.protocollist=s.data.data.rows),t.$nextTick((function(){i.Z.$emit("changeloading",!1)}))})).catch((function(t){}))}},created:function(){},activated:function(){this.setAppTitle("协议管理");var t=this.$route.query.issign;this.issign=t,this.getDataList()}},o=(0,a(51900).Z)(n,(function(){var t=this,s=t.$createElement,i=t._self._c||s;return i("div",{attrs:{id:"protocollist"}},[i("div",{staticClass:"menubox",on:{click:function(s){return s.stopPropagation(),t.changeTabs.apply(null,arguments)}}},[i("div",{staticClass:"tab-item",class:{avtivated:1==t.issign},attrs:{tabidx:"1"}},[t._v("已签署")]),t._v(" "),i("div",{staticClass:"tab-item",class:{avtivated:0==t.issign},attrs:{tabidx:"0"}},[t._v("未签署")])]),t._v(" "),i("div",{staticClass:"protocollist-content"},t._l(t.protocollist,(function(s){return i("router-link",{key:s.id,staticClass:"protocol-item",attrs:{replace:"",to:"/protocoldetail?protocolId="+s.id+"&issign="+t.issign}},[i("div",{staticClass:"item-top"},[i("p",{staticClass:"title textellipsis"},[t._v("《"+t._s(s.name)+"》")]),t._v(" "),0==s.signStatus?i("p",{staticClass:"state state0"},[t._v("待签约")]):t._e(),t._v(" "),1==s.useAgrStatus?i("p",{staticClass:"state state1"},[t._v("生效中")]):t._e(),t._v(" "),3==s.useAgrStatus?i("p",{staticClass:"state state3"},[t._v("已冻结")]):t._e(),t._v(" "),4==s.useAgrStatus?i("p",{staticClass:"state state4"},[t._v("已过期")]):t._e()]),t._v(" "),i("div",{staticClass:"item-bottom"},[0!=s.signStatus?i("p",{staticClass:"bottom"},[t._v("完成进度："+t._s(s.currentStageProgress)+"%")]):t._e(),t._v(" "),0==s.signStatus?i("p",{staticClass:"bottom"}):t._e(),t._v(" "),i("p",{staticClass:"bottom"},[t._v(t._s(t._f("formatDate")(s.showTime)))])])])})),1),t._v(" "),t.isnodata?i("div",{staticClass:"nodata"},[i("img",{attrs:{src:a(62119),alt:""}}),t._v(" "),i("p",{staticClass:"tips"},[t._v(t._s(t.nodatatip))])]):t._e()])}),[],!1,null,"24983d4a",null).exports}}]);
(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[99528],{31186:function(t,a,s){t.exports=s.p+"static/img/banner.94e3928.png"},85276:function(t,a,s){t.exports=s.p+"static/img/tab.ee56545.png"},6949:function(t,a,s){"use strict";s.r(a),s.d(a,{default:function(){return u}});var e=[function(){var t=this.$createElement,a=this._self._c||t;return a("div",{staticClass:"banner"},[a("img",{attrs:{src:s(31186),alt:""}})])}],n=s(24388),i=s(59502),o={data:function(){return{hotData:[],goodsData:[]}},methods:{getDataList:function(t,a){var s=this;this.putRequest("post","/app/layout/initExhibitionModulePage?sort=1",{merchantId:this.merchantId,limit:1e3,offset:0,exhibitionId:t}).then((function(t){"success"==t.data.status&&(s[a]=t.data.data.rows,s[a].licenseStatus=t.data.data.licenseStatus),s.$nextTick((function(){i.Z.$emit("changeloading",!1)}))})).catch((function(t){}))},moveEvent:function(){document.querySelector("#sanjiuhunan").scrollTop>800?i.Z.$emit("showBtn",{isShow:!0,dom:"#sanjiuhunan"}):i.Z.$emit("showBtn",{isShow:!1,dom:""})}},mounted:function(){this.getDataList("ZS201902261702321875","hotData"),this.getDataList("ZS201902261703131826","goodsData")},activated:function(){this.setAppTitle("华润三九")},components:{temprow:n.Z}},u=(0,s(51900).Z)(o,(function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{attrs:{id:"sanjiuhunan"},on:{touchmove:t.moveEvent}},[t._m(0),t._v(" "),e("div",{staticClass:"prod"},[e("temprow",{attrs:{"goods-data":t.hotData,"license-status":t.hotData.licenseStatus||0}})],1),t._v(" "),e("div",{staticClass:"goodsdata"},[e("img",{attrs:{src:s(85276),alt:""}}),t._v(" "),e("temprow",{attrs:{"goods-data":t.goodsData,"license-status":t.goodsData.licenseStatus||0}})],1)])}),e,!1,null,"0eff3fa2",null).exports}}]);
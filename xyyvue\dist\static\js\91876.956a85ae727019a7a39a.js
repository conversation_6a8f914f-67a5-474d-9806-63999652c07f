(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[91876],{87809:function(t,e,s){"use strict";s.d(e,{p:function(){return a}});s(74916),s(15306),s(24603),s(39714);function a(t,e){if(!t)return"";t="string"==typeof t?t.replace(/-/g,"/"):t,t=new Date(t),/(y+)/.test(e)&&(e=e.replace(RegExp.$1,(t.getFullYear()+"").substr(4-RegExp.$1.length)));var s={"M+":t.getMonth()+1,"d+":t.getDate(),"h+":t.getHours(),"m+":t.getMinutes(),"s+":t.getSeconds()};for(var a in s)if(new RegExp("(".concat(a,")")).test(e)){var n=s[a]+"";e=e.replace(RegExp.$1,1===RegExp.$1.length?n:i(n))}return e}function i(t){return("00"+t).substr(t.length)}},62119:function(t,e,s){t.exports=s.p+"static/img/icon_empty.fff9bd9.png"},32500:function(t,e,s){t.exports=s.p+"static/img/icon_error.b1f22d6.png"},3993:function(t,e,s){"use strict";s.r(e),s.d(e,{default:function(){return o}});s(74916),s(15306);var a=s(59502),i=s(87809),n={data:function(){return{isnodata:!1,isError:!1,nodatatip:"",agreementzone:[],issign:1,scrollload:!0,isload:!1,loadingmsg:"正在加载···",pagecur:1,totalpage:0,showBtn:!1,startX:0,endX:0}},mounted:function(){document.querySelector("#agreementzone").addEventListener("scroll",this.moveEvent)},filters:{formatDate:function(t){return(0,i.p)(t,"yyyy-MM-dd hh:mm")}},methods:{changeTabs:function(t){this.issign=t,this.$router.replace({path:"/agreementzone",query:{issign:this.issign,merchantId:this.merchantId}}),this.pagecur=1,this.getDataList()},moveEvent:function(){var t=document.querySelector(".menubox").offsetTop,e=document.querySelector("#agreementzone").scrollTop;this.isfixed=e>=t,this.isarrow=!0;try{window.webkit.messageHandlers.hideKeyboard.postMessage({hide:"1"})}catch(t){}var s=window.screen.height;document.querySelector(".agreementzone-content").scrollHeight-e-400<=s&&this.scrollload&&(this.scrollload=!1,this.pagecur>=this.totalpage?(this.isload=!1,this.showBtn=!0):(this.isload=!0,this.loadingmsg="正在加载···",this.pagecur++,this.getDataList()))},getDataList:function(){var t=this;a.Z.$emit("changeloading",!0),this.putRequest("post","/app/threeInOne/agreement/selectPageList",{merchantId:this.merchantId,signStatus:this.issign,limit:10,offset:this.pagecur}).then((function(e){if("success"==e.data.status){1==t.pagecur&&(t.totalpage=e.data.data.pageCount,t.agreementzone=[]),t.isload=!1,t.scrollload=!0,!e.data.data.rows||e.data.data.rows.length<=0?(t.isnodata=!0,t.isError=!1,0==t.issign?t.nodatatip="暂无未签署协议":1==t.issign&&(t.nodatatip="暂无已签署协议")):t.isnodata=!1;var s=e.data.data.rows?e.data.data.rows:[];t.agreementzone.push.apply(t.agreementzone,s)}else a.Z.$emit("changeprompt",{dialog:e.data.msg,showprompt:!0});a.Z.$emit("changeloading",!1)})).catch((function(e){t.isload=!1,t.scrollload=!0,t.isError=!0,t.agreementzone=[],t.nodatatip="网络异常"}))}},watch:{$route:function(t,e){this.issign=this.$route.query.issign,window.location.reload()}},activated:function(){this.setAppTitle("签约特供"),this.issign=this.$route.query.issign?this.$route.query.issign:this.issign,this.getDataList();try{window.webkit.messageHandlers.setRightMenu.postMessage({title:"",action:""})}catch(t){}try{window.hybrid.setRightMenu("","")}catch(t){}}},o=(0,s(51900).Z)(n,(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{attrs:{id:"agreementzone"}},[a("div",{staticClass:"menubox"},[a("div",{staticClass:"tab-item",class:{avtivated:1==t.issign},attrs:{tabidx:"1"},on:{click:function(e){return e.stopPropagation(),t.changeTabs(1)}}},[t._v("\n      已签署\n    ")]),t._v(" "),a("div",{staticClass:"tab-item",class:{avtivated:0==t.issign},attrs:{tabidx:"0"},on:{click:function(e){return e.stopPropagation(),t.changeTabs(0)}}},[t._v("\n      未签署\n    ")])]),t._v(" "),a("div",{staticClass:"agreementzone-content",on:{touchmove:function(t){t.stopPropagation()}}},[t._l(t.agreementzone,(function(e){return a("router-link",{key:e.id,staticClass:"protocol-item",attrs:{to:"/agreementdetail?agreementId="+e.agreementId+"&issign="+t.issign+"&isstatus="+e.statesName+"&merchantId="+t.merchantId}},[a("div",{staticClass:"item-top"},[a("p",{staticClass:"title textellipsis"},[t._v("《"+t._s(e.name)+"》")]),t._v(" "),a("p",{staticClass:"state state0",class:{state4:"已过期"==e.statesName}},[t._v("\n          "+t._s(e.statesName)+"\n        ")])]),t._v(" "),a("div",{staticClass:"item-bottom"},[1==t.issign?a("p",{staticClass:"bottom"},[t._v("\n          完成进度："),"已冻结"!=e.statesName?a("span",[t._v(t._s(e.currentCount)+"/"+t._s(e.agreementAmount))]):a("span",[t._v("-")])]):a("p",{staticClass:"bottom"}),t._v(" "),1==t.issign?a("p",{staticClass:"bottom"},[t._v("\n          "+t._s(t._f("formatDate")(e.signTime))+"\n        ")]):t._e(),t._v(" "),0==t.issign?a("p",{staticClass:"bottom"},[t._v("\n          "+t._s(t._f("formatDate")(e.createTime))+"\n        ")]):t._e()])])})),t._v(" "),a("div",{staticClass:"temprow-box"},[a("div",{directives:[{name:"show",rawName:"v-show",value:t.isload,expression:"isload"}],staticClass:"loaing"},[t._v(t._s(t.loadingmsg))])])],2),t._v(" "),a("div",{staticClass:"nodata"},[a("img",{directives:[{name:"show",rawName:"v-show",value:t.isnodata,expression:"isnodata"}],attrs:{src:s(62119),alt:""}}),t._v(" "),a("img",{directives:[{name:"show",rawName:"v-show",value:t.isError,expression:"isError"}],attrs:{src:s(32500),alt:""}}),t._v(" "),t.agreementzone.length<=0?a("p",{staticClass:"tips"},[t._v(t._s(t.nodatatip))]):t._e()])])}),[],!1,null,"d70f0232",null).exports}}]);
(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[85273],{51033:function(t,e,a){t.exports=a.p+"static/img/app_01.947943b.png"},55839:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return n}});var s=[function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"banner"},[e("img",{attrs:{src:a(51033),alt:""}})])}],i=a(4942),c=(a(91058),a(24388)),o=a(59502),l={data:function(){var t;return t={iscur:0,temprowData:[],licenseStatus:0,isload:!1,loadingmsg:"正在加载···",tabData:[{hdid:"ZS201907151029101834",title:"特价推荐"},{hdid:"ZS201907151030002759",title:"新品特惠"},{hdid:"ZS201907151030355450",title:"临期特卖"}],isfixed:!1,chooseHdid:"ZS201907151029101834",tabIndex:0,scrollload:!0},(0,i.Z)(t,"isload",!1),(0,i.Z)(t,"loadingmsg","正在加载···"),(0,i.Z)(t,"pagecur",0),(0,i.Z)(t,"totalpage",0),t},methods:{tabitemclick:function(t){document.querySelector("#specialpricecq").scrollTop=document.querySelector(".checktab").offsetTop,this.translatetabs()},moveEvent:function(){var t=document.querySelector(".checktab").offsetTop,e=document.querySelector("#specialpricecq").scrollTop;this.isfixed=e>=t;var a=window.screen.height;document.querySelector("#specialpricecq").scrollHeight-e<=a&&this.scrollload&&(this.scrollload=!1,this.pagecur>=this.totalpage-1?this.isload=!1:(this.isload=!0,this.loadingmsg="正在加载···",this.pagecur++,this.getDatalist(this.chooseHdid,"temprowData")))},changetabs:function(t){this.tabIndex=parseInt(t.target.getAttribute("tabitem")),this.translatetabs()},translatetabs:function(){this.iscur=this.tabIndex,this.chooseHdid=this.tabData[this.tabIndex].hdid,document.querySelector("#specialpricecq").scrollTop=document.querySelector(".checktab").offsetTop;var t=this.tabIndex+1;t=t==this.tabData.length?0:t,this.skiptext=this.tabData[t].title,this.pagecur=0,this.getDatalist(this.chooseHdid,"temprowData")},getDatalist:function(t,e){var a=this;this.putRequest("post","/app/layout/initExhibitionModulePage?sort=1",{merchantId:this.merchantId,limit:10,offset:this.pagecur,exhibitionId:t}).then((function(t){if("success"==t.data.status){0==a.pagecur&&(a.totalpage=t.data.data.pageCount,a[e]=[]),a.isload=!1,a.scrollload=!0;var s=t.data.data.rows;a[e].push.apply(a[e],s),a.licenseStatus=t.data.data.licenseStatus}a.$nextTick((function(){o.Z.$emit("changeloading",!1)}))})).catch((function(t){}))}},mounted:function(){document.querySelector("#specialpricecq").addEventListener("scroll",this.moveEvent),this.getDatalist("ZS201907151029101834","temprowData")},components:{temprow:c.Z},activated:function(){this.setAppTitle("限时特价")}},n=(0,a(51900).Z)(l,(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{attrs:{id:"specialpricecq"}},[t._m(0),t._v(" "),a("div",{staticClass:"checktab"},[a("div",{staticClass:"tabs-box",class:{tabfixed:t.isfixed},on:{click:t.changetabs}},[a("ul",{staticClass:"tab-scroll"},t._l(t.tabData,(function(e,s){return a("li",{key:s,staticClass:"tab-item",class:{cur:t.iscur==s},attrs:{tabitem:s}},[t._v(t._s(e.title)),a("i")])})),0)])]),t._v(" "),a("div",{staticClass:"temprow-box"},[a("temprow",{attrs:{"goods-data":t.temprowData,"license-status":t.licenseStatus||0}}),t._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:t.isload,expression:"isload"}],staticClass:"loaing"},[t._v(t._s(t.loadingmsg))])],1)])}),s,!1,null,"31ad215e",null).exports}}]);
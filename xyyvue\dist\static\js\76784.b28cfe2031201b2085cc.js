(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[76784],{33220:function(t,s,a){t.exports=a.p+"static/img/app_01.a90b97a.png"},48391:function(t,s,a){"use strict";a.r(s),a.d(s,{default:function(){return r}});a(91058);var e=a(24388),i=a(59502),n={data:function(){return{iscur:0,isarrow:!0,templistData:[],temprowData:[],loadingmsg:"正在加载···",tabData:[],isfixed:!1,translateleft:0,tabIndex:0,scrollload:!0,isload:!1,startX:0,endX:0,descriptionData:[],lengthAll:0}},methods:{spreadTabs:function(){this.isarrow=!this.isarrow},touchstartTabs:function(t){this.startX=t.touches[0].pageX},touchmoveTabs:function(t){var s=t.touches[0].pageX-this.startX;this.startX=t.touches[0].pageX,this.translateleft+=s,this.translateleft>=0||this.lengthAll<=2?this.translateleft=0:this.translateleft<-95*(this.lengthAll-2)&&this.lengthAll>2&&(this.translateleft=-95*(this.lengthAll-2))},moveEvent:function(){var t=document.querySelector(".tab-box").offsetTop,s=document.querySelector("#oneprice").scrollTop;s>800&&i.Z.$emit("showBtn",{isShow:!0,dom:"#oneprice"}),this.isfixed=s>=t,this.isarrow=!0;try{window.webkit.messageHandlers.hideKeyboard.postMessage({hide:"1"})}catch(t){}var a=window.screen.height;document.querySelector(".highmargin-content").scrollHeight-s-400<=a&&this.scrollload&&(this.scrollload=!1)},changetabs:function(t){this.tabIndex=parseInt(t.target.getAttribute("tabitem")),(this.tabIndex||0==this.tabIndex)&&(this.translatetabs(),this.isarrow=!0)},translatetabs:function(){this.iscur=this.tabIndex,this.tabIndex>=2?this.translateleft=-95*(this.tabIndex-1):this.translateleft=0,document.querySelector("#oneprice").scrollTop=document.querySelector(".tab-box").offsetTop;var t=this.tabIndex+1;t=t==this.tabData.length?0:t},skipNexTab:function(){this.tabIndex++,this.tabIndex=this.tabIndex==this.tabData.length?0:this.tabIndex,this.translatetabs()},getDatashop:function(){var t=this;this.putRequest("post","/fixedPrice/showFixedPriceActivity",{merchantId:this.merchantId}).then((function(s){"success"==s.data.status&&(t.tabData=s.data.result,t.lengthAll=s.data.result.length),t.$nextTick((function(){i.Z.$emit("changeloading",!1),null!=window.sessionStorage.getItem("recordPos")&&(this.scrollPos=window.sessionStorage.getItem("recordPos"),document.querySelector("#app").scroll(0,this.scrollPos))}))})).catch((function(t){}))}},created:function(){this.getDatashop()},mounted:function(){document.querySelector("#oneprice").addEventListener("scroll",this.moveEvent)},components:{temprow:e.Z},activated:function(){this.setAppTitle("一口价专区")}},r=(0,a(51900).Z)(n,(function(){var t=this,s=t.$createElement,e=t._self._c||s;return e("div",{ref:"highmargin",attrs:{id:"oneprice"}},[e("div",{staticClass:"highmargin-content"},[e("img",{attrs:{src:a(33220),alt:""}}),t._v(" "),e("div",{staticClass:"tab-box"},[e("div",{staticClass:"tab-title",class:{tabfixed:t.isfixed},on:{click:t.changetabs}},[t.isarrow?e("div",{staticClass:"tab-section"},[e("ul",{staticClass:"tab-scroll",style:{transform:["translateX("+t.translateleft+"px)","-webkit-translateX("+t.translateleft+"px)"]},on:{touchstart:t.touchstartTabs,touchmove:t.touchmoveTabs}},t._l(t.tabData,(function(s,a){return e("li",{staticClass:"tab-title-item",class:{cur:t.iscur==a}},[s.description.match(/[^；]+(?=[；])/g)?e("span",{key:a,attrs:{tabitem:a}},[t._v("\n                  "+t._s(s.description.match(/[^；]+(?=[；])/g)[0])+"\n                ")]):e("span",{key:a,attrs:{tabitem:a}},[t._v("\n                   "+t._s(s.description)+"\n                ")])])})),0)]):t._e(),t._v(" "),t.isarrow?t._e():e("div",{staticClass:"tab-more-section"},[e("ul",{staticClass:"tab-more"},t._l(t.tabData,(function(s,a){return e("li",{staticClass:"tab-more-item",class:{cur:t.iscur==a}},[s.description.match(/[^；]+(?=[；])/g)?e("span",{key:a,attrs:{tabitem:a}},[t._v("\n                  "+t._s(s.description.match(/[^；]+(?=[；])/g)[0])+"\n                ")]):e("span",{key:a,attrs:{tabitem:a}},[t._v("\n                   "+t._s(s.description)+"\n                ")])])})),0)]),t._v(" "),e("div",{staticClass:"tab-arrow",class:{arrow:!t.isarrow},on:{click:function(s){return s.stopPropagation(),t.spreadTabs.apply(null,arguments)}}})])]),t._v(" "),e("div",{staticClass:"temprow-box"},[t._l(t.tabData,(function(s,a){return e("div",{key:a,class:{cur:t.iscur==a}},[e("temprow",{attrs:{"goods-data":s.skuDetailList.skuDtoList}})],1)})),t._v(" "),e("div",{directives:[{name:"show",rawName:"v-show",value:t.isload,expression:"isload"}],staticClass:"loaing"},[t._v(t._s(t.loadingmsg))])],2)])])}),[],!1,null,"fab43702",null).exports}}]);
(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[7945],{61255:function(t,a,s){"use strict";s(91058),s(82772),s(9653);var i=s(59502),e=s(67087);a.Z={props:["dataId","productNum","isSplit","medNum","isPack","bgcolor","btncolor"],data:function(){return{goodsId:"",issplit:"",productValue:"",mednum:"",ispack:!1}},watch:{dataId:function(t){this.goodsId=t,this.productValue=this.productNum?this.productNum:0,this.issplit=this.isSplit,this.mednum=this.medNum,this.ispack=this.isPack},isExtend:function(){i.Z.$emit("cart_isExtend",this.isExtend,this.goodsId)}},methods:{addProductCart:function(t){if(this.btn_hidden)this.postCartData(this.mednum);else{var a=t.target.getAttribute("edit-cate"),s=parseInt(t.currentTarget.children[1].value),e=parseInt(this.mednum);if("add"==a)s+=e;else{if("min"!=a)return;s=1==this.issplit?s-1:s-e}s=s>0?s:0,this.productValue=s,i.Z.$emit("listenToChildEvent",{isExtend:this.isExtend,dataId:this.goodsId,productValue:this.productValue}),this.postCartData(s)}},inputCart:function(t){var a=parseInt(t.target.value);a=a>=0?a:0,this.productValue=a,i.Z.$emit("listenToChildEvent",{isExtend:this.isExtend,dataId:this.goodsId,productValue:this.productValue}),this.postCartData(t.target.value)},androidclick:function(t){if(navigator.userAgent.indexOf("Android")>=0){t.target.setAttribute("readonly",!0),t.target.blur();var a=t.target.value;i.Z.$emit("showandorideditcomp",{id:this.goodsId,val:a,showandoridedit:!0,split:this.issplit,medpack:this.mednum,package:this.ispack})}},postCartData:function(t){var a=this;if(t>0){var s=1==this.ispack?{merchantId:this.merchantId,amount:t,packageId:this.goodsId}:{merchantId:this.merchantId,amount:t,skuId:this.goodsId};this.putRequest("post","/app/changeCart",s).then((function(s){if("success"===s.data.status){a.btn_hidden&&i.Z.$emit("changeprompt",{dialog:"已添加到购物车!",showprompt:!0}),Number(s.data.data.qty)&&(0,e.M0)("h5_page_CommodityDetails_o",{commodityId:a.goodsId,real:1}),s.data.data.qty!=t&&(a.productValue=s.data.data.qty),null!=s.data.dialog&&(20==s.data.dialog.style?i.Z.$emit("changesureDialog",{dialogmsg:s.data.dialog.msg,showsureDialog:!0}):i.Z.$emit("changeprompt",{dialog:s.data.dialog.msg,showprompt:!0})),s.errorMsg&&i.Z.$emit("changeprompt",{dialog:s.errorMsg,showprompt:!0});try{var r=1==a.ispack?{proid:a.goodsId,pronum:a.productValue,isAdd:1,type:1}:{proid:a.goodsId,pronum:a.productValue,isAdd:1};window.webkit.messageHandlers.addPlanNumber.postMessage(r)}catch(t){}try{1==a.ispack?window.hybrid.addPlanNumber(a.goodsId,a.productValue,1,1):window.hybrid.addPlanNumber(a.goodsId,a.productValue,1)}catch(t){}}else a.productValue=0,i.Z.$emit("listenToChildEvent",{isExtend:a.isExtend,dataId:a.goodsId,productValue:a.productValue}),s.data.errorMsg?i.Z.$emit("changeprompt",{dialog:s.data.errorMsg,showprompt:!0}):s.data.msg?i.Z.$emit("changesureDialog",{dialogmsg:s.data.msg,showsureDialog:!0}):i.Z.$emit("changeprompt",{dialog:s.data.dialog.msg,showprompt:!0})})).catch((function(t){}))}else{var r=1==this.ispack?{merchantId:this.merchantId,packageIds:this.goodsId}:{merchantId:this.merchantId,ids:this.goodsId};this.putRequest("post","/app/batchRemoveProductFromCart",r).then((function(t){if("success"==t.data.status){a.btn_hidden&&i.Z.$emit("changeprompt",{dialog:"已添从购物车删除!",showprompt:!0}),a.productValue=0;try{var s=1==a.ispack?{proid:a.goodsId,pronum:0,isAdd:1,type:1}:{proid:a.goodsId,pronum:0,isAdd:1};window.webkit.messageHandlers.addPlanNumber.postMessage(s)}catch(t){}try{1==a.ispack?window.hybrid.addPlanNumber(a.goodsId,0,1,1):window.hybrid.addPlanNumber(a.goodsId,0,1)}catch(t){}}}))}}},created:function(){this.goodsId=this.dataId,this.productValue=this.productNum?this.productNum:0,this.issplit=this.isSplit,this.mednum=this.medNum,this.ispack=this.isPack}}},11932:function(t){t.exports="data:image/png;base64,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"},19048:function(t,a,s){t.exports=s.p+"static/img/cqh_1.3a6f96c.jpg"},51109:function(t,a,s){t.exports=s.p+"static/img/cqh_2.c06f040.jpg"},25583:function(t,a,s){t.exports=s.p+"static/img/cqh_3.7ff8e8c.jpg"},84786:function(t,a,s){"use strict";s.d(a,{Z:function(){return c}});s(91058),s(47042),s(41539),s(68309),s(91038),s(78783),s(82526),s(41817),s(32165),s(66992),s(33948);var i=s(61255),e=s(59502);function r(t,a){var s="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!s){if(Array.isArray(t)||(s=function(t,a){if(!t)return;if("string"==typeof t)return n(t,a);var s=Object.prototype.toString.call(t).slice(8,-1);"Object"===s&&t.constructor&&(s=t.constructor.name);if("Map"===s||"Set"===s)return Array.from(t);if("Arguments"===s||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(s))return n(t,a)}(t))||a&&t&&"number"==typeof t.length){s&&(t=s);var i=0,e=function(){};return{s:e,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:e}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,o=!0,c=!1;return{s:function(){s=s.call(t)},n:function(){var t=s.next();return o=t.done,t},e:function(t){c=!0,r=t},f:function(){try{o||null==s.return||s.return()}finally{if(c)throw r}}}}function n(t,a){(null==a||a>t.length)&&(a=t.length);for(var s=0,i=new Array(a);s<a;s++)i[s]=t[s];return i}var o={props:{btn_hidden:{default:!1},from_tab:{type:String,default:""},goodItem:Object,big:{type:Boolean,default:!1}},computed:{isExtend:function(){return 0!=this.productValue}},mounted:function(){var t=this;e.Z.$on("listenToChildEvent",(function(a){var s=a.isExtend,i=a.dataId;parseInt(i)===parseInt(t.goodsId)&&(s||(t.productValue=0))})),e.Z.$on("update_cart",(function(a){var s,i=a.length,e=0,n=r(a);try{for(n.s();!(s=n.n()).done;){var o=s.value;if(o.item.id===t.goodsId){t.productValue=o.item.amount;break}e++}}catch(t){n.e(t)}finally{n.f()}i===e&&(t.productValue=0)}))},watch:{isExtend:function(){e.Z.$emit("cart_isExtend",this.isExtend,this.goodsId)}},mixins:[i.Z]},c=(0,s(51900).Z)(o,(function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"buybtn",class:{bigStyle:t.big},attrs:{"data-id":t.goodsId,"is-slipt":t.issplit,"med-pack-num":t.mednum,"is-extend":t.isExtend},on:{click:function(a){return a.stopPropagation(),a.preventDefault(),t.addProductCart.apply(null,arguments)}}},[t.btn_hidden?t._e():s("span",{directives:[{name:"show",rawName:"v-show",value:t.isExtend,expression:"isExtend"}],staticClass:"min",attrs:{"edit-cate":"min"}}),t._v(" "),t.btn_hidden?t._e():s("input",{directives:[{name:"show",rawName:"v-show",value:t.isExtend,expression:"isExtend"},{name:"model",rawName:"v-model",value:t.productValue,expression:"productValue"}],staticClass:"input-val",class:"input-val"+t.goodsId,attrs:{"edit-cate":"edit",type:"tel"},domProps:{value:t.productValue},on:{change:t.inputCart,click:function(a){return a.preventDefault(),a.stopPropagation(),t.androidclick.apply(null,arguments)},input:function(a){a.target.composing||(t.productValue=a.target.value)}}}),t._v(" "),s("span",{directives:[{name:"show",rawName:"v-show",value:t.isExtend,expression:"isExtend"}],staticClass:"add addshow",attrs:{"edit-cate":"add"}},[t._v("+")]),t._v(" "),s("span",{directives:[{name:"show",rawName:"v-show",value:!t.isExtend,expression:"!isExtend"}],staticClass:"plus",attrs:{"edit-cate":"add"}})])}),[],!1,null,"3c07d9fa",null).exports},46825:function(t,a,s){"use strict";s.d(a,{Z:function(){return r}});s(56977),s(54678);var i=s(59502),e={data:function(){return{isExtend:!1,productNum:""}},filters:{fixedtwo:function(t){return parseFloat(t).toFixed(2)}},props:["dataId","uniformPrice","suggestPrice","grossMargin","status"],watch:{productNum:function(){}},methods:{showExtend:function(){var t=this;i.Z.$on("listenToChildEvent",(function(a){t.dataId==a.dataId&&(t.isExtend=a.isExtend,t.productNum=a.productValue)}))}},mounted:function(){this.showExtend()}},r=(0,s(51900).Z)(e,(function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"priceBox",attrs:{"data-id":t.dataId,productNum:t.productNum,uniformPrice:t.uniformPrice,suggestPrice:t.suggestPrice,grossMargin:t.grossMargin,status:t.status}},[t.uniformPrice?s("div",{staticClass:"control-price"},[s("span",{staticClass:"text"},[t._v("控销价")]),s("span",{staticClass:"jiage"},[t._v("¥"+t._s(t._f("fixedtwo")(t.uniformPrice)))])]):t._e(),t._v(" "),t.suggestPrice?s("div",{staticClass:"purchase-price"},[s("span",{staticClass:"text"},[t._v("零售价")]),s("span",{staticClass:"jiage"},[t._v("¥"+t._s(t._f("fixedtwo")(t.suggestPrice)))])]):t._e(),t._v(" "),t.grossMargin?s("div",{staticClass:"gross-margin"},[2!=t.status?s("span",{directives:[{name:"show",rawName:"v-show",value:t.isExtend,expression:"isExtend"}],staticClass:"maolilv"},[t._v("...")]):t._e(),t._v(" "),2==t.status?s("span",{staticClass:"maolilv"},[t._v("...")]):t._e(),t._v(" "),2!=t.status?s("div",{directives:[{name:"show",rawName:"v-show",value:!t.isExtend,expression:"!isExtend"}]},[s("span",{staticClass:"text"},[t._v("(毛利率")]),s("span",{staticClass:"jiage"},[t._v(t._s(t.grossMargin)+")")])]):t._e()]):t._e()])}),[],!1,null,"481a41d3",null).exports},36468:function(t,a,s){"use strict";s.d(a,{Z:function(){return e}});var i={data:function(){return{isExtend:!1}},mounted:function(){},mixins:[s(61255).Z]},e=(0,s(51900).Z)(i,(function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"buybtn",style:{borderColor:t.bgcolor},attrs:{bgcolr:t.bgcolor,"data-id":t.goodsId,"is-slipt":t.issplit,"med-pack-num":t.mednum,"is-extend":t.isExtend},on:{click:function(a){return a.stopPropagation(),a.preventDefault(),t.addProductCart.apply(null,arguments)}}},[s("span",{staticClass:"min",style:{backgroundColor:t.bgcolor},attrs:{"edit-cate":"min"}}),t._v(" "),s("input",{directives:[{name:"model",rawName:"v-model",value:t.productValue,expression:"productValue"}],staticClass:"input-val",class:"input-val"+t.goodsId,attrs:{"edit-cate":"edit",type:"tel"},domProps:{value:t.productValue},on:{change:t.inputCart,click:function(a){return a.preventDefault(),a.stopPropagation(),t.androidclick.apply(null,arguments)},input:function(a){a.target.composing||(t.productValue=a.target.value)}}}),t._v(" "),s("span",{staticClass:"add",style:{backgroundColor:t.bgcolor},attrs:{"edit-cate":"add"}})])}),[],!1,null,"2f0de2f5",null).exports},89095:function(t,a,s){"use strict";s.d(a,{Z:function(){return c}});s(9653),s(56977),s(54678),s(74916),s(15306),s(54747),s(47042),s(69600);var i=s(84786),e=s(46825),r=s(67087),n=s(59502),o={props:{goodsData:Array,licenseStatus:Number,from_tab:{type:String,default:""},trackingStatus:{type:Boolean,default:!0},isLive:Number},data:function(){return{datalist:[],imgUrl:"",isExtend:"",dataId:""}},computed:{detailUrl:function(){return n.Z.detailBaseUrl}},filters:{fixedtwo:function(t){return parseFloat(t).toFixed(2)},replace:function(t){return t.replace(/-/g,".")}},components:{cartBtn:i.Z,priceBox:e.Z},methods:{getIdList:function(t){var a=[];t&&t.length>0?(t.forEach((function(t,s){a.push(t.id)})),this.getPrice(t,a)):this.datalist=t},splitData:function(t){for(var a=[],s=0,i=t.length;s<i;s+=20)a.push(t.slice(s,s+20));return a},getPrice:function(t,a){var s=this,i=this.splitData(a),e=0;i.forEach((function(a,r){s.putRequest("post","/app/marketing/discount/satisfactoryInHandPrice",{merchantId:s.merchantId,skuIds:a.join(",")}).then((function(a){if(s.$nextTick((function(){n.Z.$emit("changeloading",!1)})),"success"==a.data.status){var r=a.data.data;r&&r.length>0&&r.forEach((function(a,s){t.forEach((function(t,s){a.skuId==t.id&&(t.zheHouPrice=a.price)}))}))}++e===i.length&&t&&t.length>0&&(s.datalist=[],t.forEach((function(t,a){s.datalist.push(t)})))})).catch((function(a){s.datalist=t}))}))},showExtend:function(t){this.isExtend=t.isExtend,this.dataId=t.dataId,this.productNum=t.productValue}},mounted:function(){var t=this.goodsData;this.getIdList(t),this.imgUrl=this.imgBaseUrl,(0,r.dA)("h5_page_ListPage_ExpStatic",{list:t},"",this.trackingStatus)},watch:{goodsData:function(t,a){this.getIdList(t),(0,r.dA)("h5_page_ListPage_ExpStatic",{list:t},"",this.trackingStatus)}}},c=(0,s(51900).Z)(o,(function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("div",{staticClass:"templist"},t._l(t.datalist,(function(a,e){return i("div",{key:e,staticClass:"templist-item"},[i("a",{staticClass:"img-box",attrs:{href:t.detailUrl+"product_id="+a.id}},[i("div",{staticClass:"images"},[i("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.imgUrl+"/ybm/product/min/"+a.imageUrl,expression:"imgUrl + '/ybm/product/min/' + item.imageUrl"}],staticClass:"pic",attrs:{alt:""}}),t._v(" "),a.markerUrl&&!a.reducePrice?i("img",{staticClass:"activity-token",attrs:{src:t.imgUrl+a.markerUrl,alt:""}}):t._e(),t._v(" "),a.markerUrl&&a.reducePrice&&(1!=a.isControl||1==a.isPurchase&&1==a.isControl)?i("div",{staticClass:"mark-text"},[i("img",{attrs:{src:t.imgUrl+a.markerUrl,alt:""}}),t._v(" "),i("h4",[t._v("药采节价："+t._s(a.reducePrice))])]):t._e(),t._v(" "),a.activityTag&&a.activityTag.tagNoteBackGroupUrl?i("div",{staticClass:"biaoqian"},[i("img",{attrs:{src:t.imgUrl+a.activityTag.tagNoteBackGroupUrl,alt:""}}),t._v(" "),i("div",{staticClass:"tejia806"},[i("span",{staticClass:"discount"},[t._v("\n              "+t._s(a.activityTag.timeStr)+"\n            ")]),t._v(" "),i("div",{staticClass:"labelBox"},t._l(a.activityTag.skuTagNotes,(function(a,s){return i("span",{key:s,staticClass:"price806",style:{color:"#"+a.textColor}},[t._v(t._s(a.text))])})),0)])]):t._e(),t._v(" "),a.activityTag&&a.activityTag.tagNoteBackGroupUrl&&2==+a.activityTag.sourceType?i("div",{staticClass:"biaoqian shop-biaoqian"},[i("img",{attrs:{src:a.activityTag.tagNoteBackGroupUrl,alt:""}}),t._v(" "),a.activityTag.customTopNote?i("div",{staticClass:"customTopNote"},[t._v(t._s(a.activityTag.customTopNote))]):t._e(),t._v(" "),i("div",[i("span",{staticClass:"shop-discount"},[t._v(" "+t._s(a.activityTag.timeStr)+" ")]),t._v(" "),t._l(a.activityTag.skuTagNotes,(function(a,s){return i("span",{key:s,staticClass:"shop-text"},[t._v(t._s(a.text))])}))],2),t._v(" "),a.activityTag.customBottomNote?i("div",{staticClass:"customBottomNote"},[t._v(t._s(a.activityTag.customBottomNote))]):t._e()]):t._e()]),t._v(" "),2==a.status?i("div",{staticClass:"sold-out posmiddle"},[t._v("售罄")]):t._e()]),t._v(" "),i("div",{staticClass:"med-mesg"},[i("a",{attrs:{href:t.detailUrl+"product_id="+a.id}},[i("div",{staticClass:"commonName"},[a.activityTag&&a.activityTag.tagUrl?i("span",{staticClass:"bq-hgj"},[i("img",{attrs:{src:t.imgUrl+a.activityTag.tagUrl,alt:""}})]):t._e(),t._v(" "),1==a.agent?i("span",{staticClass:"dujia"},[t._v("独家")]):t._e(),t._v(" "),i("span",{staticClass:"name"},[t._v(t._s(a.commonName))]),t._v(" "),i("span",{staticClass:"spec"},[t._v("/"+t._s(a.spec))])]),t._v(" "),i("div",{staticClass:"factory"},[i("div",{staticClass:"chang"}),t._v(" "),i("div",{staticClass:"name"},[t._v(t._s(a.manufacturer))])]),t._v(" "),i("div",{staticClass:"effect"},[i("div",{staticClass:"xiao"}),t._v(" "),a.nearEffect?i("div",{staticClass:"name"},[t._v("\n            "+t._s(t._f("replace")(a.nearEffect))+"\n          ")]):t._e(),t._v("\n          /\n          "),a.nearEffect?i("div",{staticClass:"name"},[t._v("\n            "+t._s(t._f("replace")(a.farEffect))+"\n          ")]):t._e(),t._v(" "),i("div",{staticClass:"midPack"},[t._v(t._s(a.mediumPackageTitle))])])]),t._v(" "),i("div",{staticClass:"price-btn-box"},[1===t.licenseStatus||5===t.licenseStatus?i("div",{staticClass:"qualifications"},[t._v("\n          价格认证资质可见\n        ")]):1!=a.isPurchase&&1==a.isControl?i("div",{staticClass:"nobuy"},[t._v("\n          暂无购买权限\n        ")]):i("div",{staticClass:"price-numer"},["true"==a.isOEM&&0==a.signStatus||0==a.showAgree?i("span",{staticClass:"price-permission"},[t._v("价格签署协议可见")]):2==a.priceType?i("i",[t._v("\n            ¥"+t._s(t._f("fixedtwo")(a.skuPriceRangeList[0].price))+"~"+t._s(t._f("fixedtwo")(a.skuPriceRangeList[a.skuPriceRangeList.length-1].price))+"\n          ")]):i("div",{staticClass:"pricewapper"},[i("div",{staticClass:"price-box clearfixed"},[t.isLive?i("div",{staticClass:"price-two",staticStyle:{width:"83%"}},[t._m(0,!0)]):i("div",{staticClass:"price-two"},[i("p",{staticClass:"ellipsis-box"},[i("span",[t._v("¥"+t._s(t._f("fixedtwo")(a.fob)))]),t._v(" "),a.zheHouPrice?i("span",{staticClass:"zhekou"},[t._v(t._s(a.zheHouPrice))]):t._e()])]),t._v(" "),i("div",{staticClass:"btn-box"},[2!==a.status&&1!==t.licenseStatus&&5!==t.licenseStatus?i("div",["true"==a.isOEM&&0==a.signStatus||0==a.showAgree?i("div"):i("div",[1!=a.isControl||1==a.isPurchase&&1==a.isControl?i("cartBtn",{attrs:{"is-pack":!1,"data-id":a.id,"is-split":a.isSplit,"product-num":a.cartProductNum,"med-num":a.mediumPackageNum}}):t._e()],1)]):i("img",{staticClass:"bell",attrs:{src:s(11932),alt:""}})])]),t._v(" "),i("div",{staticClass:"control-hid clearfixed"},[1!==t.licenseStatus&&5!==t.licenseStatus?i("div",{staticClass:"control-box"},["true"!=a.isOEM&&(1!=a.isControl||1==a.isPurchase&&1==a.isControl)||"true"==a.isOEM&&1==a.signStatus?i("priceBox",{attrs:{uniformPrice:a.uniformPrice,suggestPrice:a.suggestPrice,grossMargin:a.grossMargin}}):t._e()],1):t._e()])])])]),t._v(" "),i("div",{staticClass:"label-box",class:{"label-box-hide":(!a.tagList||a.tagList.length<=0)&&!(1==a.isUsableMedicalStr)}},[a.tagList&&a.tagList.length>0?i("div",{staticClass:"labels"},t._l(a.tagList.slice(0,3),(function(a,s){return i("span",{key:s,class:"span"+a.uiType},[t._v(t._s(a.name))])})),0):t._e()]),t._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:0!==a.isThirdCompany,expression:"item.isThirdCompany !== 0"}],staticClass:"enter-shop"},[a.companyName?i("i",{staticClass:"icon"}):t._e(),t._v(" "),a.companyName?i("span",[i("span",{staticClass:"companyName"},[t._v(t._s(a.companyName))])]):t._e()])])])})),0)}),[function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("p",[s("span",{staticStyle:{"font-size":"0.28rem",color:"#ff2121"}},[t._v("直播价？")])])}],!1,null,"2644f787",null).exports},45013:function(t,a,s){"use strict";s.r(a),s.d(a,{default:function(){return c}});var i=[function(){var t=this.$createElement,a=this._self._c||t;return a("div",{staticClass:"banner"},[a("img",{attrs:{src:s(19048),alt:""}})])}],e=s(89095),r=s(36468),n=s(59502),o={data:function(){return{goodData:[],dataId1:"54270",dataId2:"48275",proNum1:0,proNum2:0,bgcolor:"#ff5e2a",licenseStatus:0}},computed:{detailUrl:function(){return n.Z.detailBaseUrl}},methods:{getDataList:function(t){var a=this;this.putRequest("post","/app/layout/initExhibitionModulePage?sort=1",{merchantId:this.merchantId,limit:1e3,offset:0,exhibitionId:t}).then((function(t){"success"==t.data.status&&(a.goodData=t.data.data.rows,a.licenseStatus=t.data.data.licenseStatus),a.$nextTick((function(){n.Z.$emit("changeloading",!1)}))})).catch((function(t){}))}},mounted:function(){this.getDataList("ZS201811081040574490")},activated:function(){this.setAppTitle("曹清华专场")},components:{templist:e.Z,cartBtn:r.Z}},c=(0,s(51900).Z)(o,(function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("div",{attrs:{id:"cqhpage"}},[t._m(0),t._v(" "),i("div",{staticClass:"pro"},[i("div",{staticClass:"proitem"},[i("a",{staticStyle:{display:"block"},attrs:{href:t.detailUrl+"product_id=54270"}},[i("img",{staticClass:"imgpro",attrs:{src:s(51109),alt:""}})]),t._v(" "),i("div",{staticClass:"btn-box1"},[i("cartBtn",{attrs:{bgcolor:t.bgcolor,"is-pack":!1,"data-id":t.dataId1,"is-split":1,"product-num":t.proNum1,"med-num":1}})],1),t._v(" "),i("a",{staticStyle:{display:"block"},attrs:{href:t.detailUrl+"product_id=48275"}},[i("img",{attrs:{src:s(25583),alt:""}})]),t._v(" "),i("div",{staticClass:"btn-box2"},[i("cartBtn",{attrs:{bgcolor:t.bgcolor,"is-pack":!1,"data-id":t.dataId2,"is-split":1,"product-num":t.proNum2,"med-num":1}})],1)])])])}),i,!1,null,"cac39ae0",null).exports}}]);
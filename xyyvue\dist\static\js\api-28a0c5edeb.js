"use strict";

function _asyncToGenerator(fn) {
  return function() {
    var gen = fn.apply(this, arguments);
    return new Promise(function(resolve, reject) {
      function step(key, arg) {
        try {
          var info = gen[key](arg);
          var value = info.value;
        } catch (error) {
          reject(error);
          return;
        }
        if (info.done) {
          resolve(value);
        } else {
          return Promise.resolve(value).then(
            function(value) {
              step("next", value);
            },
            function(err) {
              step("throw", err);
            }
          );
        }
      }
      return step("next");
    });
  };
}

var api = {
  getTurnplate: function getTurnplate(branchCode) {
    var _this = this;

    return new Promise(
      (function() {
        var _ref = _asyncToGenerator(
          /*#__PURE__*/ regeneratorRuntime.mark(function _callee(
            resolve,
            reject
          ) {
            var branchCode;
            return regeneratorRuntime.wrap(
              function _callee$(_context) {
                while (1) {
                  switch ((_context.prev = _context.next)) {
                    case 0:
                      _context.next = 2;
                      return _this.getLocalCode();

                    case 2:
                      branchCode = _context.sent;

                      if (branchCode === null) reject("请求不到域");

                      $.ajax({
                        type: "get",
                        url:
                          lucky.testApi +
                          "luckdraw/findLuckDraw?branchCode=" +
                          branchCode,
                        success: function success(response) {
                          resolve(response);
                        },
                        error: function error(err) {
                          reject("请求失败");
                        }
                      });

                    case 5:
                    case "end":
                      return _context.stop();
                  }
                }
              },
              _callee,
              _this
            );
          })
        );

        return function(_x, _x2) {
          return _ref.apply(this, arguments);
        };
      })()
    );
  },
  getLuckCount: function getLuckCount() {
    var _this2 = this;

    return new Promise(
      (function() {
        var _ref2 = _asyncToGenerator(
          /*#__PURE__*/ regeneratorRuntime.mark(function _callee2(
            resolve,
            reject
          ) {
            var branchCode, merchantId;
            return regeneratorRuntime.wrap(
              function _callee2$(_context2) {
                while (1) {
                  switch ((_context2.prev = _context2.next)) {
                    case 0:
                      _context2.next = 2;
                      return _this2.getLocalCode();

                    case 2:
                      branchCode = _context2.sent;

                      if (branchCode === null) reject("请求不到域");
                      merchantId = localStorage.getItem("merchantId");

                      $.ajax({
                        type: "get",
                        url:
                          lucky.testApi +
                          "luckdraw/queryLuckCount?merchantId=" +
                          (merchantId ? merchantId : 0) +
                          "&branchCode=" +
                          branchCode,
                        success: function success(response) {
                          resolve(response);
                        },
                        error: function error(err) {
                          reject("请求失败");
                        }
                      });

                    case 6:
                    case "end":
                      return _context2.stop();
                  }
                }
              },
              _callee2,
              _this2
            );
          })
        );

        return function(_x3, _x4) {
          return _ref2.apply(this, arguments);
        };
      })()
    );
  },
  getLuck: function getLuck() {
    var _this3 = this;

    return new Promise(
      (function() {
        var _ref3 = _asyncToGenerator(
          /*#__PURE__*/ regeneratorRuntime.mark(function _callee3(
            resolve,
            reject
          ) {
            var branchCode, merchantId;
            return regeneratorRuntime.wrap(
              function _callee3$(_context3) {
                while (1) {
                  switch ((_context3.prev = _context3.next)) {
                    case 0:
                      _context3.next = 2;
                      return _this3.getLocalCode();

                    case 2:
                      branchCode = _context3.sent;

                      if (branchCode === null) reject("请求不到域");
                      merchantId = localStorage.getItem("merchantId");

                      $.ajax({
                        type: "get",
                        url:
                          lucky.testApi +
                          "luckdraw/luck?branchCode=" +
                          branchCode +
                          "&merchantId=" +
                          merchantId,
                        success: function success(response) {
                          resolve(response);
                        },
                        error: function error(err) {
                          reject("请求失败");
                        }
                      });

                    case 6:
                    case "end":
                      return _context3.stop();
                  }
                }
              },
              _callee3,
              _this3
            );
          })
        );

        return function(_x5, _x6) {
          return _ref3.apply(this, arguments);
        };
      })()
    );
  },
  getUserLuck: function getUserLuck(pageNum) {
    var _this4 = this;

    return new Promise(
      (function() {
        var _ref4 = _asyncToGenerator(
          /*#__PURE__*/ regeneratorRuntime.mark(function _callee4(
            resolve,
            reject
          ) {
            var branchCode, merchantId;
            return regeneratorRuntime.wrap(
              function _callee4$(_context4) {
                while (1) {
                  switch ((_context4.prev = _context4.next)) {
                    case 0:
                      _context4.next = 2;
                      return _this4.getLocalCode();

                    case 2:
                      branchCode = _context4.sent;

                      if (branchCode === null) reject("请求不到域");
                      merchantId = localStorage.getItem("merchantId");

                      pageNum = pageNum || 1;
                      $.ajax({
                        type: "get",
                        url:
                          lucky.testApi +
                          "luckdraw/queryUserLuck?merchantId=" +
                          merchantId +
                          "&pageNum=" +
                          pageNum +
                          "&pageSize=10",
                        success: function success(response) {
                          resolve(response);
                        },
                        error: function error(err) {
                          reject("请求失败");
                        }
                      });

                    case 7:
                    case "end":
                      return _context4.stop();
                  }
                }
              },
              _callee4,
              _this4
            );
          })
        );

        return function(_x7, _x8) {
          return _ref4.apply(this, arguments);
        };
      })()
    );
  },
  addLuckCount: function addLuckCount() {
    var _this5 = this;

    return new Promise(
      (function() {
        var _ref5 = _asyncToGenerator(
          /*#__PURE__*/ regeneratorRuntime.mark(function _callee5(
            resolve,
            reject
          ) {
            var branchCode, merchantId;
            return regeneratorRuntime.wrap(
              function _callee5$(_context5) {
                while (1) {
                  switch ((_context5.prev = _context5.next)) {
                    case 0:
                      _context5.next = 2;
                      return _this5.getLocalCode();

                    case 2:
                      branchCode = _context5.sent;

                      if (branchCode === null) reject("请求不到域");
                      merchantId = localStorage.getItem("merchantId");

                      $.ajax({
                        type: "post",
                        url: lucky.testApi + "luckdraw/updateLuckCount",
                        data: {
                          merchantId: merchantId,
                          branchCode: branchCode
                        },
                        success: function success(response) {
                          resolve(response);
                        },
                        error: function error(err) {
                          reject("请求失败");
                        }
                      });

                    case 6:
                    case "end":
                      return _context5.stop();
                  }
                }
              },
              _callee5,
              _this5
            );
          })
        );

        return function(_x9, _x10) {
          return _ref5.apply(this, arguments);
        };
      })()
    );
  },
  getLocalCode: function getLocalCode() {
    return new Promise(function(resolve, reject) {
      var count = 0;
      function getCode() {
        var _this6 = this;

        var branchCode = localStorage.getItem("branchCode");
        if (!branchCode) {
          if (count <= 4) {
            setTimeout(
              (function() {
                var _ref6 = _asyncToGenerator(
                  /*#__PURE__*/ regeneratorRuntime.mark(function _callee6(_) {
                    return regeneratorRuntime.wrap(
                      function _callee6$(_context6) {
                        while (1) {
                          switch ((_context6.prev = _context6.next)) {
                            case 0:
                              count++;
                              getCode();

                            case 2:
                            case "end":
                              return _context6.stop();
                          }
                        }
                      },
                      _callee6,
                      _this6
                    );
                  })
                );

                return function(_x11) {
                  return _ref6.apply(this, arguments);
                };
              })(),
              300
            );
          } else {
            resolve(null);
          }
        } else {
          resolve(branchCode);
        }
      }
      getCode();
    });
  },
  getLocalMerchantId: function getLocalMerchantId() {
    return new Promise(function(resolve, reject) {
      var count = 0;
      function getCode() {
        var _this7 = this;

        var merchantId = localStorage.getItem("merchantId");
        if (!merchantId) {
          if (count <= 3) {
            setTimeout(
              (function() {
                var _ref7 = _asyncToGenerator(
                  /*#__PURE__*/ regeneratorRuntime.mark(function _callee7(_) {
                    return regeneratorRuntime.wrap(
                      function _callee7$(_context7) {
                        while (1) {
                          switch ((_context7.prev = _context7.next)) {
                            case 0:
                              count++;
                              getCode();

                            case 2:
                            case "end":
                              return _context7.stop();
                          }
                        }
                      },
                      _callee7,
                      _this7
                    );
                  })
                );

                return function(_x12) {
                  return _ref7.apply(this, arguments);
                };
              })(),
              200
            );
          } else {
            resolve(null);
          }
        } else {
          resolve(merchantId);
        }
      }
      getCode();
    });
  },
  getBranchCode: (function() {
    var _ref8 = _asyncToGenerator(
      /*#__PURE__*/ regeneratorRuntime.mark(function _callee8() {
        var merchantId;
        return regeneratorRuntime.wrap(
          function _callee8$(_context8) {
            while (1) {
              switch ((_context8.prev = _context8.next)) {
                case 0:
                  _context8.next = 2;
                  return this.getLocalMerchantId();

                case 2:
                  merchantId = _context8.sent;

                  $.ajax({
                    type: "post",
                    url: lucky.testApi + "app/getBranchCode",
                    data: { merchantId: merchantId },
                    headers: {
                      terminalType: 3,
                      version: 520
                    },
                    success: function success(response) {
                      localStorage.setItem("branchCode", response.data);
                      api.getListMoreWays();
                    },
                    error: function error(err) {
                      console.log(err);
                    }
                  });

                case 4:
                case "end":
                  return _context8.stop();
              }
            }
          },
          _callee8,
          this
        );
      })
    );

    function getBranchCode() {
      return _ref8.apply(this, arguments);
    }

    return getBranchCode;
  })(),
  getListMoreWays: function getListMoreWays(branchCode) {
    var _this9 = this;
    return new Promise(
      (function() {
        var _ref9 = _asyncToGenerator(
          /*#__PURE__*/ regeneratorRuntime.mark(function _callee9(
            resolve,
            reject
          ) {
            var branchCode;
            return regeneratorRuntime.wrap(
              function _callee9$(_context9) {
                while (1) {
                  switch ((_context9.prev = _context9.next)) {
                    case 0:
                      _context9.next = 2;
                      return _this9.getLocalCode();

                    case 2:
                      branchCode = _context9.sent;

                      if (branchCode === null) reject("请求不到域");
                      // localStorage.setItem("merchantId", 1500123373);
                      var merchantId = localStorage.getItem("merchantId");
                      $.ajax({
                        headers: {
                          terminalType: navigator.userAgent.indexOf("iPhone") >= 0 ? 2 : 1,
                          merchantId: merchantId || 0
                        },
                        type: "get",
                        url:
                          lucky.testApi +
                          "luckdraw/listMoreWays?branchCode=" +
                          branchCode,
                        success: function success(response) {
                          if (response.success) {
                            var data = response.data;
                            var list = data.list;
                            var str = '';
                            list.forEach(function(item) {
                              str = str + '<div class="one"><img src="../static/img/'+(item.type === 1 ? 'login_icon.png' : ((item.type === 2 || item.type === 4) ? 'order_icon.png' : 'cart_bg.png'))+'" class="icon"/><p>'+(item.name)+'</p><div data-type="'+item.type+'" data-link="'+item.action+'" data-status="'+item.status+'" class="oppoBtn '+(item.type !== 1 ? (item.status !== 2 ? 'oppoOn' : '') : '')+'">'+(item.type === 1 ? '已获得' : item.status === 2 ? '已获得' : ((item.type === 2 || item.type === 4) ? '去下单' : '去加购'))+'</div></div>'
                            })
                            $(".getMorePopList").html(str);
                            $(".oppoBtn").on("click", function(e) {
                              var target = e.currentTarget;
                              var type = target.getAttribute('data-type');
                              var status = target.getAttribute('data-status');
                              var link = target.getAttribute('data-link');
                              if (type && type === '1') {
                                return false;
                              }
                              if (status && status === '2') return false;
                              if (link) {
                                location.href = link;
                              } else {
                                var isIphone = navigator.userAgent.indexOf('iPhone') >= 0;
                                if (isIphone) {
                                  // setupWebViewJavascriptBridge(function(bridge) {
                                  //   bridge.callHandler('JSCloseWebView', {'key':'value'}, function responseCallback(responseData) {
                                  //     // console.log("JS received response:", responseData)
                                  //   })
                                  // })
                                  window.parent.postMessage({
                                    message: 'iosCloseWebView'
                                  }, '*');
                                } else {
                                  if (window.hybrid) {
                                    if (window.hybrid.closeWebView) {
                                      window.hybrid.closeWebView();
                                    }
                                  }
                                }
                              }
                              // if (type && (type === '2' || (type === '4' && !link))) {
                              //   if (status && status === '2') return false;
                              //   // function setupWebViewJavascriptBridge(callback) {
                              //   //   if (window.WebViewJavascriptBridge) { return callback(WebViewJavascriptBridge); }
                              //   //   if (window.WVJBCallbacks) { return window.WVJBCallbacks.push(callback); }
                              //   //   window.WVJBCallbacks = [callback];
                              //   //   var WVJBIframe = document.createElement('iframe');
                              //   //   WVJBIframe.style.display = 'none';
                              //   //   WVJBIframe.src = 'https://__bridge_loaded__';
                              //   //   document.documentElement.appendChild(WVJBIframe);
                              //   //   setTimeout(function() { document.documentElement.removeChild(WVJBIframe) }, 0)
                              //   // }
                              //   var isIphone = navigator.userAgent.indexOf('iPhone') >= 0;
                              //   if (isIphone) {
                              //     // setupWebViewJavascriptBridge(function(bridge) {
                              //     //   bridge.callHandler('JSCloseWebView', {'key':'value'}, function responseCallback(responseData) {
                              //     //     // console.log("JS received response:", responseData)
                              //     //   })
                              //     // })
                              //     window.parent.postMessage({
                              //       message: 'iosCloseWebView'
                              //     }, '*');
                              //   } else {
                              //     if (window.hybrid) {
                              //       if (window.hybrid.closeWebView) {
                              //         window.hybrid.closeWebView();
                              //       }
                              //     }
                              //   }
                              //   return false;
                              // }
                              // if (type && (type === '3' || type === '4')) {
                              //   if (status && status === '2') return false;
                              //   if (link) {
                              //     location.href = link;
                              //   }
                              // }
                            })
                          }
                          resolve(response);
                        },
                        error: function error(err) {
                          reject("请求失败");
                        }
                      });

                    case 5:
                    case "end":
                      return _context9.stop();
                  }
                }
              },
              _callee9,
              _this9
            );
          })
        );
        return function(_x13, _x14) {
          return _ref9.apply(this, arguments);
        };
      })()
    );
  }
};

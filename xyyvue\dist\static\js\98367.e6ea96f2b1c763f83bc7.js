(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[98367],{34553:function(t,e,i){"use strict";var s=i(82109),n=i(42092).findIndex,a=i(51223),c="findIndex",v=!0;c in[]&&Array(1).findIndex((function(){v=!1})),s({target:"Array",proto:!0,forced:v},{findIndex:function(t){return n(this,t,arguments.length>1?arguments[1]:void 0)}}),a(c)},13295:function(t){t.exports="data:image/png;base64,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"},32571:function(t){t.exports="data:image/png;base64,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"},98367:function(t,e,i){"use strict";i.r(e),i.d(e,{default:function(){return c}});var s=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"quan"},[s("div",{staticClass:"left-quan"},[s("img",{attrs:{src:i(13295),alt:""}})]),t._v(" "),s("div",{staticClass:"right-quan"},[s("img",{attrs:{src:i(32571),alt:""}})])])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"can-get other-type"},[i("div",{staticClass:"no-get-text"},[t._v("\n                                        已经领取\n                                    ")])])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"rules-index"},[i("div",{staticClass:"index"},[t._v("\n                                1\n                            ")])])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("li",[i("div",{staticClass:"rules-index"},[i("div",{staticClass:"index"},[t._v("\n                                2\n                            ")])]),t._v(" "),i("div",{staticClass:"rules-text"},[t._v("\n                            在活动期间提交订单，并成功支付订单，以实际支付金额作为累计金额\n                        ")])])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("li",[i("div",{staticClass:"rules-index"},[i("div",{staticClass:"index"},[t._v("\n                                3\n                            ")])]),t._v(" "),i("div",{staticClass:"rules-text"},[t._v("\n                            兑换优惠券有效期：2019年10月8日-2019年10月10日\n                        ")])])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("li",[i("div",{staticClass:"rules-index"},[i("div",{staticClass:"index"},[t._v("\n                                4\n                            ")])]),t._v(" "),i("div",{staticClass:"rules-text"},[t._v("\n                            每张优惠券仅限兑换领取1次\n                        ")])])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("li",[i("div",{staticClass:"rules-index"},[i("div",{staticClass:"index"},[t._v("\n                                5\n                            ")])]),t._v(" "),i("div",{staticClass:"rules-text"},[t._v("\n                            使用兑换券的订单如果发生金额退款等行为致交易未成功，兑换券将不退回用户帐户。\n                        ")])])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("li",[i("div",{staticClass:"rules-index"},[i("div",{staticClass:"index"},[t._v("\n                                6\n                            ")])]),t._v(" "),i("div",{staticClass:"rules-text"},[t._v("\n                            该活动最终解释权归小药药公司所有\n                        ")])])}],n=(i(21249),i(47042),i(19601),i(56977),i(34553),i(59502)),a={name:"index",created:function(){var t=this;this.putRequest("post","/heavenlyVoucher/index",{merchantId:this.merchantId}).then((function(e){var i=e.data;if("success"===i.status)if(t.cur_money=i.data.totalAcount,t.boos_num=i.data.num4HadGet,t.cur_time=i.data.now,i.data.now<=i.data.startTime?t.boos_num=0:t.count_down(i.data.endTime),i.data.voucherCenterList.length){var s=i.data.voucherCenterList.slice(0,2).map((function(t){return Object.assign({had_get:!1},t)}));t.voucher_list=s}else n.Z.$emit("changeprompt",{dialog:"暂无优惠券！",showprompt:!0});else n.Z.$emit("changeprompt",{dialog:"暂无优惠券！",showprompt:!0})})).catch((function(t){n.Z.$emit("changeprompt",{dialog:"暂无优惠券！",showprompt:!0})}))},data:function(){return{day:"14",hour:"00",min:"00",sec:"00",timer:null,boos_num:null,cur_money:null,voucher_list:[],cur_time:null,get_voucher_progress:!1}},filters:{fixTwo:function(t){return t.toFixed(2)}},methods:{get_voucher:function(t){var e=this;this.get_voucher_progress||(this.get_voucher_progress=!0,this.putRequest("post","/heavenlyVoucher/receiveVoucher",{merchantId:this.merchantId,voucherTemplateId:t}).then((function(i){var s=i.data;if(e.get_voucher_progress=!1,"success"===s.status){var a=e.voucher_list.findIndex((function(e){return t===e.templateId}));e.voucher_list[a].had_get=!0,n.Z.$emit("changeprompt",{dialog:"领券成功！",showprompt:!0})}else n.Z.$emit("changeprompt",{dialog:s.msg,showprompt:!0})})).catch((function(t){e.get_voucher_progress=!1,n.Z.$emit("changeprompt",{dialog:"领券失败！",showprompt:!0})})))},count_down:function(t){var e=this;this.timer=setInterval((function(){var i=new Date(e.cur_time),s=new Date(t).getTime()-i.getTime();s>0?(e.day=Math.floor(s/864e5),e.hour=Math.floor(s/36e5%24),e.min=Math.floor(s/6e4%60),e.sec=Math.floor(s/1e3%60),e.day=e.day<10?"0"+e.day:e.day,e.hour=e.hour<10?"0"+e.hour:e.hour,e.min=e.min<10?"0"+e.min:e.min,e.sec=e.sec<10?"0"+e.sec:e.sec,e.cur_time=e.cur_time+1e3):(e.day="00",e.hour="00",e.min="00",e.sec="00",clearInterval(e.timer))}),1e3)}}},c=(0,i(51900).Z)(a,(function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{attrs:{id:"nbVoucher"}},[i("div",{staticClass:"topBg"}),t._v(" "),i("div",{staticClass:"big-container"},[i("div",{staticClass:"limit-time"},[i("div",{staticClass:"time"},[i("div",{staticClass:"time-container"},[i("div",{staticClass:"time-box"},[t._v("\n                        "+t._s(t.day)+"\n                    ")]),t._v(" "),i("div",[t._v("天")]),t._v(" "),i("div",{staticClass:"time-box"},[t._v("\n                        "+t._s(t.hour)+"\n                    ")]),t._v(" "),i("div",[t._v("时")]),t._v(" "),i("div",{staticClass:"time-box"},[t._v("\n                        "+t._s(t.min)+"\n                    ")]),t._v(" "),i("div",[t._v("分")]),t._v(" "),i("div",{staticClass:"time-box"},[t._v("\n                        "+t._s(t.sec)+"\n                    ")]),t._v(" "),i("div",[t._v("秒")])])]),t._v(" "),i("div",{staticClass:"time-bottom"})]),t._v(" "),i("div",{staticClass:"had-get-container"},[i("div",{staticClass:"had-get"},[t._v("\n                已经有"),i("span",{staticClass:"boos_num"},[t._v(t._s(t.boos_num))]),t._v("个老板领取了\n            ")])]),t._v(" "),i("div",{staticClass:"voucher-container"},[t._m(0),t._v(" "),i("div",{staticClass:"voucher"},[i("div",{staticClass:"voucher-content"},[i("div",{staticClass:"voucher-item"},t._l(t.voucher_list,(function(e){return i("div",{staticClass:"voucher-common"},[i("div",{staticClass:"money"},[i("div",{staticClass:"use-time"},[t._v("\n                                    有效期：10.8-10.10\n                                ")]),t._v(" "),i("div",{staticClass:"use-money"},[i("div",{staticClass:"rmb"},[t._v("\n                                        ￥\n                                    ")]),t._v(" "),i("div",[t._v("\n                                        "+t._s(e.moneyInVoucher)+"\n                                    ")])])]),t._v(" "),i("div",{staticClass:"validity-date"},[t._v("\n                                满"+t._s(e.minMoneyToEnable)+"元可用\n                            ")]),t._v(" "),!1===e.had_get?i("div",[1===e.type?i("div",{staticClass:"can-get other-type"},[i("div",{staticClass:"no-get-text"},[t._v("\n                                        已经领取\n                                    ")])]):2===e.type?i("div",{staticClass:"can-get other-type"},[i("div",{staticClass:"can-get-text"},[t._v("\n                                        还差"+t._s(t._f("fixTwo")(e.restMoney))+"元领取\n                                    ")])]):3===e.type?i("div",{staticClass:"can-get",on:{click:function(i){return t.get_voucher(e.templateId)}}},[i("div",{staticClass:"can-get-text"},[t._v("\n                                        立即领取\n                                    ")]),t._v(" "),i("div",{staticClass:"click-in"})]):4===e.type?i("div",{staticClass:"can-get other-type"},[i("div",{staticClass:"can-get-text"},[t._v("\n                                        已抢光\n                                    ")])]):t._e()]):i("div",[t._m(1,!0)])])})),0),t._v(" "),i("div",{staticClass:"voucher-bottom"},[i("span",{staticClass:"left-icon"}),t._v("\n                        当前累计实付金额 "),i("span",{staticClass:"real-money"},[t._v(t._s(t.cur_money))]),t._v("元\n                        "),i("span",{staticClass:"right-icon"})])])])]),t._v(" "),i("div",{staticClass:"voucher-rules"},[i("div",{staticClass:"voucher-rules-container"},[i("ul",{staticClass:"voucher-rules-list"},[i("li",[t._m(2),t._v(" "),i("div",{staticClass:"rules-text"},[t._v("\n                            在活动期间（9月16日-9月29日）累计实付满\n                            "),t.voucher_list[0]?i("span",[t._v("\n                                "+t._s(t.voucher_list[0].minMoneyToReceive)+"\n                            ")]):t._e(),t._v("\n                            元可兑换\n                            "),t.voucher_list[0]?i("span",[t._v("\n                            "+t._s(t.voucher_list[0].moneyInVoucher)+"\n                               ")]):t._e(),t._v("\n                            元券，累计实付满\n                            "),t.voucher_list[1]?i("span",[t._v("\n                                "+t._s(t.voucher_list[1].minMoneyToReceive)+"\n                            ")]):t._e(),t._v("\n                            元可兑换\n                            "),t.voucher_list[1]?i("span",[t._v("\n                            "+t._s(t.voucher_list[1].moneyInVoucher)+"\n                               ")]):t._e(),t._v("\n                            元券\n                        ")])]),t._v(" "),t._m(3),t._v(" "),t._m(4),t._v(" "),t._m(5),t._v(" "),t._m(6),t._v(" "),t._m(7)])])])])])}),s,!1,null,"8ee3d8a4",null).exports}}]);
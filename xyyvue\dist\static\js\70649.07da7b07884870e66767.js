(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[70649],{74492:function(t,a,e){t.exports=e.p+"static/img/yibaotop.00ec108.jpg"},70649:function(t,a,e){"use strict";e.r(a),e.d(a,{default:function(){return o}});var s=[function(){var t=this.$createElement,a=this._self._c||t;return a("div",{staticClass:"banner"},[a("img",{attrs:{src:e(74492),alt:""}})])}],i=(e(91058),e(24388)),n=e(59502),c={data:function(){return{listDetail:!1,searchList:!1,iscur:0,temprowData:[],licenseStatus:0,isload:!1,loadingmsg:"正在加载···",tabData:[{hdid:"ZS201810151711151275",title:"心脑血管"},{hdid:"ZS201810151704361673",title:"慢病用药"},{hdid:"ZS201810151701042798",title:"风湿骨痛"},{hdid:"ZS201810151702214280",title:"呼吸系统"},{hdid:"ZS201810151703213182",title:"抗菌消炎"},{hdid:"ZS201810151710147680",title:"消化系统"},{hdid:"ZS201810151709185831",title:"五官用药"},{hdid:"ZS201810151718002821",title:"滋补养生"},{hdid:"ZS201810151705342709",title:"皮肤用药"},{hdid:"ZS201810151706462937",title:"其他热销"}],isfixed:!1,chooseHdid:"ZS201810151711151275",tabIndex:0,scrollload:!0,pagecur:0,totalpage:0,skiptext:"心脑血管",showBtn:!1,imgNumber:0,imgSrc:["心脑血管","慢病用药","风湿骨痛","呼吸系统","抗菌消炎","消化系统","五官用药","滋补养生","皮肤用药","其他热销"],searchKey:"",branchCode:"",searchListData:[],detailData:{}}},created:function(){var t=this;this.putRequest("post","/app/getBranchCode",{merchantId:this.merchantId}).then((function(a){"success"==a.data.status&&(t.branchCode=a.data.data)})).catch((function(t){}))},methods:{tabitemclick:function(t){document.querySelector("#seasonalsales").scrollTop=document.querySelector(".checktab").offsetTop,this.translatetabs()},skipNexTab:function(){this.tabIndex++,this.tabIndex=this.tabIndex==this.tabData.length?0:this.tabIndex,this.translatetabs()},moveEvent:function(){var t=document.querySelector(".checktab").offsetTop,a=document.querySelector("#seasonalsales").scrollTop;this.isfixed=a>=t,a>800&&n.Z.$emit("showBtn",{isShow:!0,dom:"#seasonalsales"});var e=window.screen.height;document.querySelector("#seasonalsales").scrollHeight-a<=e&&this.scrollload&&(this.scrollload=!1,this.pagecur>=this.totalpage-1?(this.isload=!1,this.showBtn=!0):(this.isload=!0,this.loadingmsg="正在加载···",this.pagecur++,this.getDatalist(this.chooseHdid,"temprowData")))},changetabs:function(t){this.tabIndex=parseInt(t.target.getAttribute("tabitem")),this.translatetabs(),this.imgNumber=this.tabIndex-0},translatetabs:function(){this.iscur=this.tabIndex,this.chooseHdid=this.tabData[this.tabIndex].hdid,document.querySelector("#seasonalsales").scrollTop=document.querySelector(".checktab").offsetTop;var t=this.tabIndex+1;t=t==this.tabData.length?0:t,this.skiptext=this.tabData[t].title,this.pagecur=0,this.showBtn=!1,this.getDatalist(this.chooseHdid,"temprowData"),this.imgNumber=this.tabIndex-0},getDatalist:function(t,a){var e=this;this.putRequest("post","/app/layout/initExhibitionModulePage?sort=1",{merchantId:this.merchantId,limit:10,offset:this.pagecur,exhibitionId:t}).then((function(t){if("success"==t.data.status){t.data.data.rows.length<3&&(e.showBtn=!0),0==e.pagecur&&(e.totalpage=t.data.data.pageCount,e[a]=[]),e.isload=!1,e.scrollload=!0;var s=t.data.data.rows;e[a].push.apply(e[a],s),e.licenseStatus=t.data.data.licenseStatus}e.$nextTick((function(){n.Z.$emit("changeloading",!1)}))})).catch((function(t){}))},searchData:function(t,a){this.searchKey&&13==a.keyCode&&(this.$refs.mysearch.blur(),this.$router.push({path:"/searchpage",query:{searchKey:this.searchKey,ybm_title:"医保支付查询价格"}}))},seeDetail:function(t){this.detailData=t,this.listDetail=!this.listDetail}},mounted:function(){document.querySelector("#seasonalsales").addEventListener("scroll",this.moveEvent),this.getDatalist("ZS201810151711151275","temprowData")},components:{temprow:i.Z},activated:function(){},computed:{detailUrl:function(){return n.Z.detailBaseUrl}}},o=(0,e(51900).Z)(c,(function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{attrs:{id:"seasonalsales"}},[t._m(0),t._v(" "),e("div",{staticClass:"search-box"},[e("p",{staticClass:"p-title"},[t._v("医保目录查询")]),t._v(" "),e("div",{staticClass:"div-box"},[e("span"),t._v(" "),e("input",{directives:[{name:"model",rawName:"v-model",value:t.searchKey,expression:"searchKey"}],ref:"mysearch",staticClass:"input-kw",attrs:{type:"searchInput",autocomplete:"off",name:"baike-search",placeholder:"请输入药品名称/关键词",id:"searchInput"},domProps:{value:t.searchKey},on:{keypress:function(a){return t.searchData("",a)},input:function(a){a.target.composing||(t.searchKey=a.target.value)}}})])]),t._v(" "),e("div",{staticClass:"checktab"},[e("div",{staticClass:"tabs-box",class:{tabfixed:t.isfixed},on:{click:t.changetabs}},[e("ul",{staticClass:"tab-scroll"},t._l(t.tabData,(function(a,s){return e("li",{key:s,staticClass:"tab-item",class:{cur:t.iscur==s},attrs:{tabitem:s}},[t._v(t._s(a.title)),e("i")])})),0)])]),t._v(" "),e("div",{staticClass:"temprow-box"},[e("temprow",{attrs:{"goods-data":t.temprowData,"license-status":t.licenseStatus||0}}),t._v(" "),e("div",{directives:[{name:"show",rawName:"v-show",value:t.isload,expression:"isload"}],staticClass:"loaing"},[t._v(t._s(t.loadingmsg))])],1),t._v(" "),e("div",{directives:[{name:"show",rawName:"v-show",value:t.showBtn,expression:"showBtn"}],staticClass:"highmargin-btn-box"},[e("div",{staticClass:"highmargin-btn",on:{click:t.skipNexTab}},[t._v("点击跳转至“"+t._s(t.skiptext)+"”")])]),t._v(" "),e("div",{directives:[{name:"show",rawName:"v-show",value:t.searchList,expression:"searchList"}],staticClass:"search-tip"},[e("div",{staticClass:"search-top"},[e("div",{staticClass:"search-input"},[e("span"),t._v(" "),e("input",{directives:[{name:"model",rawName:"v-model",value:t.searchKey,expression:"searchKey"}],ref:"mysearchone",staticClass:"input-kw",attrs:{type:"searchInput",autocomplete:"off",name:"baike-search",placeholder:"请输入药品名称/关键词"},domProps:{value:t.searchKey},on:{keypress:function(a){return t.searchData(1,a)},input:function(a){a.target.composing||(t.searchKey=a.target.value)}}})]),t._v(" "),e("div",{on:{click:function(a){t.searchList=!1}}},[t._v("取消")])]),t._v(" "),e("div",{staticClass:"search-list"},[t.searchListData&&t.searchListData.length>0?e("ul",t._l(t.searchListData,(function(a,s){return e("li",{key:s,on:{click:function(e){return e.stopPropagation(),t.seeDetail(a)}}},[e("p",{staticClass:"title"},[t._v(t._s(a.inviteTendersCommonName?a.inviteTendersCommonName:""))]),t._v(" "),e("p",[t._v("医保支付价格："+t._s(a.medicalInsuranceAmount?a.medicalInsuranceAmount:""))]),t._v(" "),e("p",[t._v("处方分类："+t._s(a.drugClassification?a.drugClassification:""))]),t._v(" "),e("p",[t._v("转化比："+t._s(a.conversionRatio?a.conversionRatio:""))]),t._v(" "),e("p",[t._v("规格："+t._s(a.spec?a.spec:""))]),t._v(" "),e("p",[t._v("生产企业："+t._s(a.manufacturer))]),t._v(" "),a.skuId?e("p",{staticClass:"color"},[t._v("药帮忙价："+t._s(a.fob?a.fob:""))]):t._e(),t._v(" "),a.skuId?e("p",{staticClass:"p-btn"},[e("a",{attrs:{href:t.detailUrl+"product_id="+a.skuId},on:{click:function(t){t.stopPropagation()}}},[t._v("去购买")])]):t._e()])})),0):e("div",{staticClass:"noList"},[e("p",[t._v("暂无数据~~")])])])]),t._v(" "),t.listDetail?e("div",{staticClass:"search-detail"},[e("div",{staticClass:"inner"},[e("p",{staticClass:"title"},[e("span",[t._v(t._s(t.detailData.inviteTendersCommonName?t.detailData.inviteTendersCommonName:""))]),t._v(" "),e("span",{staticClass:"close",on:{click:function(a){t.listDetail=!1}}},[t._v("X")])]),t._v(" "),e("p",[e("span",[t._v("医保编码："+t._s(t.detailData.medicalInsuranceCode?t.detailData.medicalInsuranceCode:""))])]),t._v(" "),e("p",[e("span",[t._v("医保支付价："+t._s(t.detailData.medicalInsuranceAmount?t.detailData.medicalInsuranceAmount:""))])]),t._v(" "),e("p",[e("span",[t._v("处方分类："+t._s(t.detailData.drugClassification?t.detailData.drugClassification:""))])]),t._v(" "),e("p",[t._v("转化比："+t._s(t.detailData.conversionRatio?t.detailData.conversionRatio:""))]),t._v(" "),e("p",[t._v("规格："+t._s(t.detailData.spec?t.detailData.spec:""))]),t._v(" "),e("p",[t._v("生产企业："+t._s(t.detailData.manufacturer?t.detailData.manufacturer:""))]),t._v(" "),e("p",[t._v("医保中文名称："+t._s(t.detailData.medicalInsuranceChName?t.detailData.medicalInsuranceChName:""))]),t._v(" "),e("p",[t._v("英文名称："+t._s(t.detailData.medicalInsuranceEnName?t.detailData.medicalInsuranceEnName:""))]),t._v(" "),e("p",[t._v("医保文件剂型："+t._s(t.detailData.medicalInsuranceDosageForm?t.detailData.medicalInsuranceDosageForm:""))]),t._v(" "),e("p",[t._v("招标药品号："+t._s(t.detailData.inviteTendersDrugCode?t.detailData.inviteTendersDrugCode:""))]),t._v(" "),e("p",[t._v("商品名："+t._s(t.detailData.productName?t.detailData.productName:""))]),t._v(" "),e("p",[t._v("剂型："+t._s(t.detailData.dosageForm?t.detailData.dosageForm:""))]),t._v(" "),e("p",[t._v("单位："+t._s(t.detailData.unit?t.detailData.unit:""))]),t._v(" "),e("p",[t._v("包装材质："+t._s(t.detailData.packingMaterial?t.detailData.packingMaterial:""))]),t._v(" "),e("p",[t._v("一致性评价标志："+t._s(t.detailData.isConsistency?t.detailData.isConsistency:""))]),t._v(" "),e("p",[t._v("国家带量采购中选："+t._s(t.detailData.purchaseSelection?t.detailData.purchaseSelection:""))]),t._v(" "),e("p",[t._v("备注："+t._s(t.detailData.remarks?t.detailData.remarks:""))])])]):t._e()])}),s,!1,null,"46ced17d",null).exports}}]);
(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[74881],{92737:function(t,a,i){t.exports=i.p+"static/img/app_01.5f75669.jpg"},88517:function(t,a,i){t.exports=i.p+"static/img/app_02.53f2db2.jpg"},23034:function(t,a,i){t.exports=i.p+"static/img/app_03.2f9b7eb.jpg"},59916:function(t,a,i){t.exports=i.p+"static/img/app_04.93a9e8a.jpg"},36468:function(t,a,i){"use strict";i.d(a,{Z:function(){return e}});var s={data:function(){return{isExtend:!1}},mounted:function(){},mixins:[i(61255).Z]},e=(0,i(51900).Z)(s,(function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("div",{staticClass:"buybtn",style:{borderColor:t.bgcolor},attrs:{bgcolr:t.bgcolor,"data-id":t.goodsId,"is-slipt":t.issplit,"med-pack-num":t.mednum,"is-extend":t.isExtend},on:{click:function(a){return a.stopPropagation(),a.preventDefault(),t.addProductCart.apply(null,arguments)}}},[i("span",{staticClass:"min",style:{backgroundColor:t.bgcolor},attrs:{"edit-cate":"min"}}),t._v(" "),i("input",{directives:[{name:"model",rawName:"v-model",value:t.productValue,expression:"productValue"}],staticClass:"input-val",class:"input-val"+t.goodsId,attrs:{"edit-cate":"edit",type:"tel"},domProps:{value:t.productValue},on:{change:t.inputCart,click:function(a){return a.preventDefault(),a.stopPropagation(),t.androidclick.apply(null,arguments)},input:function(a){a.target.composing||(t.productValue=a.target.value)}}}),t._v(" "),i("span",{staticClass:"add",style:{backgroundColor:t.bgcolor},attrs:{"edit-cate":"add"}})])}),[],!1,null,"2f0de2f5",null).exports},12429:function(t,a,i){"use strict";i.r(a),i.d(a,{default:function(){return c}});var s=[function(){var t=this.$createElement,a=this._self._c||t;return a("div",{staticClass:"banner"},[a("img",{attrs:{src:i(92737),alt:""}})])},function(){var t=this.$createElement,a=this._self._c||t;return a("a",{staticStyle:{display:"block"},attrs:{href:"ybmpage://productdetail?product_id=249"}},[a("img",{attrs:{src:i(88517),alt:""}})])},function(){var t=this.$createElement,a=this._self._c||t;return a("a",{staticStyle:{display:"block"},attrs:{href:"ybmpage://productdetail?product_id=3914"}},[a("img",{attrs:{src:i(23034),alt:""}})])},function(){var t=this.$createElement,a=this._self._c||t;return a("a",{staticStyle:{display:"block"},attrs:{href:"ybmpage://productdetail?product_id=1383"}},[a("img",{attrs:{src:i(59916),alt:""}})])}],e=i(24388),n=i(36468),o=i(59502),r={data:function(){return{hotData:[],proNum:0,proNum2:0,proNum3:0,bgcolor:"#ff7f0e"}},methods:{getDataList:function(t,a){var i=this;this.putRequest("post","/app/layout/initExhibitionModulePage?sort=1",{merchantId:this.merchantId,limit:1e3,offset:0,exhibitionId:t}).then((function(t){"success"==t.data.status&&(i[a]=t.data.data.rows,i.licenseStatus=t.data.data.licenseStatus),i.$nextTick((function(){o.Z.$emit("changeloading",!1)}))})).catch((function(t){}))}},mounted:function(){},activated:function(){this.setAppTitle("小葵花专场")},components:{temprow:e.Z,cartBtn:n.Z}},c=(0,i(51900).Z)(r,(function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("div",{attrs:{id:"tianshilipage"}},[t._m(0),t._v(" "),i("div",{staticClass:"good-pills"},[t._m(1),t._v(" "),i("div",{staticClass:"btn-box"},[i("cartBtn",{attrs:{bgcolor:t.bgcolor,"is-pack":!1,"data-id":249,"is-split":0,"product-num":t.proNum,"med-num":20}})],1)]),t._v(" "),i("div",{staticClass:"good-pills"},[t._m(2),t._v(" "),i("div",{staticClass:"btn-box"},[i("cartBtn",{attrs:{bgcolor:t.bgcolor,"is-pack":!1,"data-id":3914,"is-split":0,"product-num":t.proNum2,"med-num":10}})],1)]),t._v(" "),i("div",{staticClass:"good-pills"},[t._m(3),t._v(" "),i("div",{staticClass:"btn-box"},[i("cartBtn",{attrs:{bgcolor:t.bgcolor,"is-pack":!1,"data-id":1383,"is-split":0,"product-num":t.proNum3,"med-num":10}})],1)])])}),s,!1,null,"daba19d0",null).exports}}]);
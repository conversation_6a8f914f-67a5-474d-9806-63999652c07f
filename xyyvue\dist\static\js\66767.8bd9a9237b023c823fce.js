(self.webpackChunkvuetest=self.webpackChunkvuetest||[]).push([[66767],{70288:function(t,e,s){t.exports=s.p+"static/img/banner.8be41f1.jpg"},64959:function(t,e,s){t.exports=s.p+"static/img/forty.ff362d3.jpg"},75818:function(t,e,s){t.exports=s.p+"static/img/hot.5409d3f.jpg"},43831:function(t,e,s){t.exports=s.p+"static/img/recom.c001783.jpg"},20734:function(t,e,s){t.exports=s.p+"static/img/recom01.b56c935.jpg"},21942:function(t,e,s){t.exports=s.p+"static/img/recom02.8e665a8.jpg"},49984:function(t,e,s){t.exports=s.p+"static/img/recom03.ead3865.jpg"},39335:function(t,e,s){t.exports=s.p+"static/img/recom04.3735571.jpg"},83982:function(t,e,s){t.exports=s.p+"static/img/twenty.5e90f92.jpg"},75428:function(t,e,s){"use strict";s.r(e),s.d(e,{default:function(){return r}});var a=[function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"banner"},[e("img",{attrs:{src:s(70288),alt:""}})])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("img",{attrs:{src:s(43831),alt:""}})])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("img",{attrs:{src:s(75818),alt:""}})])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("img",{attrs:{src:s(83982),alt:""}})])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("img",{attrs:{src:s(64959),alt:""}})])}],i=s(24388),c=s(59502),o={data:function(){return{isCur:"recom-content",voucher1:!1,voucher2:!1,istabfixed:!1,hotData:[],twentyData:[],fortyData:[]}},methods:{getDataList:function(t,e){var s=this;this.putRequest("post","/app/layout/initExhibitionModulePage?sort=1",{merchantId:this.merchantId,limit:1e3,offset:0,exhibitionId:t}).then((function(t){"success"==t.data.status&&(s[e]=t.data.data.rows,s[e].licenseStatus=t.data.data.licenseStatus),s.$nextTick((function(){c.Z.$emit("changeloading",!1)}))})).catch((function(t){}))},moveEvent:function(){var t=document.querySelector("#app").scrollTop,e=document.querySelector(".tabs-box").offsetTop,s=document.querySelector(".tabs-box").offsetHeight;this.istabfixed=t>=e;var a=this.$refs.recom.offsetTop,i=this.$refs.hot.offsetTop,c=this.$refs.twenty.offsetTop,o=this.$refs.forty.offsetTop;t>=a-s&&t<i-s?this.isCur="recom-content":t>=i-s&&t<c-s?this.isCur="hot-content":t>=c-s&&t<o-s?this.isCur="twenty-content":t>=o-s&&(this.isCur="forty-content")},tabitemclick:function(t){this.istabfixed=!0;var e=t.currentTarget.getAttribute("contClass");this.isCur=e;var s=document.querySelector("."+e).offsetTop,a=document.querySelector(".tabs-box").offsetHeight;document.querySelector("#app").scrollTop=s-a;document.querySelector("#medical").scrollTop}},mounted:function(){this.getDataList("ZS201809051006451745","hotData"),this.getDataList("ZS201809051007024445","twentyData"),this.getDataList("ZS201809051007342631","fortyData")},components:{temprow:i.Z},activated:function(){this.setAppTitle("医疗器械")}},r=(0,s(51900).Z)(o,(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{attrs:{id:"medical"},on:{touchmove:t.moveEvent}},[t._m(0),t._v(" "),a("div",{staticClass:"tabs-box"},[a("div",{staticClass:"tab-list",class:{tabfiexd:t.istabfixed}},[a("div",{staticClass:"tab-item",class:{tabclickcolor:"recom-content"==t.isCur},attrs:{contClass:"recom-content"},on:{click:t.tabitemclick}},[t._v("厂家推荐")]),t._v(" "),a("div",{staticClass:"tab-item",class:{tabclickcolor:"hot-content"==t.isCur},attrs:{contClass:"hot-content"},on:{click:t.tabitemclick}},[t._v("热销单品")]),t._v(" "),a("div",{staticClass:"tab-item",class:{tabclickcolor:"twenty-content"==t.isCur},attrs:{contClass:"twenty-content"},on:{click:t.tabitemclick}},[t._v("满200减20")]),t._v(" "),a("div",{staticClass:"tab-item",class:{tabclickcolor:"forty-content"==t.isCur},attrs:{contClass:"forty-content"},on:{click:t.tabitemclick}},[t._v("满200减40")])])]),t._v(" "),a("div",{ref:"recom",staticClass:"recom-content"},[t._m(1),t._v(" "),a("div",{staticClass:"recom"},[a("router-link",{attrs:{to:"/temprowpage/ZS201809051004044788/fff/2a65ff/239f3bdd-a57c-4e08-a153-8354033b0910?ybm_title=金华景迪医疗"}},[a("img",{attrs:{src:s(20734),alt:""}})]),t._v(" "),a("router-link",{attrs:{to:"/temprowpage/ZS201809051006189441/fff/f42141/5f67613f-0764-4fbc-bec7-20d4896aec2c?ybm_title=青岛盛久医疗"}},[a("img",{attrs:{src:s(21942),alt:""}})]),t._v(" "),a("router-link",{attrs:{to:"/temprowpage/ZS201809051005043710/fff/ff8500/4abc2d7b-3487-4aea-8079-8920efafed9c?ybm_title=山东名德医疗"}},[a("img",{attrs:{src:s(49984),alt:""}})]),t._v(" "),a("router-link",{attrs:{to:"/temprowpage/ZS201809051004363015/fff/a348ed/d0a6b81d-3bd0-4a5e-803a-c65efd57a2ad?ybm_title=安徽可赛克医疗"}},[a("img",{attrs:{src:s(39335),alt:""}})])],1)]),t._v(" "),a("div",{ref:"hot",staticClass:"hot-content"},[t._m(2),t._v(" "),a("temprow",{attrs:{"goods-data":t.hotData,"license-status":t.hotData.licenseStatus||0}})],1),t._v(" "),a("div",{ref:"twenty",staticClass:"twenty-content"},[t._m(3),t._v(" "),a("temprow",{attrs:{"goods-data":t.twentyData,"license-status":t.twentyData.licenseStatus||0}})],1),t._v(" "),a("div",{ref:"forty",staticClass:"forty-content"},[t._m(4),t._v(" "),a("temprow",{attrs:{"goods-data":t.fortyData,"license-status":t.fortyData.licenseStatus||0}})],1)])}),a,!1,null,"f1369cd0",null).exports}}]);